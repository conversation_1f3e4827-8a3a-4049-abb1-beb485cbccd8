# MostlySecure Security Scanner

**Mostly Secure, Completely Confident** - Advanced Security Scanning Platform

MostlySecure is a comprehensive security scanning platform that helps developers and security teams identify vulnerabilities in web applications, APIs, databases, and cloud infrastructure.

## Features

### 🔍 **Comprehensive Scanning Engines**
- **API Endpoint Scanner**: Discovers and tests REST/GraphQL endpoints for authentication bypass, injection vulnerabilities, and misconfigurations
- **Database Security Checker**: Tests for SQL injection, Row-Level Security (RLS), privilege escalation, and information disclosure
- **Secrets Scanner**: Detects exposed API keys, credentials, tokens, and high-entropy strings in code and configurations
- **Firebase Security Auditor**: Analyzes Firebase/Firestore rules, authentication, storage, and Cloud Functions security
- **Webhook Security Validator**: Tests webhook endpoints for SSL/TLS security, authentication, and input validation

### 🚀 **Production-Ready Architecture**
- **FastAPI Backend**: High-performance async API with automatic OpenAPI documentation
- **PostgreSQL Database**: Reliable data storage with async connections
- **Redis**: Caching and message broker for background tasks
- **Celery**: Distributed task queue for async scanning
- **Docker**: Containerized deployment with multi-stage builds
- **Nginx**: Reverse proxy with rate limiting and security headers

### 🔐 **Security & Authentication**
- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: Per-user and IP-based rate limiting
- **Input Validation**: Comprehensive request validation
- **Security Headers**: OWASP-recommended security headers
- **Audit Logging**: Structured logging for security events

### 📊 **Advanced Reporting**
- **Vulnerability Management**: Categorize, prioritize, and track vulnerabilities
- **CVSS Scoring**: Industry-standard vulnerability scoring
- **OWASP Mapping**: Map findings to OWASP Top 10 categories
- **Export Formats**: JSON, PDF, and HTML reports
- **Dashboard Analytics**: Scan summaries and vulnerability trends

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Git

### 1. Clone the Repository
```bash
git clone https://github.com/your-org/mostlysecure-scanner.git
cd mostlysecure-scanner
```

### 2. Set Up Environment
```bash
# Copy environment template
cp .env.example .env

# Edit configuration (required)
nano .env
```

### 3. Start the Platform
```bash
# Build and start all services
make up

# Or using docker-compose directly
docker-compose up -d
```

### 4. Initialize Database
```bash
# Run database migrations
make migrate

# Check service health
make health
```

### 5. Access the Platform
- **API Documentation**: http://localhost:8000/docs
- **API Base URL**: http://localhost:8000/api/v1/
- **Flower (Celery Monitor)**: http://localhost:5555/

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | Required |
| `REDIS_URL` | Redis connection string | `redis://localhost:6379` |
| `SECRET_KEY` | JWT signing secret | Required |
| `DEBUG` | Enable debug mode | `false` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `RATE_LIMIT_PER_MINUTE` | API rate limit | `60` |
| `MAX_CONCURRENT_SCANS` | Max concurrent scans per user | `5` |
| `SCAN_TIMEOUT_SECONDS` | Scan timeout | `300` |

### Security Configuration

```bash
# Generate a secure secret key
python -c "import secrets; print(secrets.token_urlsafe(32))"

# Set strong database password
DB_PASSWORD=your-secure-password

# Configure allowed hosts
ALLOWED_HOSTS=["yourdomain.com", "api.yourdomain.com"]
```

## API Usage

### Authentication

1. **Register a new user**:
```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "SecurePass123!",
    "full_name": "Test User"
  }'
```

2. **Login to get access token**:
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "SecurePass123!"
  }'
```

### Start a Security Scan

```bash
curl -X POST "http://localhost:8000/api/v1/scans/" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://api.example.com",
    "scan_type": "comprehensive",
    "scan_name": "My First Scan",
    "scan_description": "Testing API security"
  }'
```

### Get Scan Results

```bash
# Get scan status
curl -X GET "http://localhost:8000/api/v1/scans/1/status" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Get scan results
curl -X GET "http://localhost:8000/api/v1/scans/1" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Get vulnerabilities
curl -X GET "http://localhost:8000/api/v1/scans/1/vulnerabilities" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Scanning Types

### 1. API Endpoints (`api_endpoints`)
- Discovers API endpoints through various methods
- Tests for authentication bypass
- Checks HTTP method security
- Performs parameter fuzzing for injection vulnerabilities
- Validates CORS configuration

### 2. Database Security (`database_security`)
- Tests database connection security
- Validates Row-Level Security (RLS) implementation
- Checks for privilege escalation vulnerabilities
- Tests information disclosure

### 3. Secrets Detection (`secrets`)
- Scans for exposed API keys and tokens
- Detects database connection strings
- Finds hardcoded passwords
- Analyzes Git history for leaked credentials
- Validates secret authenticity

### 4. Firebase Security (`firebase`)
- Analyzes Firestore security rules
- Tests Realtime Database permissions
- Checks Firebase Storage configuration
- Validates Authentication settings
- Tests Cloud Functions security

### 5. Webhook Security (`webhooks`)
- Tests SSL/TLS configuration
- Validates webhook authentication
- Checks for input validation vulnerabilities
- Tests rate limiting implementation
- Analyzes replay attack protection

### 6. Comprehensive (`comprehensive`)
- Runs all available scanners
- Provides complete security assessment
- Generates detailed vulnerability report

## Development

### Setting Up Development Environment

```bash
# Install dependencies
pip install -r requirements/dev.txt

# Set up pre-commit hooks
pre-commit install

# Run tests
pytest tests/

# Run linting
flake8 app/
mypy app/

# Format code
black app/
```

### Adding New Scanners

1. Create a new scanner class inheriting from `BaseScanner`
2. Implement the `scan` method
3. Add scanner to the factory in `services/tasks.py`
4. Update the `ScanType` enum in schemas
5. Add tests for the new scanner

### Database Migrations

```bash
# Create a new migration
alembic revision --autogenerate -m "Add new feature"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

## Deployment

### Production Deployment

1. **Update configuration**:
```bash
# Set production environment variables
export DEBUG=false
export LOG_LEVEL=WARNING
export SECRET_KEY=your-production-secret
export DATABASE_URL=***********************************/mostlysecure
```

2. **Deploy with Docker**:
```bash
# Build production images
make prod-build

# Start production environment
make prod-up
```

3. **Set up SSL/TLS**:
- Configure SSL certificates in Nginx
- Update security headers
- Enable HSTS

### Monitoring and Maintenance

```bash
# Monitor logs
make logs

# Check service health
make health

# Backup database
make backup-db

# Clean up old scans (automated via Celery Beat)
docker-compose exec worker celery -A app.services.celery_app call app.services.tasks.cleanup_old_scans
```

## Security Considerations

### Infrastructure Security
- Use strong, unique passwords for all services
- Implement network segmentation
- Regular security updates
- Monitor for suspicious activity

### Application Security
- Validate all user inputs
- Implement proper rate limiting
- Use HTTPS in production
- Regular dependency updates

### Data Protection
- Encrypt sensitive data at rest
- Secure API keys and secrets
- Implement proper access controls
- Regular security audits

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-scanner`
3. Make your changes and add tests
4. Run the test suite: `make test`
5. Submit a pull request

### Code Style
- Follow PEP 8 guidelines
- Use type hints
- Write comprehensive docstrings
- Add unit tests for new features

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- **Documentation**: [docs.mostlysecure.com](https://docs.mostlysecure.com)
- **Issues**: [GitHub Issues](https://github.com/your-org/mostlysecure-scanner/issues)
- **Security**: <EMAIL>
- **Community**: [Discord](https://discord.gg/mostlysecure)

---

**MostlySecure Security Scanner** - Empowering developers to ship secure applications with confidence.