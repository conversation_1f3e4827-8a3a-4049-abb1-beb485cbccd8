#!/usr/bin/env python3
"""
Test script to create a scan and verify notifications are working.
"""
import requests
import time
import json

# API Configuration
API_BASE = "http://localhost:8000/api/v1"
USERNAME = "ogkais"
PASSWORD = "Kreatorn01."

def login():
    """Login and get access token."""
    response = requests.post(f"{API_BASE}/auth/login", json={
        "username": USERNAME,
        "password": PASSWORD
    })

    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"Login failed: {response.status_code} - {response.text}")
        return None

def create_scan(token):
    """Create a new scan."""
    headers = {"Authorization": f"Bearer {token}"}
    
    scan_data = {
        "target_url": "https://httpbin.org/get",
        "scan_type": "secrets",
        "scan_name": "Notification Test - After Rebuild",
        "scan_description": "Testing notifications after rebuilding containers",
        "max_depth": 1,
        "timeout_seconds": 30,
        "concurrent_requests": 1
    }
    
    response = requests.post(f"{API_BASE}/scans/", json=scan_data, headers=headers)

    if response.status_code in [200, 201]:
        scan = response.json()
        print(f"✅ Scan created successfully: ID {scan['id']}")
        return scan
    else:
        print(f"❌ Failed to create scan: {response.status_code} - {response.text}")
        return None

def check_scan_status(token, scan_id):
    """Check scan status."""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{API_BASE}/scans/{scan_id}", headers=headers)
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"❌ Failed to get scan status: {response.status_code} - {response.text}")
        return None

def check_notifications(token):
    """Check for notifications."""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{API_BASE}/notifications/", headers=headers)
    
    if response.status_code == 200:
        notifications = response.json()
        print(f"📬 Found {len(notifications)} notifications:")
        for notif in notifications:
            print(f"  - {notif['type']}: {notif['title']}")
            print(f"    {notif['message']}")
            print(f"    Created: {notif['created_at']}")
            print()
        return notifications
    else:
        print(f"❌ Failed to get notifications: {response.status_code} - {response.text}")
        return []

def main():
    print("🚀 Testing VibeRush Notification System")
    print("=" * 50)
    
    # Login
    print("1. Logging in...")
    token = login()
    if not token:
        return
    
    print("✅ Login successful")
    
    # Check existing notifications
    print("\n2. Checking existing notifications...")
    existing_notifications = check_notifications(token)
    
    # Create scan
    print("\n3. Creating test scan...")
    scan = create_scan(token)
    if not scan:
        return
    
    scan_id = scan["id"]
    
    # Monitor scan progress
    print(f"\n4. Monitoring scan {scan_id}...")
    max_wait = 120  # 2 minutes max
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        scan_status = check_scan_status(token, scan_id)
        if not scan_status:
            break
            
        status = scan_status["status"]
        progress = scan_status.get("progress_percentage", 0)
        
        print(f"   Status: {status} ({progress}%)")
        
        if status in ["COMPLETED", "FAILED"]:
            print(f"✅ Scan {status.lower()}")
            break
            
        time.sleep(5)
    else:
        print("⏰ Scan timeout - continuing to check notifications")
    
    # Wait a bit for notification to be created
    print("\n5. Waiting for notification creation...")
    time.sleep(3)
    
    # Check for new notifications
    print("\n6. Checking for new notifications...")
    new_notifications = check_notifications(token)
    
    # Compare notifications
    if len(new_notifications) > len(existing_notifications):
        print("🎉 SUCCESS! New notification(s) created!")
        new_count = len(new_notifications) - len(existing_notifications)
        print(f"   {new_count} new notification(s) found")
    else:
        print("❌ FAILED! No new notifications created")
        print("   This indicates the notification system is not working properly")
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    main()
