#!/bin/bash
set -e

# Wait for database to be ready
echo "Waiting for database..."
./scripts/wait-for-it.sh db:5432 --timeout=60 --strict -- echo "Database is ready!"

# Wait for Redis to be ready
echo "Waiting for Redis..."
./scripts/wait-for-it.sh redis:6379 --timeout=60 --strict -- echo "Redis is ready!"

# Run database migrations
echo "Running database migrations..."
alembic upgrade head

# Create default superuser if it doesn't exist
echo "Creating default superuser..."
python -c "
import asyncio
from app.core.database import AsyncSessionLocal
from app.models.user import User
from app.core.security import get_password_hash, generate_api_key
from sqlalchemy import select

async def create_superuser():
    async with AsyncSessionLocal() as db:
        result = await db.execute(select(User).where(User.is_superuser == True))
        if not result.scalar_one_or_none():
            superuser = User(
                email='<EMAIL>',
                username='admin',
                full_name='MostlySecure Administrator',
                hashed_password=get_password_hash('admin123!'),
                is_superuser=True,
                is_active=True,
                api_key=generate_api_key()
            )
            db.add(superuser)
            await db.commit()
            print('Default superuser created: <EMAIL> / admin123!')
        else:
            print('Superuser already exists')

asyncio.run(create_superuser())
"

# Start the application
echo "Starting MostlySecure Security Scanner..."
exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 1