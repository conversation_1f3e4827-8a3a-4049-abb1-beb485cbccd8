#!/usr/bin/env python3
"""
Test the API to see if security_grade is returned
"""

import requests
import json

# Test the scans endpoint
try:
    headers = {'Authorization': 'Bearer ogkais Adhbgy07.'}
    response = requests.get('http://localhost:8000/api/v1/scans/?limit=3', headers=headers)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        scans = response.json()
        print(f"Number of scans returned: {len(scans)}")
        
        if scans:
            print("\nFirst scan data:")
            first_scan = scans[0]
            print(json.dumps(first_scan, indent=2))
            
            # Check if security_grade is present
            if 'security_grade' in first_scan:
                print(f"\n✅ security_grade field found: {first_scan['security_grade']}")
            else:
                print("\n❌ security_grade field NOT found")
                print("Available fields:", list(first_scan.keys()))
        else:
            print("No scans returned")
    else:
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"Error: {e}")
