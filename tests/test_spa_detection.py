"""
Test SPA (Single Page Application) detection and false positive prevention.
This ensures our scanner doesn't flag SPA fallback responses as vulnerabilities.
"""

import pytest
import httpx
from unittest.mock import Mock, AsyncMock
from app.scanners.secrets_scanner import SecretsScanner


class TestSPADetection:
    """Test suite for SPA detection and false positive prevention."""

    @pytest.fixture
    def scanner(self):
        """Create a scanner instance for testing."""
        return SecretsScanner()

    @pytest.fixture
    def spa_html_response(self):
        """Mock SPA HTML response that gets returned for all routes."""
        return """<!doctype html>
<html lang="tr">
<head>
    <meta charset="utf-8"/>
    <title>Sanalyapi - Mimari <PERSON>y <PERSON> Dönüşüm Araçları</title>
    <script defer="defer" src="/static/js/main.9ae8a2a4.js"></script>
</head>
<body>
    <div id="root"></div>
</body>
</html>"""

    @pytest.fixture
    def valid_json_response(self):
        """Mock valid JSON configuration file."""
        return '{"api_key": "sk-1234567890abcdef", "database_url": "****************************"}'

    @pytest.fixture
    def valid_env_response(self):
        """Mock valid .env file."""
        return """# Database Configuration
DATABASE_URL=postgres://user:password@localhost/myapp
API_KEY=sk-1234567890abcdef
DEBUG=true
PORT=3000"""

    def test_spa_html_detection_for_json_file(self, scanner, spa_html_response):
        """Test that SPA HTML response is detected when requesting JSON file."""
        # Mock response that returns HTML for config.json request
        response = Mock()
        response.headers = {
            'content-type': 'text/html; charset=utf-8',
            'content-disposition': 'inline; filename="index.html"'
        }
        response.text = spa_html_response

        # Should return False (invalid) because we requested JSON but got HTML
        assert not scanner._is_valid_file_response(response, 'config.json', 'https://example.com')

    def test_spa_html_detection_for_env_file(self, scanner, spa_html_response):
        """Test that SPA HTML response is detected when requesting .env file."""
        response = Mock()
        response.headers = {
            'content-type': 'text/html; charset=utf-8',
            'content-disposition': 'inline; filename="index.html"'
        }
        response.text = spa_html_response

        # Should return False (invalid) because we requested .env but got HTML
        assert not scanner._is_valid_file_response(response, '.env.development', 'https://example.com')

    def test_valid_json_file_detection(self, scanner, valid_json_response):
        """Test that valid JSON files are correctly identified."""
        response = Mock()
        response.headers = {
            'content-type': 'application/json',
        }
        response.text = valid_json_response

        # Should return True (valid) because we got actual JSON
        assert scanner._is_valid_file_response(response, 'config.json', 'https://example.com')

    def test_valid_env_file_detection(self, scanner, valid_env_response):
        """Test that valid .env files are correctly identified."""
        response = Mock()
        response.headers = {
            'content-type': 'text/plain',
        }
        response.text = valid_env_response

        # Should return True (valid) because we got actual env file format
        assert scanner._is_valid_file_response(response, '.env', 'https://example.com')

    def test_content_hash_duplicate_detection(self, scanner, spa_html_response):
        """Test that duplicate content is detected across multiple requests."""
        response = Mock()
        response.headers = {'content-type': 'text/html'}
        response.text = spa_html_response

        # First request should pass (we haven't seen this content before)
        result1 = scanner._is_valid_file_response(response, 'config.json', 'https://example.com')
        
        # Second request with same content should fail (duplicate content detected)
        result2 = scanner._is_valid_file_response(response, 'secrets.json', 'https://example.com')
        
        # First might pass, but second should definitely fail due to duplicate detection
        assert not result2

    def test_content_size_duplicate_detection(self, scanner, spa_html_response):
        """Test that duplicate content sizes are detected."""
        response = Mock()
        response.headers = {'content-type': 'text/html'}
        response.text = spa_html_response

        # Make multiple requests with same content size
        for i in range(4):
            result = scanner._is_valid_file_response(response, f'file{i}.json', 'https://example.com')
            if i >= 2:  # After 3rd occurrence, should start failing
                assert not result

    def test_framework_indicator_detection(self, scanner):
        """Test that SPA framework indicators are detected."""
        react_content = """<!DOCTYPE html>
<html>
<head><title>React App</title></head>
<body>
    <div id="root"></div>
    <script>window.React = {};</script>
</body>
</html>"""
        
        response = Mock()
        response.headers = {'content-type': 'text/html'}
        response.text = react_content

        # Should fail because content contains React indicators
        assert not scanner._is_valid_file_response(response, 'config.json', 'https://example.com')

    def test_invalid_json_detection(self, scanner):
        """Test that invalid JSON is detected when JSON is expected."""
        invalid_json = "This is not JSON content"
        
        response = Mock()
        response.headers = {'content-type': 'application/json'}
        response.text = invalid_json

        # Should fail because content is not valid JSON
        assert not scanner._is_valid_file_response(response, 'config.json', 'https://example.com')

    def test_invalid_env_format_detection(self, scanner, spa_html_response):
        """Test that invalid .env format is detected."""
        response = Mock()
        response.headers = {'content-type': 'text/plain'}
        response.text = spa_html_response  # HTML content when .env expected

        # Should fail because content doesn't match .env format
        assert not scanner._is_valid_file_response(response, '.env', 'https://example.com')

    def test_sql_file_validation(self, scanner):
        """Test that SQL files are properly validated."""
        valid_sql = "SELECT * FROM users; CREATE TABLE test (id INT);"
        invalid_sql = "<html><body>Not SQL</body></html>"

        # Valid SQL should pass
        response1 = Mock()
        response1.headers = {'content-type': 'text/plain'}
        response1.text = valid_sql
        assert scanner._is_valid_file_response(response1, 'backup.sql', 'https://example.com')

        # Invalid SQL should fail
        response2 = Mock()
        response2.headers = {'content-type': 'text/plain'}
        response2.text = invalid_sql
        assert not scanner._is_valid_file_response(response2, 'backup.sql', 'https://example.com')

    def test_yaml_file_validation(self, scanner):
        """Test that YAML files are properly validated."""
        valid_yaml = "database:\n  host: localhost\n  port: 5432"
        invalid_yaml = "<html><script>alert('test')</script></html>"

        # Valid YAML should pass
        response1 = Mock()
        response1.headers = {'content-type': 'text/yaml'}
        response1.text = valid_yaml
        assert scanner._is_valid_file_response(response1, 'config.yml', 'https://example.com')

        # Invalid YAML (contains HTML) should fail
        response2 = Mock()
        response2.headers = {'content-type': 'text/yaml'}
        response2.text = invalid_yaml
        assert not scanner._is_valid_file_response(response2, 'config.yml', 'https://example.com')


@pytest.mark.asyncio
class TestSPAIntegration:
    """Integration tests for SPA detection in the full scanner."""

    @pytest.fixture
    def scanner(self):
        return SecretsScanner()

    async def test_spa_false_positive_prevention(self, scanner):
        """Test that SPA responses don't generate false positive vulnerabilities."""
        spa_html = """<!doctype html>
<html><head><title>SPA App</title></head>
<body><div id="root"></div></body></html>"""

        # Mock client that returns SPA HTML for all requests
        mock_client = AsyncMock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {
            'content-type': 'text/html; charset=utf-8',
            'content-disposition': 'inline; filename="index.html"'
        }
        mock_response.text = spa_html
        mock_client.get.return_value = mock_response

        # Scan common files - should not find vulnerabilities due to SPA detection
        vulnerabilities = await scanner._scan_common_files(mock_client, 'https://spa-app.com')

        # Should be empty because all responses are SPA fallbacks
        assert len(vulnerabilities) == 0

    async def test_real_vulnerability_detection_still_works(self, scanner):
        """Test that real vulnerabilities are still detected when files are valid."""
        real_config = '{"api_key": "sk-live_1234567890abcdef", "secret": "super_secret_key"}'

        # Mock client that returns real config file
        mock_client = AsyncMock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {'content-type': 'application/json'}
        mock_response.text = real_config
        mock_client.get.return_value = mock_response

        # Scan should find vulnerabilities in real config file
        vulnerabilities = await scanner._scan_common_files(mock_client, 'https://vulnerable-app.com')

        # Should find vulnerabilities because this is a real config file with secrets
        assert len(vulnerabilities) > 0
        
        # Check that we found the API key
        api_key_found = any('api_key' in vuln.get('title', '').lower() or 
                           'sk-live_' in vuln.get('payload', '') 
                           for vuln in vulnerabilities)
        assert api_key_found
