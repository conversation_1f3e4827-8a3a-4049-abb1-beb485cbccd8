{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci", "test:integration": "jest --testPathPattern=__tests__/integration", "test:api": "jest --testPathPattern=__tests__/api"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.1", "@tanstack/react-query": "^5.80.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.18.1", "lucide-react": "^0.515.0", "next": "15.3.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tanstack/react-query-devtools": "^5.80.7", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^15.0.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@swc/core": "^1.3.100", "@swc/jest": "^0.2.29", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.5", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "node-fetch": "^2.7.0", "tailwindcss": "^4", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.4", "typescript": "^5"}}