'use client';

import { useEffect, useRef, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Scan, ScanStatus, ScanType } from '@/types';
import { apiClient } from '@/lib/api';
import { toast } from 'sonner';

interface UseRealTimeScansOptions {
  statusFilter?: ScanStatus | 'all';
  typeFilter?: ScanType | 'all';
  onScanCompleted?: (scan: unknown) => void;
  onScanFailed?: (scan: unknown) => void;
  onScanStarted?: (scan: unknown) => void;
}

export function useRealTimeScans(options: UseRealTimeScansOptions = {}) {
  const { statusFilter, typeFilter, onScanCompleted, onScanFailed, onScanStarted } = options;
  const queryClient = useQueryClient();
  const previousScansRef = useRef<Scan[]>([]);
  const notificationShownRef = useRef<Set<number>>(new Set());

  // Main scans query with aggressive polling for running scans
  const {
    data: scans,
    isLoading,
    refetch,
    isRefetching
  } = useQuery({
    queryKey: ['scans', statusFilter, typeFilter],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (statusFilter && statusFilter !== 'all') params.append('status', statusFilter);
      if (typeFilter && typeFilter !== 'all') params.append('scan_type', typeFilter);
      params.append('limit', '100');
      
      const response = await fetch(`/api/scans?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch scans');
      }
      return response.json();
    },
    refetchInterval: (data) => {
      // More aggressive polling strategy
      if (!Array.isArray(data)) return 30000; // 30s if no data
      
      const hasRunningScans = data.some(scan => 
        scan.status === ScanStatus.RUNNING || scan.status === ScanStatus.PENDING
      );
      
      if (hasRunningScans) {
        return 3000; // 3 seconds for running scans
      }
      
      // Check if any scans completed in the last 5 minutes
      const recentlyCompleted = data.some(scan => {
        if (scan.completed_at) {
          const completedTime = new Date(scan.completed_at).getTime();
          const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
          return completedTime > fiveMinutesAgo;
        }
        return false;
      });
      
      return recentlyCompleted ? 10000 : 30000; // 10s if recently completed, 30s otherwise
    },
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    staleTime: 0, // Always consider data stale for real-time updates
  });

  // Detect scan status changes and trigger callbacks
  useEffect(() => {
    if (!scans || !Array.isArray(scans)) return;

    const previousScans = previousScansRef.current;
    
    if (previousScans.length > 0) {
      scans.forEach(currentScan => {
        const previousScan = previousScans.find(prev => prev.id === currentScan.id);
        
        if (previousScan && previousScan.status !== currentScan.status) {
          // Status changed - trigger appropriate callback and notification
          switch (currentScan.status) {
            case ScanStatus.COMPLETED:
              if (!notificationShownRef.current.has(currentScan.id)) {
                toast.success(
                  `Scan "${currentScan.scan_name || 'Unnamed'}" completed successfully!`,
                  {
                    description: `Found ${currentScan.vulnerabilities_count || 0} vulnerabilities`,
                    duration: 5000,
                  }
                );
                notificationShownRef.current.add(currentScan.id);
                onScanCompleted?.(currentScan);
              }
              break;
              
            case ScanStatus.FAILED:
              if (!notificationShownRef.current.has(currentScan.id)) {
                toast.error(
                  `Scan "${currentScan.scan_name || 'Unnamed'}" failed`,
                  {
                    description: currentScan.error_message || 'Unknown error occurred',
                    duration: 7000,
                  }
                );
                notificationShownRef.current.add(currentScan.id);
                onScanFailed?.(currentScan);
              }
              break;
              
            case ScanStatus.RUNNING:
              if (previousScan.status === ScanStatus.PENDING) {
                toast.info(
                  `Scan "${currentScan.scan_name || 'Unnamed'}" started`,
                  {
                    description: 'Scanning in progress...',
                    duration: 3000,
                  }
                );
                onScanStarted?.(currentScan);
              }
              break;
          }
        }
      });
      
      // Check for new scans
      const newScans = scans.filter(scan => 
        !previousScans.some(prev => prev.id === scan.id)
      );
      
      newScans.forEach(newScan => {
        if (newScan.status === ScanStatus.RUNNING) {
          toast.info(
            `New scan "${newScan.scan_name || 'Unnamed'}" started`,
            {
              description: 'Scanning in progress...',
              duration: 3000,
            }
          );
          onScanStarted?.(newScan);
        }
      });
    }
    
    previousScansRef.current = scans;
  }, [scans, onScanCompleted, onScanFailed, onScanStarted]);

  // Force refresh function
  const forceRefresh = useCallback(() => {
    // Invalidate all scan-related queries
    queryClient.invalidateQueries({ queryKey: ['scans'] });
    queryClient.invalidateQueries({ queryKey: ['scan-summary'] });
    queryClient.invalidateQueries({ queryKey: ['vulnerability-summary'] });
    
    // Also refetch notifications
    queryClient.invalidateQueries({ queryKey: ['notifications'] });
    queryClient.invalidateQueries({ queryKey: ['notification-count'] });
    
    return refetch();
  }, [queryClient, refetch]);

  // Auto-refresh when page becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        forceRefresh();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [forceRefresh]);

  // Cleanup notification tracking when component unmounts
  useEffect(() => {
    const notificationRef = notificationShownRef.current;
    return () => {
      notificationRef.clear();
    };
  }, []);

  return {
    scans,
    isLoading,
    isRefetching,
    refetch: forceRefresh,
    hasRunningScans: scans ? scans.some((scan: Scan) => 
      scan.status === ScanStatus.RUNNING || scan.status === ScanStatus.PENDING
    ) : false,
  };
}

// Hook for individual scan real-time updates
export function useRealTimeScan(scanId: string) {
  const queryClient = useQueryClient();
  const previousStatusRef = useRef<ScanStatus | null>(null);
  const notificationShownRef = useRef(false);

  const {
    data: scan,
    isLoading,
    refetch
  } = useQuery({
    queryKey: ['scan', scanId],
    queryFn: () => apiClient.getScan(scanId),
    refetchInterval: 3000, // Poll every 3 seconds
    refetchOnWindowFocus: true,
    staleTime: 0,
  });

  // Handle status changes
  useEffect(() => {
    if (!scan) return;

    const previousStatus = previousStatusRef.current;
    
    if (previousStatus && previousStatus !== scan.status) {
      switch (scan.status) {
        case ScanStatus.COMPLETED:
          if (!notificationShownRef.current) {
            toast.success(
              'Scan completed successfully!',
              {
                description: `Found ${scan.vulnerabilities_count || 0} vulnerabilities`,
                duration: 5000,
              }
            );
            notificationShownRef.current = true;
            
            // Refresh vulnerabilities data
            queryClient.invalidateQueries({ queryKey: ['vulnerabilities', scanId] });
          }
          break;
          
        case ScanStatus.FAILED:
          if (!notificationShownRef.current) {
            toast.error(
              'Scan failed',
              {
                description: scan.error_message || 'Unknown error occurred',
                duration: 7000,
              }
            );
            notificationShownRef.current = true;
          }
          break;
      }
    }
    
    previousStatusRef.current = scan.status;
  }, [scan, scanId, queryClient]);

  return {
    scan,
    isLoading,
    refetch,
  };
}
