import { useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useScanStore } from '@/stores/scans'
import type { Database } from '@/types/supabase'

type Scan = Database['public']['Tables']['scans']['Row']
type Vulnerability = Database['public']['Tables']['vulnerabilities']['Row']

export function useRealtimeScan(scanId: string | null) {
  const { updateScan, addVulnerability } = useScanStore()
  const supabase = createClient()

  const subscribeToScan = useCallback(() => {
    if (!scanId) return

    // Subscribe to scan updates
    const scanChannel = supabase
      .channel(`scan-${scanId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'scans',
          filter: `id=eq.${scanId}`
        },
        (payload) => {
          const updatedScan = payload.new as Scan
          updateScan(scanId, updatedScan)
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'vulnerabilities',
          filter: `scan_id=eq.${scanId}`
        },
        (payload) => {
          const newVulnerability = payload.new as Vulnerability
          addVulnerability(scanId, newVulnerability)
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(scanChannel)
    }
  }, [scanId, supabase, updateScan, addVulnerability])

  useEffect(() => {
    const unsubscribe = subscribeToScan()
    return () => {
      unsubscribe?.()
    }
  }, [subscribeToScan])
}

export function useRealtimeScans() {
  const { updateScan, removeScan } = useScanStore()
  const supabase = createClient()

  useEffect(() => {
    // Get current user
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return null

      // Subscribe to all user's scans
      const scansChannel = supabase
        .channel('user-scans')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'scans',
            filter: `user_id=eq.${user.id}`
          },
          (payload) => {
            if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
              const scan = payload.new as Scan
              updateScan(scan.id, scan)
            } else if (payload.eventType === 'DELETE') {
              const scan = payload.old as { id: string }
              removeScan(scan.id)
            }
          }
        )
        .subscribe()

      return () => {
        supabase.removeChannel(scansChannel)
      }
    }

    getCurrentUser()
  }, [supabase, updateScan, removeScan])
}