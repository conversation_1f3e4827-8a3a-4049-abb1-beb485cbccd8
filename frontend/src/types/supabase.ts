export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          username: string
          full_name: string | null
          phone: string | null
          location: string | null
          company: string | null
          avatar_url: string | null
          email_notifications: boolean
          browser_notifications: boolean
          scan_completion_alerts: boolean
          vulnerability_alerts: boolean
          weekly_reports: boolean
          api_key: string
          rate_limit_per_minute: number
          max_concurrent_scans: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          username: string
          full_name?: string | null
          phone?: string | null
          location?: string | null
          company?: string | null
          avatar_url?: string | null
          email_notifications?: boolean
          browser_notifications?: boolean
          scan_completion_alerts?: boolean
          vulnerability_alerts?: boolean
          weekly_reports?: boolean
          api_key?: string
          rate_limit_per_minute?: number
          max_concurrent_scans?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          username?: string
          full_name?: string | null
          phone?: string | null
          location?: string | null
          company?: string | null
          avatar_url?: string | null
          email_notifications?: boolean
          browser_notifications?: boolean
          scan_completion_alerts?: boolean
          vulnerability_alerts?: boolean
          weekly_reports?: boolean
          api_key?: string
          rate_limit_per_minute?: number
          max_concurrent_scans?: number
          created_at?: string
          updated_at?: string
        }
      }
      scans: {
        Row: {
          id: string
          user_id: string
          scan_name: string
          scan_description: string | null
          target_url: string
          scan_type: 'api_endpoints' | 'database_security' | 'secrets' | 'firebase' | 'webhooks' | 'comprehensive'
          status: 'pending' | 'queued' | 'running' | 'completed' | 'failed' | 'cancelled'
          progress: number
          config: Json
          vulnerabilities_count: number
          critical_count: number
          high_count: number
          medium_count: number
          low_count: number
          info_count: number
          started_at: string | null
          completed_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          scan_name: string
          scan_description?: string | null
          target_url: string
          scan_type: 'api_endpoints' | 'database_security' | 'secrets' | 'firebase' | 'webhooks' | 'comprehensive'
          status?: 'pending' | 'queued' | 'running' | 'completed' | 'failed' | 'cancelled'
          progress?: number
          config?: Json
          vulnerabilities_count?: number
          critical_count?: number
          high_count?: number
          medium_count?: number
          low_count?: number
          info_count?: number
          started_at?: string | null
          completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          scan_name?: string
          scan_description?: string | null
          target_url?: string
          scan_type?: 'api_endpoints' | 'database_security' | 'secrets' | 'firebase' | 'webhooks' | 'comprehensive'
          status?: 'pending' | 'queued' | 'running' | 'completed' | 'failed' | 'cancelled'
          progress?: number
          config?: Json
          vulnerabilities_count?: number
          critical_count?: number
          high_count?: number
          medium_count?: number
          low_count?: number
          info_count?: number
          started_at?: string | null
          completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      vulnerabilities: {
        Row: {
          id: string
          scan_id: string
          title: string
          description: string | null
          severity: 'critical' | 'high' | 'medium' | 'low' | 'info'
          cvss_score: number | null
          cvss_vector: string | null
          owasp_category: string | null
          cwe_id: string | null
          evidence: Json
          affected_component: string | null
          attack_vector: string | null
          recommendation: string | null
          reference_links: string[] | null
          is_false_positive: boolean
          is_resolved: boolean
          resolved_at: string | null
          resolved_by: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          scan_id: string
          title: string
          description?: string | null
          severity: 'critical' | 'high' | 'medium' | 'low' | 'info'
          cvss_score?: number | null
          cvss_vector?: string | null
          owasp_category?: string | null
          cwe_id?: string | null
          evidence?: Json
          affected_component?: string | null
          attack_vector?: string | null
          recommendation?: string | null
          reference_links?: string[] | null
          is_false_positive?: boolean
          is_resolved?: boolean
          resolved_at?: string | null
          resolved_by?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          scan_id?: string
          title?: string
          description?: string | null
          severity?: 'critical' | 'high' | 'medium' | 'low' | 'info'
          cvss_score?: number | null
          cvss_vector?: string | null
          owasp_category?: string | null
          cwe_id?: string | null
          evidence?: Json
          affected_component?: string | null
          attack_vector?: string | null
          recommendation?: string | null
          reference_links?: string[] | null
          is_false_positive?: boolean
          is_resolved?: boolean
          resolved_at?: string | null
          resolved_by?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string
          title: string
          message: string
          type: 'scan_complete' | 'scan_failed' | 'vulnerability_found' | 'system'
          is_read: boolean
          read_at: string | null
          scan_id: string | null
          vulnerability_id: string | null
          data: Json
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          message: string
          type: 'scan_complete' | 'scan_failed' | 'vulnerability_found' | 'system'
          is_read?: boolean
          read_at?: string | null
          scan_id?: string | null
          vulnerability_id?: string | null
          data?: Json
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          message?: string
          type?: 'scan_complete' | 'scan_failed' | 'vulnerability_found' | 'system'
          is_read?: boolean
          read_at?: string | null
          scan_id?: string | null
          vulnerability_id?: string | null
          data?: Json
          created_at?: string
        }
      }
      scan_logs: {
        Row: {
          id: string
          scan_id: string
          level: 'info' | 'warning' | 'error' | 'debug'
          message: string
          details: Json
          created_at: string
        }
        Insert: {
          id?: string
          scan_id: string
          level: 'info' | 'warning' | 'error' | 'debug'
          message: string
          details?: Json
          created_at?: string
        }
        Update: {
          id?: string
          scan_id?: string
          level?: 'info' | 'warning' | 'error' | 'debug'
          message?: string
          details?: Json
          created_at?: string
        }
      }
      api_usage: {
        Row: {
          id: string
          user_id: string
          endpoint: string
          method: string
          status_code: number | null
          response_time_ms: number | null
          ip_address: string | null
          user_agent: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          endpoint: string
          method: string
          status_code?: number | null
          response_time_ms?: number | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          endpoint?: string
          method?: string
          status_code?: number | null
          response_time_ms?: number | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      scan_summary: {
        Row: {
          id: string
          user_id: string
          scan_name: string
          scan_description: string | null
          target_url: string
          scan_type: string
          status: string
          progress: number
          config: Json
          vulnerabilities_count: number
          critical_count: number
          high_count: number
          medium_count: number
          low_count: number
          info_count: number
          started_at: string | null
          completed_at: string | null
          created_at: string
          updated_at: string
          username: string
          full_name: string | null
          total_vulnerabilities: number | null
        }
      }
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      notification_type: 'scan_complete' | 'scan_failed' | 'vulnerability_found' | 'system'
      scan_status: 'pending' | 'queued' | 'running' | 'completed' | 'failed' | 'cancelled'
      scan_type: 'api_endpoints' | 'database_security' | 'secrets' | 'firebase' | 'webhooks' | 'comprehensive'
      severity_level: 'critical' | 'high' | 'medium' | 'low' | 'info'
    }
  }
}