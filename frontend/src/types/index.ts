// User types
export interface User {
  id: number;
  email: string;
  username: string;
  full_name?: string;
  is_active: boolean;
  api_key?: string;
  rate_limit_per_minute: number;
  max_concurrent_scans: number;
  created_at: string;
  updated_at: string;
  last_login?: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  username: string;
  password: string;
  full_name: string;
}

// Scan types
export enum ScanType {
  API_ENDPOINTS = 'api_endpoints',
  DATABASE_SECURITY = 'database_security',
  SECRETS = 'secrets',
  FIREBASE = 'firebase',
  WEBHOOKS = 'webhooks',
  COMPREHENSIVE = 'comprehensive'
}

export enum ScanStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface Scan {
  id: string;
  user_id: string;
  target_url: string;
  scan_type: ScanType;
  scan_name: string;
  scan_description?: string;
  status: ScanStatus;
  progress: number;
  error_message?: string;
  vulnerabilities_count: number;
  critical_count: number;
  high_count: number;
  medium_count: number;
  low_count: number;
  info_count: number;
  config?: Record<string, any>;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  updated_at: string;
}

export interface CreateScanRequest {
  target_url: string;
  scan_type: ScanType;
  scan_name: string;
  scan_description?: string;
  max_depth?: number;
  timeout_seconds?: number;
  concurrent_requests?: number;
  custom_headers?: Record<string, string>;
}

// Vulnerability types
export enum VulnerabilitySeverity {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low'
}

export enum BusinessPriority {
  CRITICAL = 'critical',
  HIGH = 'high', 
  MEDIUM = 'medium',
  LOW = 'low',
  VERY_LOW = 'very_low'
}

export interface Vulnerability {
  id: number;
  scan_id: number;
  title: string;
  description: string;
  severity: VulnerabilitySeverity;
  category: string;
  url: string;
  method?: string;
  parameter?: string;
  payload?: string;
  evidence?: string;
  cvss_score?: number;
  cwe_id?: string;
  owasp_category?: string;
  remediation?: string;
  references: string[];
  business_priority: BusinessPriority;
  endpoint_score: number;
  is_likely_false_positive: boolean;
  false_positive_confidence: number;
  is_false_positive: boolean;
  is_resolved: boolean;
  resolution_notes?: string;
  created_at: string;
  updated_at: string;
}

// Dashboard types
export interface ScanSummary {
  total_scans: number;
  pending_scans: number;
  running_scans: number;
  completed_scans: number;
  failed_scans: number;
}

export interface VulnerabilitySummary {
  total_vulnerabilities: number;
  critical_count: number;
  high_count: number;
  medium_count: number;
  low_count: number;
  by_category: Record<string, number>;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// Error types
export interface ApiError {
  detail: string;
  status_code: number;
}
