'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Shield, Zap, Heart, ArrowRight, Github, Twitter } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { LoginForm } from './login-form';
import { RegisterForm } from './register-form';
import { ProcessSection } from '@/components/sections/process-section';
import { VulnerabilitiesSection } from '@/components/sections/vulnerabilities-section';
import { BadgeSection } from '@/components/sections/badge-section';
import { FAQSection } from '@/components/sections/faq-section';

export function LandingPage() {
  const [showLogin, setShowLogin] = useState(false);
  const [showRegister, setShowRegister] = useState(false);

  const switchToRegister = () => {
    setShowLogin(false);
    setShowRegister(true);
  };

  const switchToLogin = () => {
    setShowRegister(false);
    setShowLogin(true);
  };

  const features = [
    {
      icon: Shield,
      title: 'Advanced Security',
      description: '10 powerful scanners with 500+ test patterns and AI-powered analysis',
    },
    {
      icon: Zap,
      title: 'Lightning Fast',
      description: 'Concurrent async processing with intelligent false positive reduction',
    },
    {
      icon: Heart,
      title: 'Developer Friendly',
      description: 'Beautiful UI, comprehensive API, and detailed remediation advice',
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50">
      {/* Header */}
      <header className="relative z-10 px-4 py-6">
        <nav className="mx-auto flex max-w-7xl items-center justify-between">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center gap-2"
          >
            <div className="gradient-primary rounded-lg p-2">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              MostlySecure
            </span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center gap-4"
          >
            <Button
              variant="ghost"
              onClick={() => setShowLogin(true)}
              className="text-gray-600 hover:text-purple-600"
            >
              Sign In
            </Button>
            <Button
              onClick={() => setShowRegister(true)}
              className="gradient-primary text-white hover:opacity-90"
            >
              Get Started
            </Button>
          </motion.div>
        </nav>
      </header>

      {/* Hero Section */}
      <main className="relative">
        <div className="mx-auto max-w-7xl px-4 py-16 sm:py-24">
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <h1 className="text-4xl font-display tracking-tight text-gray-900 sm:text-6xl text-balance">
                Mostly Secure,{' '}
                <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Completely Confident
                </span>
              </h1>
            </motion.div>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600"
            >
              Advanced AI-powered security scanning that catches critical vulnerabilities before they become costly breaches. 
              Deploy with confidence.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mt-10 flex items-center justify-center gap-x-6"
            >
              <Button
                size="lg"
                onClick={() => setShowRegister(true)}
                className="gradient-primary text-white hover:opacity-90 group"
              >
                Start Scanning
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => setShowLogin(true)}
                className="border-purple-200 text-purple-600 hover:bg-purple-50"
              >
                Sign In
              </Button>
            </motion.div>
          </div>

          {/* Features */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mx-auto mt-16 max-w-5xl"
          >
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-3">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 + index * 0.1 }}
                >
                  <Card className="border-purple-100 bg-white/50 backdrop-blur-sm hover:shadow-lg transition-all duration-300 animate-float">
                    <CardHeader className="text-center">
                      <div className="mx-auto mb-4 w-12 h-12 gradient-primary rounded-lg flex items-center justify-center">
                        <feature.icon className="h-6 w-6 text-white" />
                      </div>
                      <CardTitle className="text-gray-900">{feature.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-center text-gray-600">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="mx-auto mt-16 max-w-4xl"
          >
            <div className="grid grid-cols-2 gap-8 sm:grid-cols-4">
              {[
                { label: 'Scanners', value: '10+' },
                { label: 'Test Patterns', value: '500+' },
                { label: 'Vulnerabilities', value: 'OWASP' },
                { label: 'Performance', value: 'Concurrent' },
              ].map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.8 + index * 0.1 }}
                  className="text-center"
                >
                  <div className="text-2xl font-bold text-purple-600">{stat.value}</div>
                  <div className="text-sm text-gray-600">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </main>

      {/* New Sections */}
      <ProcessSection />
      <VulnerabilitiesSection />
      <BadgeSection />
      <FAQSection />

      {/* Footer */}
      <footer className="border-t border-purple-100 bg-white/30 backdrop-blur-sm">
          <div className="mx-auto max-w-7xl px-4 py-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="gradient-primary rounded-lg p-1">
                  <Shield className="h-4 w-4 text-white" />
                </div>
                <span className="text-sm text-gray-600">
                  © 2024 MostlySecure. Made with ❤️
                </span>
              </div>
              <div className="flex items-center gap-4">
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-purple-600">
                  <Github className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-purple-600">
                  <Twitter className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </footer>

      {/* Modals */}
      <LoginForm 
        open={showLogin} 
        onOpenChange={setShowLogin}
        onSwitchToRegister={switchToRegister}
      />
      <RegisterForm 
        open={showRegister} 
        onOpenChange={setShowRegister}
        onSwitchToLogin={switchToLogin}
      />
    </div>
  );
}
