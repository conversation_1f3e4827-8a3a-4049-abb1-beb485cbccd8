import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  AlertTriangle, 
  Shield, 
  Filter, 
  Search, 
  ExternalLink,
  CheckCircle,
  XCircle,
  Eye,
  Download,
  TrendingUp,
  Star,
  Target
} from 'lucide-react';
import { Vulnerability, VulnerabilitySeverity, BusinessPriority } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { LoadingSpinner } from '@/components/shared/loading-spinner';
import { EmptyState } from '@/components/shared/empty-state';
import { VulnerabilityDetail } from './vulnerability-detail';
import { CleanSecurityReport } from './clean-security-report';
import { cn } from '@/lib/utils';
import { apiClient } from '@/lib/api';

interface VulnerabilityListProps {
  vulnerabilities?: Vulnerability[];
  isLoading?: boolean;
  scanId: number;
}


const businessPriorityConfig = {
  [BusinessPriority.CRITICAL]: {
    color: 'bg-red-100 text-red-800 border-red-200',
    icon: Star,
    label: 'Critical Business Impact',
    priority: 5,
  },
  [BusinessPriority.HIGH]: {
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    icon: TrendingUp,
    label: 'High Business Impact',
    priority: 4,
  },
  [BusinessPriority.MEDIUM]: {
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    icon: Target,
    label: 'Medium Business Impact',
    priority: 3,
  },
  [BusinessPriority.LOW]: {
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    icon: Shield,
    label: 'Low Business Impact',
    priority: 2,
  },
  [BusinessPriority.VERY_LOW]: {
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: Shield,
    label: 'Very Low Business Impact',
    priority: 1,
  },
};

const getSeverityPriority = (severity: VulnerabilitySeverity): number => {
  switch (severity) {
    case VulnerabilitySeverity.CRITICAL: return 4;
    case VulnerabilitySeverity.HIGH: return 3;
    case VulnerabilitySeverity.MEDIUM: return 2;
    case VulnerabilitySeverity.LOW: return 1;
    default: return 0;
  }
};

export function VulnerabilityList({ vulnerabilities, isLoading, scanId }: VulnerabilityListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [severityFilter, setSeverityFilter] = useState<VulnerabilitySeverity | 'all'>('all');
  const [businessPriorityFilter, setBusinessPriorityFilter] = useState<BusinessPriority | 'all'>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [selectedVulnerability, setSelectedVulnerability] = useState<Vulnerability | null>(null);

  const handleExportResults = async () => {
    try {
      const reportData = await apiClient.getScanReport(scanId, 'json');
      const blob = new Blob([JSON.stringify(reportData, null, 2)], {
        type: 'application/json'
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `vulnerabilities-report-${scanId}.json`;
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export results:', error);
      alert('Failed to export results. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading vulnerabilities..." />
      </div>
    );
  }

  if (!vulnerabilities || vulnerabilities.length === 0) {
    return <CleanSecurityReport scanId={scanId.toString()} />;
  }

  // Get unique categories for filter
  const categories = Array.from(new Set(vulnerabilities.map(v => v.category)));

  // Filter and sort vulnerabilities
  const filteredAndSortedVulnerabilities = vulnerabilities
    .filter(vuln => {
      const matchesSearch = !searchQuery || 
        vuln.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        vuln.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        vuln.url?.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesSeverity = severityFilter === 'all' || vuln.severity === severityFilter;
      const matchesBusinessPriority = businessPriorityFilter === 'all' || vuln.business_priority === businessPriorityFilter;
      const matchesCategory = categoryFilter === 'all' || vuln.category === categoryFilter;
      
      return matchesSearch && matchesSeverity && matchesBusinessPriority && matchesCategory;
    })
    .sort((a, b) => {
      // 1. Sort by business priority first (Critical -> High -> Medium -> Low -> Very Low)
      const businessPriorityA = businessPriorityConfig[a.business_priority]?.priority || 0;
      const businessPriorityB = businessPriorityConfig[b.business_priority]?.priority || 0;
      if (businessPriorityA !== businessPriorityB) {
        return businessPriorityB - businessPriorityA; // Higher priority first
      }
      
      // 2. Then by severity (Critical -> High -> Medium -> Low)
      const severityPriorityA = getSeverityPriority(a.severity);
      const severityPriorityB = getSeverityPriority(b.severity);
      if (severityPriorityA !== severityPriorityB) {
        return severityPriorityB - severityPriorityA; // Higher severity first
      }
      
      // 3. Finally by endpoint score (highest first)
      return (b.endpoint_score || 0) - (a.endpoint_score || 0);
    });

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search vulnerabilities..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex gap-3 flex-wrap">
              <Select value={severityFilter} onValueChange={(value) => setSeverityFilter(value as VulnerabilitySeverity | 'all')}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Severity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Severities</SelectItem>
                  <SelectItem value={VulnerabilitySeverity.CRITICAL}>Critical</SelectItem>
                  <SelectItem value={VulnerabilitySeverity.HIGH}>High</SelectItem>
                  <SelectItem value={VulnerabilitySeverity.MEDIUM}>Medium</SelectItem>
                  <SelectItem value={VulnerabilitySeverity.LOW}>Low</SelectItem>
                </SelectContent>
              </Select>

              <Select value={businessPriorityFilter} onValueChange={(value) => setBusinessPriorityFilter(value as BusinessPriority | 'all')}>
                <SelectTrigger className="w-[160px]">
                  <SelectValue placeholder="Business Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value={BusinessPriority.CRITICAL}>Critical</SelectItem>
                  <SelectItem value={BusinessPriority.HIGH}>High</SelectItem>
                  <SelectItem value={BusinessPriority.MEDIUM}>Medium</SelectItem>
                  <SelectItem value={BusinessPriority.LOW}>Low</SelectItem>
                  <SelectItem value={BusinessPriority.VERY_LOW}>Very Low</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600">
          Showing {filteredAndSortedVulnerabilities.length} of {vulnerabilities.length} vulnerabilities
          <span className="ml-2 text-xs text-gray-500">
            (sorted by business priority, severity, endpoint score)
          </span>
        </div>
        <Button variant="outline" size="sm" onClick={handleExportResults}>
          <Download className="mr-2 h-4 w-4" />
          Export Results
        </Button>
      </div>

      {/* Vulnerability Cards */}
      {filteredAndSortedVulnerabilities.length === 0 ? (
        <EmptyState
          icon={Filter}
          title="No vulnerabilities match your filters"
          description="Try adjusting your search criteria or filters"
        />
      ) : (
        <div className="space-y-4">
          {filteredAndSortedVulnerabilities.map((vulnerability, index) => {
            const severityConfig = {
              [VulnerabilitySeverity.CRITICAL]: {
                color: 'bg-red-100 text-red-800 border-red-200',
                bgColor: 'bg-red-50',
                textColor: 'text-red-600',
              },
              [VulnerabilitySeverity.HIGH]: {
                color: 'bg-orange-100 text-orange-800 border-orange-200',
                bgColor: 'bg-orange-50',
                textColor: 'text-orange-600',
              },
              [VulnerabilitySeverity.MEDIUM]: {
                color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
                bgColor: 'bg-yellow-50',
                textColor: 'text-yellow-600',
              },
              [VulnerabilitySeverity.LOW]: {
                color: 'bg-green-100 text-green-800 border-green-200',
                bgColor: 'bg-green-50',
                textColor: 'text-green-600',
              },
            };
            const config = severityConfig[vulnerability.severity];
            const businessPriorityDetails = businessPriorityConfig[vulnerability.business_priority];
            const BusinessPriorityIcon = businessPriorityDetails?.icon || Shield;
            
            return (
              <motion.div
                key={vulnerability.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <Card className="border-purple-100 hover:shadow-md transition-all duration-200">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3 mb-3 flex-wrap">
                          <Badge className={cn("text-sm", businessPriorityDetails?.color)}>
                            <BusinessPriorityIcon className="mr-1 h-3 w-3" />
                            {vulnerability.business_priority.toUpperCase().replace('_', ' ')} PRIORITY
                          </Badge>
                          <Badge className={cn("text-sm", config.color)}>
                            <AlertTriangle className="mr-1 h-3 w-3" />
                            {vulnerability.severity.toUpperCase()}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {vulnerability.category}
                          </Badge>
                          <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">
                            <Target className="mr-1 h-3 w-3" />
                            Score: {vulnerability.endpoint_score}
                          </Badge>
                          {vulnerability.is_resolved && (
                            <Badge className="bg-green-100 text-green-800">
                              <CheckCircle className="mr-1 h-3 w-3" />
                              Resolved
                            </Badge>
                          )}
                          {vulnerability.is_false_positive && (
                            <Badge className="bg-gray-100 text-gray-800">
                              <XCircle className="mr-1 h-3 w-3" />
                              False Positive
                            </Badge>
                          )}
                          {vulnerability.is_likely_false_positive && (
                            <Badge className="bg-yellow-100 text-yellow-800">
                              <AlertTriangle className="mr-1 h-3 w-3" />
                              Likely False Positive ({vulnerability.false_positive_confidence}%)
                            </Badge>
                          )}
                        </div>
                        
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {vulnerability.title}
                        </h3>
                        
                        <p className="text-gray-600 mb-3 line-clamp-2">
                          {vulnerability.description}
                        </p>
                        
                        {vulnerability.url && (
                          <div className="flex items-center gap-2 text-sm text-gray-500 mb-3">
                            <ExternalLink className="h-4 w-4" />
                            <span className="truncate">{vulnerability.url}</span>
                            {vulnerability.method && (
                              <>
                                <span>•</span>
                                <Badge variant="outline" className="text-xs">
                                  {vulnerability.method}
                                </Badge>
                              </>
                            )}
                          </div>
                        )}
                        
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          {vulnerability.cvss_score && (
                            <span>CVSS: {vulnerability.cvss_score}</span>
                          )}
                          {vulnerability.cwe_id && (
                            <span>CWE: {vulnerability.cwe_id}</span>
                          )}
                          {vulnerability.owasp_category && (
                            <span>OWASP: {vulnerability.owasp_category}</span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedVulnerability(vulnerability)}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      )}

      {/* Vulnerability Detail Modal */}
      {selectedVulnerability && (
        <VulnerabilityDetail
          vulnerability={selectedVulnerability}
          open={!!selectedVulnerability}
          onOpenChange={(open) => !open && setSelectedVulnerability(null)}
        />
      )}
    </div>
  );
}
