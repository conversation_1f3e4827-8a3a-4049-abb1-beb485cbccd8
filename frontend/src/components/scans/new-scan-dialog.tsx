'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Globe, Database, Key, Flame, Webhook, Zap } from 'lucide-react';
import { ScanType } from '@/types';
import { useScanStore } from '@/stores/scans';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/shared/loading-spinner';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface NewScanDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onScanCreated?: () => void;
}

const scanTypes = [
  {
    type: ScanType.API_ENDPOINTS,
    icon: Globe,
    title: 'API Endpoints',
    description: 'Scan for API vulnerabilities, authentication issues, and endpoint security',
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
  },
  {
    type: ScanType.DATABASE_SECURITY,
    icon: Database,
    title: 'Database Security',
    description: 'Check for SQL injection, RLS policies, and privilege escalation',
    color: 'text-green-600',
    bgColor: 'bg-green-100',
  },
  {
    type: ScanType.SECRETS,
    icon: Key,
    title: 'Secrets Scanner',
    description: 'Detect exposed API keys, tokens, and sensitive information',
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
  },
  {
    type: ScanType.FIREBASE,
    icon: Flame,
    title: 'Firebase Security',
    description: 'Audit Firebase configuration and security rules',
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
  },
  {
    type: ScanType.WEBHOOKS,
    icon: Webhook,
    title: 'Webhook Security',
    description: 'Validate webhook SSL, authentication, and input validation',
    color: 'text-pink-600',
    bgColor: 'bg-pink-100',
  },
  {
    type: ScanType.COMPREHENSIVE,
    icon: Zap,
    title: 'Comprehensive',
    description: 'Run all security scanners for complete coverage',
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-100',
  },
];

export function NewScanDialog({ open, onOpenChange, onScanCreated }: NewScanDialogProps) {
  const [formData, setFormData] = useState({
    target_url: '',
    scan_type: '' as ScanType,
    scan_name: '',
    scan_description: '',
    max_depth: 3,
    timeout_seconds: 300,
    concurrent_requests: 10,
  });
  const [selectedType, setSelectedType] = useState<ScanType | null>(null);
  const [customHeaders, setCustomHeaders] = useState('');
  const { createScan, isLoading } = useScanStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.target_url || !formData.scan_type) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      // Parse custom headers
      let headers = {};
      if (customHeaders.trim()) {
        try {
          headers = JSON.parse(customHeaders);
        } catch {
          toast.error('Invalid JSON format for custom headers');
          return;
        }
      }

      const { max_depth, timeout_seconds, concurrent_requests, ...scanData } = formData;
      
      await createScan({
        ...scanData,
        config: {
          max_depth,
          timeout_seconds,
          concurrent_requests,
          custom_headers: Object.keys(headers).length > 0 ? headers : undefined,
        },
      });
      
      toast.success('Scan created successfully! 🚀');
      onOpenChange(false);
      onScanCreated?.();
      
      // Reset form
      setFormData({
        target_url: '',
        scan_type: '' as ScanType,
        scan_name: '',
        scan_description: '',
        max_depth: 3,
        timeout_seconds: 300,
        concurrent_requests: 10,
      });
      setSelectedType(null);
      setCustomHeaders('');
    } catch {
      toast.error('Failed to create scan. Please try again.');
    }
  };

  const handleTypeSelect = (type: ScanType) => {
    setSelectedType(type);
    setFormData(prev => ({ ...prev, scan_type: type }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            Create New Security Scan
          </DialogTitle>
          <DialogDescription>
            Configure and launch a new security scan for your application
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Scan Type Selection */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Scan Type *</Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {scanTypes.map((scanType) => {
                const Icon = scanType.icon;
                const isSelected = selectedType === scanType.type;
                
                return (
                  <motion.div
                    key={scanType.type}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Card 
                      className={cn(
                        "cursor-pointer transition-all duration-200 hover:shadow-md",
                        isSelected 
                          ? "border-purple-300 bg-purple-50 shadow-md" 
                          : "border-gray-200 hover:border-purple-200"
                      )}
                      onClick={() => handleTypeSelect(scanType.type)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                          <div className={cn("p-2 rounded-lg", scanType.bgColor)}>
                            <Icon className={cn("h-5 w-5", scanType.color)} />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-medium text-gray-900 mb-1">
                              {scanType.title}
                            </h3>
                            <p className="text-sm text-gray-600 leading-relaxed">
                              {scanType.description}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="target_url">Target URL *</Label>
              <Input
                id="target_url"
                type="url"
                placeholder="https://example.com"
                value={formData.target_url}
                onChange={(e) => setFormData(prev => ({ ...prev, target_url: e.target.value }))}
                className="border-purple-200 focus:border-purple-400 focus:ring-purple-400"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="scan_name">Scan Name</Label>
              <Input
                id="scan_name"
                placeholder="My Security Scan"
                value={formData.scan_name}
                onChange={(e) => setFormData(prev => ({ ...prev, scan_name: e.target.value }))}
                className="border-purple-200 focus:border-purple-400 focus:ring-purple-400"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="scan_description">Description</Label>
              <Textarea
                id="scan_description"
                placeholder="Optional description for this scan..."
                value={formData.scan_description}
                onChange={(e) => setFormData(prev => ({ ...prev, scan_description: e.target.value }))}
                className="border-purple-200 focus:border-purple-400 focus:ring-purple-400"
                rows={3}
              />
            </div>
          </div>

          {/* Advanced Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Advanced Settings</h3>
            
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="max_depth">Max Depth</Label>
                <Input
                  id="max_depth"
                  type="number"
                  min="1"
                  max="10"
                  value={formData.max_depth}
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    if (!isNaN(value) && value >= 1 && value <= 10) {
                      setFormData(prev => ({ ...prev, max_depth: value }));
                    }
                  }}
                  className="border-purple-200 focus:border-purple-400 focus:ring-purple-400"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="timeout_seconds">Timeout (seconds)</Label>
                <Input
                  id="timeout_seconds"
                  type="number"
                  min="30"
                  max="3600"
                  value={formData.timeout_seconds}
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    if (!isNaN(value) && value >= 30 && value <= 3600) {
                      setFormData(prev => ({ ...prev, timeout_seconds: value }));
                    }
                  }}
                  className="border-purple-200 focus:border-purple-400 focus:ring-purple-400"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="concurrent_requests">Concurrent Requests</Label>
                <Input
                  id="concurrent_requests"
                  type="number"
                  min="1"
                  max="50"
                  value={formData.concurrent_requests}
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    if (!isNaN(value) && value >= 1 && value <= 50) {
                      setFormData(prev => ({ ...prev, concurrent_requests: value }));
                    }
                  }}
                  className="border-purple-200 focus:border-purple-400 focus:ring-purple-400"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="custom_headers">Custom Headers (JSON)</Label>
              <Textarea
                id="custom_headers"
                placeholder='{"Authorization": "Bearer token", "X-API-Key": "key"}'
                value={customHeaders}
                onChange={(e) => setCustomHeaders(e.target.value)}
                className="border-purple-200 focus:border-purple-400 focus:ring-purple-400 font-mono text-sm"
                rows={3}
              />
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="gradient-primary text-white hover:opacity-90"
              disabled={isLoading || !formData.target_url || !formData.scan_type}
            >
              {isLoading ? (
                <LoadingSpinner size="sm" text="Creating scan..." />
              ) : (
                'Create Scan'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
