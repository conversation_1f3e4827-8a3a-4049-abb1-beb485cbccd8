import { motion } from 'framer-motion';
import { 
  Clock, 
  Target, 
  Activity, 
  TrendingUp, 
  BarChart3,
  Timer,
  Globe
} from 'lucide-react';
import { Scan } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatDistanceToNow, differenceInSeconds } from 'date-fns';

interface ScanMetricsProps {
  scan: Scan;
}

export function ScanMetrics({ scan }: ScanMetricsProps) {
  const getScanDuration = () => {
    if (!scan.started_at) return 'Not started';
    if (!scan.completed_at) return 'In progress';
    
    const duration = differenceInSeconds(
      new Date(scan.completed_at),
      new Date(scan.started_at)
    );
    
    if (duration < 60) return `${duration} seconds`;
    if (duration < 3600) return `${Math.floor(duration / 60)} minutes`;
    return `${Math.floor(duration / 3600)} hours`;
  };

  const getEfficiencyScore = () => {
    if (scan.total_vulnerabilities === 0) return 100;
    const criticalHighCount = scan.critical_count + scan.high_count;
    const totalCount = scan.total_vulnerabilities;
    return Math.max(0, 100 - (criticalHighCount / totalCount) * 100);
  };

  const metrics = [
    {
      title: 'Scan Duration',
      value: getScanDuration(),
      icon: Timer,
      description: 'Total time taken',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Target Depth',
      value: scan.max_depth,
      icon: Target,
      description: 'Maximum crawl depth',
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Concurrent Requests',
      value: scan.concurrent_requests,
      icon: Activity,
      description: 'Parallel requests',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      title: 'Timeout Setting',
      value: `${scan.timeout_seconds}s`,
      icon: Clock,
      description: 'Request timeout',
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
  ];

  const performanceMetrics = [
    {
      title: 'Efficiency Score',
      value: `${Math.round(getEfficiencyScore())}%`,
      description: 'Based on vulnerability severity distribution',
      trend: getEfficiencyScore() > 80 ? 'good' : getEfficiencyScore() > 60 ? 'medium' : 'poor',
    },
    {
      title: 'Coverage',
      value: scan.status === 'completed' ? '100%' : `${scan.progress_percentage}%`,
      description: 'Scan completion percentage',
      trend: scan.status === 'completed' ? 'good' : 'medium',
    },
    {
      title: 'Vulnerability Density',
      value: scan.total_vulnerabilities > 0 ? `${scan.total_vulnerabilities} found` : 'Clean',
      description: 'Total security issues discovered',
      trend: scan.total_vulnerabilities === 0 ? 'good' : scan.total_vulnerabilities < 5 ? 'medium' : 'poor',
    },
  ];

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'good': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'poor': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="space-y-6">
      {/* Configuration Metrics */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Scan Configuration</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {metrics.map((metric, index) => {
            const Icon = metric.icon;
            return (
              <motion.div
                key={metric.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${metric.bgColor}`}>
                        <Icon className={`h-5 w-5 ${metric.color}`} />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                        <p className="text-lg font-bold text-gray-900">{metric.value}</p>
                        <p className="text-xs text-gray-500">{metric.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Performance Metrics */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {performanceMetrics.map((metric, index) => (
            <motion.div
              key={metric.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 + index * 0.1 }}
            >
              <Card>
                <CardContent className="p-6">
                  <div className="text-center">
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mb-3 ${getTrendColor(metric.trend)}`}>
                      <TrendingUp className="h-4 w-4 mr-1" />
                      {metric.trend === 'good' ? 'Excellent' : metric.trend === 'medium' ? 'Good' : 'Needs Attention'}
                    </div>
                    <div className="text-2xl font-bold text-gray-900 mb-1">
                      {metric.value}
                    </div>
                    <div className="text-sm font-medium text-gray-700 mb-2">
                      {metric.title}
                    </div>
                    <div className="text-xs text-gray-500">
                      {metric.description}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Detailed Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Scan Timeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between py-3 border-b border-gray-100">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="font-medium text-gray-900">Scan Created</span>
              </div>
              <span className="text-sm text-gray-600">
                {formatDistanceToNow(new Date(scan.created_at), { addSuffix: true })}
              </span>
            </div>
            
            {scan.started_at && (
              <div className="flex items-center justify-between py-3 border-b border-gray-100">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="font-medium text-gray-900">Scan Started</span>
                </div>
                <span className="text-sm text-gray-600">
                  {formatDistanceToNow(new Date(scan.started_at), { addSuffix: true })}
                </span>
              </div>
            )}
            
            {scan.completed_at && (
              <div className="flex items-center justify-between py-3">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="font-medium text-gray-900">Scan Completed</span>
                </div>
                <span className="text-sm text-gray-600">
                  {formatDistanceToNow(new Date(scan.completed_at), { addSuffix: true })}
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Target Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Target Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-600">Target URL</label>
              <p className="text-gray-900 break-all">{scan.target_url}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Scan Type</label>
              <p className="text-gray-900 capitalize">{scan.scan_type.replace('_', ' ')}</p>
            </div>
            {scan.custom_headers && Object.keys(scan.custom_headers).length > 0 && (
              <div className="md:col-span-2">
                <label className="text-sm font-medium text-gray-600">Custom Headers</label>
                <pre className="mt-1 p-3 bg-gray-50 rounded-lg text-sm overflow-x-auto">
                  {JSON.stringify(scan.custom_headers, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
