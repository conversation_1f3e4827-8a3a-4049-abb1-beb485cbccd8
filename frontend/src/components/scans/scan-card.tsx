import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { motion } from 'framer-motion';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  Loader2,
  Play,
  Pause,
  Trash2,
  MoreHorizontal,
  ExternalLink,
  Download
} from 'lucide-react';
import { Scan, ScanStatus, ScanType } from '@/types';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { useScanStore } from '@/stores/scans';
import { toast } from 'sonner';

interface ScanCardProps {
  scan: Scan;
}

const statusConfig = {
  [ScanStatus.PENDING]: {
    icon: Clock,
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    label: 'Pending',
    bgColor: 'bg-yellow-50',
  },
  [ScanStatus.RUNNING]: {
    icon: Loader2,
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    label: 'Running',
    bgColor: 'bg-blue-50',
  },
  [ScanStatus.COMPLETED]: {
    icon: CheckCircle,
    color: 'bg-green-100 text-green-800 border-green-200',
    label: 'Completed',
    bgColor: 'bg-green-50',
  },
  [ScanStatus.FAILED]: {
    icon: XCircle,
    color: 'bg-red-100 text-red-800 border-red-200',
    label: 'Failed',
    bgColor: 'bg-red-50',
  },
  [ScanStatus.CANCELLED]: {
    icon: XCircle,
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    label: 'Cancelled',
    bgColor: 'bg-gray-50',
  },
};

const typeLabels = {
  [ScanType.API_ENDPOINTS]: 'API Endpoints',
  [ScanType.DATABASE_SECURITY]: 'Database Security',
  [ScanType.SECRETS]: 'Secrets',
  [ScanType.FIREBASE]: 'Firebase',
  [ScanType.WEBHOOKS]: 'Webhooks',
  [ScanType.COMPREHENSIVE]: 'Comprehensive',
};

export function ScanCard({ scan }: ScanCardProps) {
  const { cancelScan, deleteScan } = useScanStore();
  const config = statusConfig[scan.status];
  const StatusIcon = config.icon;

  const handleCancel = async () => {
    try {
      await cancelScan(scan.id.toString());
      toast.success('Scan cancelled successfully');
    } catch {
      toast.error('Failed to cancel scan');
    }
  };

  const handleDelete = async () => {
    try {
      await deleteScan(scan.id.toString());
      toast.success('Scan deleted successfully');
    } catch {
      toast.error('Failed to delete scan');
    }
  };

  const handleRetry = () => {
    // TODO: Implement retry functionality
    toast.info('Retry functionality coming soon');
  };

  const getSeverityColor = (count: number, severity: string) => {
    if (count === 0) return 'text-gray-500';
    switch (severity) {
      case 'critical': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <motion.div
      whileHover={{ y: -2 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="border-purple-100 bg-white/50 backdrop-blur-sm hover:shadow-lg transition-all duration-300">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <Badge className={cn("text-xs", config.color)}>
                  <StatusIcon
                    className={cn(
                      "mr-1 h-3 w-3",
                      scan.status === ScanStatus.RUNNING && "animate-spin"
                    )}
                  />
                  {config.label}
                  {(scan.status === ScanStatus.RUNNING || scan.status === ScanStatus.PENDING) && (
                    <span className="ml-1 h-1.5 w-1.5 bg-current rounded-full animate-pulse" />
                  )}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {typeLabels[scan.scan_type]}
                </Badge>
              </div>
              
              <h3 className="font-semibold text-gray-900 truncate">
                {scan.scan_name || `Scan #${scan.id}`}
              </h3>
              
              <div className="flex items-center gap-1 mt-1">
                <ExternalLink className="h-3 w-3 text-gray-400" />
                <span className="text-sm text-gray-600 truncate">
                  {scan.target_url}
                </span>
              </div>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link href={`/dashboard/scans/${scan.id}`}>
                    View Details
                  </Link>
                </DropdownMenuItem>
                
                {scan.status === ScanStatus.COMPLETED && (
                  <DropdownMenuItem>
                    <Download className="mr-2 h-4 w-4" />
                    Download Report
                  </DropdownMenuItem>
                )}
                
                {scan.status === ScanStatus.RUNNING && (
                  <DropdownMenuItem onClick={handleCancel}>
                    <Pause className="mr-2 h-4 w-4" />
                    Cancel Scan
                  </DropdownMenuItem>
                )}
                
                {scan.status === ScanStatus.FAILED && (
                  <DropdownMenuItem onClick={handleRetry}>
                    <Play className="mr-2 h-4 w-4" />
                    Retry Scan
                  </DropdownMenuItem>
                )}
                
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={handleDelete}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          {/* Progress Bar for Running Scans */}
          {scan.status === ScanStatus.RUNNING && (
            <div className="mb-4">
              <div className="flex items-center justify-between text-sm mb-2">
                <span className="text-gray-600">Progress</span>
                <span className="font-medium">{scan.progress_percentage}%</span>
              </div>
              <Progress value={scan.progress_percentage} className="h-2" />
            </div>
          )}
          
          {/* Vulnerability Summary for Completed Scans */}
          {scan.status === ScanStatus.COMPLETED && (
            <div className="mb-4">
              <div className="grid grid-cols-3 gap-3 text-sm">
                <div className="text-center p-2 bg-gray-50 rounded-lg">
                  <div className="font-semibold text-gray-900">
                    {scan.total_vulnerabilities}
                  </div>
                  <div className="text-xs text-gray-600">Total Issues</div>
                </div>
                <div className="text-center p-2 bg-red-50 rounded-lg">
                  <div className={cn("font-semibold", getSeverityColor(scan.critical_count + scan.high_count, 'critical'))}>
                    {scan.critical_count + scan.high_count}
                  </div>
                  <div className="text-xs text-gray-600">Critical/High</div>
                </div>
                {scan.security_grade && (
                  <div className={cn(
                    "text-center p-2 rounded-lg",
                    scan.security_grade === 'A+' || scan.security_grade === 'A' ? 'bg-green-50' :
                    scan.security_grade === 'B+' || scan.security_grade === 'B' ? 'bg-blue-50' :
                    scan.security_grade === 'C+' || scan.security_grade === 'C' ? 'bg-yellow-50' :
                    'bg-red-50'
                  )}>
                    <div className={cn(
                      "font-bold text-lg",
                      scan.security_grade === 'A+' || scan.security_grade === 'A' ? 'text-green-700' :
                      scan.security_grade === 'B+' || scan.security_grade === 'B' ? 'text-blue-700' :
                      scan.security_grade === 'C+' || scan.security_grade === 'C' ? 'text-yellow-700' :
                      'text-red-700'
                    )}>
                      {scan.security_grade}
                    </div>
                    <div className="text-xs text-gray-600">Security Grade</div>
                  </div>
                )}
              </div>
              
              {scan.total_vulnerabilities > 0 && (
                <div className="flex items-center justify-between text-xs mt-3 pt-3 border-t border-gray-100">
                  <div className="flex items-center gap-3">
                    <span className={getSeverityColor(scan.critical_count, 'critical')}>
                      {scan.critical_count} Critical
                    </span>
                    <span className={getSeverityColor(scan.high_count, 'high')}>
                      {scan.high_count} High
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className={getSeverityColor(scan.medium_count, 'medium')}>
                      {scan.medium_count} Medium
                    </span>
                    <span className={getSeverityColor(scan.low_count, 'low')}>
                      {scan.low_count} Low
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}
          
          {/* Error Message for Failed Scans */}
          {scan.status === ScanStatus.FAILED && scan.error_message && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-800">{scan.error_message}</p>
            </div>
          )}
          
          {/* Scan Description */}
          {scan.scan_description && (
            <p className="text-sm text-gray-600 mb-4 line-clamp-2">
              {scan.scan_description}
            </p>
          )}
          
          {/* Footer */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>
              Created {formatDistanceToNow(new Date(scan.created_at), { addSuffix: true })}
            </span>
            {scan.completed_at && (
              <span>
                Completed {formatDistanceToNow(new Date(scan.completed_at), { addSuffix: true })}
              </span>
            )}
          </div>
          
          {/* Action Button */}
          <div className="mt-4">
            <Button 
              asChild 
              className="w-full gradient-primary text-white hover:opacity-90"
              size="sm"
            >
              <Link href={`/dashboard/scans/${scan.id}`}>
                View Details
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
