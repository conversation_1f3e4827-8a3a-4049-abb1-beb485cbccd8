import { motion } from 'framer-motion';
import { Clock, Zap, Target, Shield } from 'lucide-react';
import { Scan } from '@/types';
import { Progress } from '@/components/ui/progress';
import { formatDistanceToNow } from 'date-fns';

interface ScanProgressProps {
  scan: Scan;
}

export function ScanProgress({ scan }: ScanProgressProps) {
  const getProgressStage = (percentage: number) => {
    if (percentage < 25) return 'Initializing scan...';
    if (percentage < 50) return 'Discovering endpoints...';
    if (percentage < 75) return 'Running security tests...';
    if (percentage < 95) return 'Analyzing results...';
    return 'Finalizing report...';
  };

  const getEstimatedTimeRemaining = (percentage: number, startTime: string) => {
    if (percentage <= 0) return 'Calculating...';
    
    const elapsed = Date.now() - new Date(startTime).getTime();
    const totalEstimated = (elapsed / percentage) * 100;
    const remaining = totalEstimated - elapsed;
    
    if (remaining <= 0) return 'Almost done...';
    
    const minutes = Math.ceil(remaining / (1000 * 60));
    if (minutes < 1) return 'Less than a minute';
    if (minutes === 1) return '1 minute';
    return `${minutes} minutes`;
  };

  return (
    <div className="space-y-6">
      {/* Main Progress */}
      <div className="text-center">
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="mb-4"
        >
          <div className="text-3xl font-bold text-gray-900 mb-2">
            {scan.progress_percentage}%
          </div>
          <div className="text-lg text-gray-600 mb-4">
            {getProgressStage(scan.progress_percentage)}
          </div>
        </motion.div>
        
        <div className="max-w-md mx-auto">
          <Progress 
            value={scan.progress_percentage} 
            className="h-3 mb-2"
          />
          <div className="flex justify-between text-sm text-gray-500">
            <span>0%</span>
            <span>100%</span>
          </div>
        </div>
      </div>

      {/* Progress Details */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="text-center p-4 bg-blue-50 rounded-lg"
        >
          <Clock className="h-6 w-6 text-blue-600 mx-auto mb-2" />
          <div className="text-sm font-medium text-blue-900">Time Elapsed</div>
          <div className="text-blue-700">
            {scan.started_at 
              ? formatDistanceToNow(new Date(scan.started_at))
              : 'Not started'
            }
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="text-center p-4 bg-purple-50 rounded-lg"
        >
          <Zap className="h-6 w-6 text-purple-600 mx-auto mb-2" />
          <div className="text-sm font-medium text-purple-900">Estimated Remaining</div>
          <div className="text-purple-700">
            {scan.started_at 
              ? getEstimatedTimeRemaining(scan.progress_percentage, scan.started_at)
              : 'Calculating...'
            }
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="text-center p-4 bg-green-50 rounded-lg"
        >
          <Target className="h-6 w-6 text-green-600 mx-auto mb-2" />
          <div className="text-sm font-medium text-green-900">Scan Type</div>
          <div className="text-green-700 capitalize">
            {scan.scan_type.replace('_', ' ')}
          </div>
        </motion.div>
      </div>

      {/* Live Updates */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="bg-gray-50 rounded-lg p-4"
      >
        <div className="flex items-center gap-2 mb-3">
          <Shield className="h-5 w-5 text-gray-600" />
          <span className="font-medium text-gray-900">Live Updates</span>
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        </div>
        
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center justify-between">
            <span>Scanning target URL...</span>
            <span className="text-green-600">✓</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Discovering endpoints...</span>
            <span className={scan.progress_percentage > 25 ? "text-green-600" : "text-gray-400"}>
              {scan.progress_percentage > 25 ? "✓" : "○"}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span>Running security tests...</span>
            <span className={scan.progress_percentage > 50 ? "text-green-600" : "text-gray-400"}>
              {scan.progress_percentage > 50 ? "✓" : "○"}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span>Analyzing vulnerabilities...</span>
            <span className={scan.progress_percentage > 75 ? "text-green-600" : "text-gray-400"}>
              {scan.progress_percentage > 75 ? "✓" : "○"}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span>Generating report...</span>
            <span className={scan.progress_percentage > 90 ? "text-green-600" : "text-gray-400"}>
              {scan.progress_percentage > 90 ? "✓" : "○"}
            </span>
          </div>
        </div>
      </motion.div>

      {/* Configuration Summary */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-3">Scan Configuration</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Max Depth:</span>
            <span className="ml-2 font-medium">{scan.max_depth}</span>
          </div>
          <div>
            <span className="text-gray-600">Timeout:</span>
            <span className="ml-2 font-medium">{scan.timeout_seconds}s</span>
          </div>
          <div>
            <span className="text-gray-600">Concurrent:</span>
            <span className="ml-2 font-medium">{scan.concurrent_requests}</span>
          </div>
          <div>
            <span className="text-gray-600">Target:</span>
            <span className="ml-2 font-medium truncate">{scan.target_url}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
