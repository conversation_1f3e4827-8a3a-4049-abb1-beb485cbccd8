'use client';

import { useState } from 'react';
import { 
  AlertTriangle, 
  ExternalLink, 
  Shield, 
  Code, 
  FileText,
  CheckCircle,
  XCircle,
  Copy,
  Download,
  TrendingUp,
  Star,
  Target,
  Brain
} from 'lucide-react';
import { Vulnerability, VulnerabilitySeverity, BusinessPriority } from '@/types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface VulnerabilityDetailProps {
  vulnerability: Vulnerability;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const severityConfig = {
  [VulnerabilitySeverity.CRITICAL]: {
    color: 'bg-red-100 text-red-800 border-red-200',
    bgColor: 'bg-red-50',
    textColor: 'text-red-600',
  },
  [VulnerabilitySeverity.HIGH]: {
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    bgColor: 'bg-orange-50',
    textColor: 'text-orange-600',
  },
  [VulnerabilitySeverity.MEDIUM]: {
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    bgColor: 'bg-yellow-50',
    textColor: 'text-yellow-600',
  },
  [VulnerabilitySeverity.LOW]: {
    color: 'bg-green-100 text-green-800 border-green-200',
    bgColor: 'bg-green-50',
    textColor: 'text-green-600',
  },
};

const businessPriorityConfig = {
  [BusinessPriority.CRITICAL]: {
    color: 'bg-red-100 text-red-800 border-red-200',
    icon: Star,
    label: 'Critical Business Impact',
    description: 'Immediate threat to core business operations',
  },
  [BusinessPriority.HIGH]: {
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    icon: TrendingUp,
    label: 'High Business Impact',
    description: 'Significant risk to business operations or security',
  },
  [BusinessPriority.MEDIUM]: {
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    icon: Target,
    label: 'Medium Business Impact',
    description: 'Moderate risk with limited business impact',
  },
  [BusinessPriority.LOW]: {
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    icon: Shield,
    label: 'Low Business Impact',
    description: 'Minor risk with minimal business impact',
  },
  [BusinessPriority.VERY_LOW]: {
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: Shield,
    label: 'Very Low Business Impact',
    description: 'Negligible risk with no significant business impact',
  },
};

export function VulnerabilityDetail({ vulnerability, open, onOpenChange }: VulnerabilityDetailProps) {
  const [resolutionNotes, setResolutionNotes] = useState(vulnerability.resolution_notes || '');
  const config = severityConfig[vulnerability.severity];
  const businessPriorityDetails = businessPriorityConfig[vulnerability.business_priority];
  const BusinessPriorityIcon = businessPriorityDetails?.icon || Shield;

  const handleCopyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const handleMarkResolved = () => {
    // TODO: Implement mark as resolved
    toast.success('Marked as resolved');
  };

  const handleMarkFalsePositive = () => {
    // TODO: Implement mark as false positive
    toast.success('Marked as false positive');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2 flex-wrap">
                <Badge className={cn("text-sm", businessPriorityDetails?.color)}>
                  <BusinessPriorityIcon className="mr-1 h-3 w-3" />
                  {vulnerability.business_priority.toUpperCase().replace('_', ' ')} PRIORITY
                </Badge>
                <Badge className={cn("text-sm", config.color)}>
                  <AlertTriangle className="mr-1 h-3 w-3" />
                  {vulnerability.severity.toUpperCase()}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {vulnerability.category}
                </Badge>
                <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">
                  <Target className="mr-1 h-3 w-3" />
                  Score: {vulnerability.endpoint_score}
                </Badge>
                {vulnerability.is_resolved && (
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="mr-1 h-3 w-3" />
                    Resolved
                  </Badge>
                )}
                {vulnerability.is_false_positive && (
                  <Badge className="bg-gray-100 text-gray-800">
                    <XCircle className="mr-1 h-3 w-3" />
                    False Positive
                  </Badge>
                )}
                {vulnerability.is_likely_false_positive && (
                  <Badge className="bg-yellow-100 text-yellow-800">
                    <Brain className="mr-1 h-3 w-3" />
                    AI: Likely False Positive ({vulnerability.false_positive_confidence}%)
                  </Badge>
                )}
              </div>
              <DialogTitle className="text-xl font-bold text-gray-900">
                {vulnerability.title}
              </DialogTitle>
              <DialogDescription className="mt-2">
                {vulnerability.description}
              </DialogDescription>
            </div>
            
            <div className="flex items-center gap-2 ml-4">
              <Button variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </div>
          </div>
        </DialogHeader>

        <Tabs defaultValue="business" className="mt-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="business">Business Impact</TabsTrigger>
            <TabsTrigger value="technical">Technical</TabsTrigger>
            <TabsTrigger value="remediation">Remediation</TabsTrigger>
            <TabsTrigger value="actions">Actions</TabsTrigger>
          </TabsList>

          <TabsContent value="business" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Business Priority */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BusinessPriorityIcon className="h-5 w-5" />
                    Business Priority
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Priority Level</label>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={cn("text-sm", businessPriorityDetails?.color)}>
                        <BusinessPriorityIcon className="mr-1 h-3 w-3" />
                        {vulnerability.business_priority.toUpperCase().replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Impact Description</label>
                    <p className="text-gray-900 text-sm mt-1">{businessPriorityDetails?.description}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Endpoint Scoring */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Endpoint Scoring
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Business Risk Score</label>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${vulnerability.endpoint_score}%` }}
                        />
                      </div>
                      <span className="text-sm font-semibold text-blue-600">
                        {vulnerability.endpoint_score}/100
                      </span>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Risk Level</label>
                    <p className="text-gray-900 text-sm mt-1">
                      {vulnerability.endpoint_score >= 80 ? 'Very High Risk' :
                       vulnerability.endpoint_score >= 60 ? 'High Risk' :
                       vulnerability.endpoint_score >= 40 ? 'Medium Risk' :
                       vulnerability.endpoint_score >= 20 ? 'Low Risk' : 'Very Low Risk'}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* AI Analysis */}
            {(vulnerability.is_likely_false_positive || vulnerability.false_positive_confidence > 0) && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5" />
                    AI Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">False Positive Analysis</label>
                    <div className="flex items-center gap-3 mt-2">
                      {vulnerability.is_likely_false_positive ? (
                        <Badge className="bg-yellow-100 text-yellow-800">
                          <Brain className="mr-1 h-3 w-3" />
                          Likely False Positive
                        </Badge>
                      ) : (
                        <Badge className="bg-green-100 text-green-800">
                          <CheckCircle className="mr-1 h-3 w-3" />
                          Likely Real Vulnerability
                        </Badge>
                      )}
                      <span className="text-sm text-gray-600">
                        Confidence: {vulnerability.false_positive_confidence}%
                      </span>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Recommendation</label>
                    <p className="text-gray-900 text-sm mt-1">
                      {vulnerability.is_likely_false_positive
                        ? 'This vulnerability has been flagged by AI as likely being a false positive. Manual review is recommended before dismissing.'
                        : 'AI analysis indicates this is likely a legitimate security vulnerability that should be addressed.'}
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="details" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Business Priority</label>
                    <p className={cn("font-semibold capitalize", businessPriorityDetails?.color.split(' ')[1])}>
                      {vulnerability.business_priority.replace('_', ' ')}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Severity</label>
                    <p className={cn("font-semibold capitalize", config.textColor)}>
                      {vulnerability.severity}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Category</label>
                    <p className="text-gray-900">{vulnerability.category}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Endpoint Score</label>
                    <p className="text-gray-900">{vulnerability.endpoint_score}/100</p>
                  </div>
                  {vulnerability.cvss_score && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">CVSS Score</label>
                      <p className="text-gray-900">{vulnerability.cvss_score}</p>
                    </div>
                  )}
                  {vulnerability.cwe_id && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">CWE ID</label>
                      <p className="text-gray-900">{vulnerability.cwe_id}</p>
                    </div>
                  )}
                  {vulnerability.owasp_category && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">OWASP Category</label>
                      <p className="text-gray-900">{vulnerability.owasp_category}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Location Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Location</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {vulnerability.url && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">URL</label>
                      <div className="flex items-center gap-2">
                        <p className="text-gray-900 break-all flex-1">{vulnerability.url}</p>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyToClipboard(vulnerability.url!)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                  {vulnerability.method && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">HTTP Method</label>
                      <p className="text-gray-900">{vulnerability.method}</p>
                    </div>
                  )}
                  {vulnerability.parameter && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Parameter</label>
                      <p className="text-gray-900">{vulnerability.parameter}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">{vulnerability.description}</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="technical" className="space-y-6">
            {/* Payload */}
            {vulnerability.payload && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-5 w-5" />
                    Payload
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    <pre className="bg-gray-50 p-4 rounded-lg text-sm overflow-x-auto">
                      <code>{vulnerability.payload}</code>
                    </pre>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => handleCopyToClipboard(vulnerability.payload!)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Evidence */}
            {vulnerability.evidence && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Evidence
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    <pre className="bg-gray-50 p-4 rounded-lg text-sm overflow-x-auto">
                      <code>{vulnerability.evidence}</code>
                    </pre>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => handleCopyToClipboard(vulnerability.evidence!)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* References */}
            {vulnerability.references && vulnerability.references.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ExternalLink className="h-5 w-5" />
                    References
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {vulnerability.references.map((ref, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <ExternalLink className="h-4 w-4 text-gray-400" />
                        <a
                          href={ref}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 underline break-all"
                        >
                          {ref}
                        </a>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="remediation" className="space-y-6">
            {vulnerability.remediation && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Remediation Steps
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-sm max-w-none">
                    <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                      {vulnerability.remediation}
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Resolution Notes */}
            <Card>
              <CardHeader>
                <CardTitle>Resolution Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  placeholder="Add notes about how this vulnerability was resolved..."
                  value={resolutionNotes}
                  onChange={(e) => setResolutionNotes(e.target.value)}
                  rows={4}
                  className="mb-4"
                />
                <Button className="gradient-primary text-white hover:opacity-90">
                  Save Notes
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="actions" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Vulnerability Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    onClick={handleMarkResolved}
                    className="h-auto p-4 flex flex-col items-center gap-2"
                    variant={vulnerability.is_resolved ? "default" : "outline"}
                  >
                    <CheckCircle className="h-6 w-6 text-green-600" />
                    <span className="font-medium">Mark as Resolved</span>
                    <span className="text-xs text-gray-500">
                      This vulnerability has been fixed
                    </span>
                  </Button>

                  <Button
                    onClick={handleMarkFalsePositive}
                    className="h-auto p-4 flex flex-col items-center gap-2"
                    variant={vulnerability.is_false_positive ? "default" : "outline"}
                  >
                    <XCircle className="h-6 w-6 text-gray-600" />
                    <span className="font-medium">Mark as False Positive</span>
                    <span className="text-xs text-gray-500">
                      This is not a real vulnerability
                    </span>
                  </Button>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-medium text-gray-900 mb-3">Export Options</h4>
                  <div className="flex gap-3">
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      PDF Report
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      JSON Data
                    </Button>
                    <Button variant="outline" size="sm">
                      <Copy className="mr-2 h-4 w-4" />
                      Copy Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
