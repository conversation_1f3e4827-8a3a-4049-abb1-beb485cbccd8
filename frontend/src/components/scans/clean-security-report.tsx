'use client';

import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import {
  Shield,
  CheckCircle,
  Lock,
  Globe,
  Database,
  Key
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { apiClient } from '@/lib/api';
import { LoadingSpinner } from '@/components/shared/loading-spinner';

interface CleanSecurityReportProps {
  scanId: string;
}

interface ScanMetadata {
  scan_type?: string;
  detected_services?: Array<{ name: string; version?: string }>;
  tests_performed?: string[];
  total_endpoints_tested?: number;
  cloud_services_found?: number;
  discovered_endpoints?: number;
  valid_endpoints?: number;
  filtered_out?: number;
  filtering_accuracy?: number;
  scan_methods?: string[];
}

interface ScanResults {
  metadata?: ScanMetadata;
  [key: string]: unknown;
}

interface SecurityTest {
  category: string;
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  tests: string[];
  status: 'passed' | 'not_applicable';
  details: string;
}

export function CleanSecurityReport({ scanId }: CleanSecurityReportProps) {
  const { data: scan, isLoading: scanLoading } = useQuery({
    queryKey: ['scan', scanId],
    queryFn: () => apiClient.getScan(parseInt(scanId)),
    enabled: !!scanId
  });

  const { data: scanResults, isLoading: resultsLoading } = useQuery({
    queryKey: ['scan-results', scanId],
    queryFn: () => apiClient.getScanReport(parseInt(scanId), 'json'),
    enabled: !!scanId
  });




  if (scanLoading || resultsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading security report..." />
      </div>
    );
  }

  const metadata: ScanMetadata = (scanResults as ScanResults)?.metadata || {};
  
  const securityTests: SecurityTest[] = [
    {
      category: 'authentication',
      icon: Lock,
      title: 'Authentication & Authorization',
      description: 'Tests for authentication bypass, weak credentials, and access control issues',
      tests: [
        'Authentication bypass attempts',
        'Weak credential testing',
        'Session management validation',
        'Access control verification'
      ],
      status: 'passed',
      details: 'All authentication mechanisms are properly implemented and secure.'
    },
    {
      category: 'injection',
      icon: Database,
      title: 'Injection Attacks',
      description: 'SQL injection, NoSQL injection, and other injection vulnerability tests',
      tests: [
        'SQL injection testing',
        'NoSQL injection testing', 
        'LDAP injection testing',
        'Command injection testing'
      ],
      status: 'passed',
      details: 'No injection vulnerabilities found. Input validation and parameterized queries are properly implemented.'
    },
    {
      category: 'api_security',
      icon: Globe,
      title: 'API Security',
      description: 'REST API, GraphQL, and WebSocket security testing',
      tests: [
        'REST API endpoint testing',
        'GraphQL introspection testing',
        'WebSocket security testing',
        'API rate limiting verification'
      ],
      status: 'passed',
      details: 'API endpoints are properly secured with appropriate authentication and input validation.'
    },
    {
      category: 'cloud_services',
      icon: Key,
      title: 'Cloud Database Security',
      description: 'Modern cloud database and service security testing',
      tests: [
        'Supabase RLS policy testing',
        'Firebase security rules testing',
        'Cloud service configuration testing',
        'API key exposure testing'
      ],
      status: (metadata.detected_services?.length ?? 0) > 0 ? 'passed' : 'not_applicable',
      details: (metadata.detected_services?.length ?? 0) > 0 
        ? `Tested ${metadata.detected_services?.length ?? 0} cloud services. All are properly configured.`
        : 'No cloud database services detected.'
    }
  ];

  const scanStats = {
    endpointsTested: metadata.total_endpoints_tested || metadata.discovered_endpoints || 0,
    testsPerformed: metadata.tests_performed?.length || 0,
    scanMethods: metadata.scan_methods?.length || 0,
    cloudServices: metadata.cloud_services_found || 0
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="text-center py-8">
        <motion.div
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.1 }}
          className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-6"
        >
          <Shield className="h-10 w-10 text-green-600" />
        </motion.div>
        
        <motion.h2
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="text-3xl font-bold text-gray-900 mb-2"
        >
          🎉 Security Scan Passed!
        </motion.h2>
        
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto"
        >
          Excellent news! Your application passed all security tests. No vulnerabilities were found during this comprehensive security scan.
        </motion.p>


      </div>

      {/* Security Summary - Only show if we have meaningful data */}
      {(scanStats.endpointsTested > 0 || scanStats.testsPerformed > 0 || scanStats.scanMethods > 0 || scanStats.cloudServices > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Security Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              {scanStats.endpointsTested > 0 && (
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{scanStats.endpointsTested}</div>
                  <div className="text-sm text-gray-600">Endpoints Tested</div>
                </div>
              )}
              {scanStats.testsPerformed > 0 && (
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{scanStats.testsPerformed}</div>
                  <div className="text-sm text-gray-600">Security Tests</div>
                </div>
              )}
              {scanStats.scanMethods > 0 && (
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{scanStats.scanMethods}</div>
                  <div className="text-sm text-gray-600">Scan Methods</div>
                </div>
              )}
              {scanStats.cloudServices > 0 && (
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">{scanStats.cloudServices}</div>
                  <div className="text-sm text-gray-600">Cloud Services</div>
                </div>
              )}
            </div>

            <div className="text-sm text-gray-600">
              {scan?.scan_type && (
                <p className="mb-2">
                  <strong>Scan Type:</strong> {scan.scan_type.replace('_', ' ').toUpperCase()}
                </p>
              )}
              {scan?.target_url && (
                <p className="mb-2">
                  <strong>Target:</strong> {scan.target_url}
                </p>
              )}
              {scan?.completed_at && (
                <p>
                  <strong>Completed:</strong> {new Date(scan.completed_at).toLocaleString()}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Security Tests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Security Tests Performed
          </CardTitle>
        </CardHeader>
        <CardContent>
              <div className="space-y-4">
                {securityTests.map((test, index) => {
                  const Icon = test.icon;
                  return (
                    <motion.div
                      key={test.category}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="border rounded-lg p-4"
                    >
                      <div className="flex items-start gap-3">
                        <div className={`p-2 rounded-lg ${
                          test.status === 'passed' ? 'bg-green-100' : 'bg-gray-100'
                        }`}>
                          <Icon className={`h-5 w-5 ${
                            test.status === 'passed' ? 'text-green-600' : 'text-gray-500'
                          }`} />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium text-gray-900">{test.title}</h4>
                            <Badge variant={test.status === 'passed' ? 'default' : 'secondary'}>
                              {test.status === 'passed' ? 'PASSED' : 'N/A'}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-3">{test.description}</p>
                          <p className="text-sm text-gray-700 mb-3">{test.details}</p>
                          <div className="text-xs text-gray-500">
                            <strong>Tests included:</strong> {test.tests.join(', ')}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </CardContent>
        </Card>
    </motion.div>
  );
}
