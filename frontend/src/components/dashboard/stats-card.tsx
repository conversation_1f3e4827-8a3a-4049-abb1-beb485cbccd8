import { LucideIcon, TrendingUp, TrendingDown } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/shared/loading-spinner';
import { cn } from '@/lib/utils';

interface StatsCardProps {
  title: string;
  value: number;
  icon: LucideIcon;
  description: string;
  trend?: string;
  trendUp?: boolean;
  isLoading?: boolean;
}

export function StatsCard({
  title,
  value,
  icon: Icon,
  description,
  trend,
  trendUp,
  isLoading,
}: StatsCardProps) {
  return (
    <Card className="border-purple-100 bg-white/50 backdrop-blur-sm hover:shadow-lg transition-all duration-300">
      <CardContent className="p-6">
        {isLoading ? (
          <div className="flex items-center justify-center h-20">
            <LoadingSpinner size="sm" />
          </div>
        ) : (
          <>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 gradient-primary rounded-lg">
                  <Icon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">{title}</p>
                  <p className="text-2xl font-bold text-gray-900">{value.toLocaleString()}</p>
                </div>
              </div>
            </div>
            
            <div className="mt-4 flex items-center justify-between">
              <p className="text-xs text-gray-500">{description}</p>
              {trend && (
                <div className={cn(
                  "flex items-center gap-1 text-xs font-medium",
                  trendUp ? "text-green-600" : "text-red-600"
                )}>
                  {trendUp ? (
                    <TrendingUp className="h-3 w-3" />
                  ) : (
                    <TrendingDown className="h-3 w-3" />
                  )}
                  {trend}
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
