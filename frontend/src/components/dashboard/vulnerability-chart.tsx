import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, Toolt<PERSON> } from 'recharts';
import { VulnerabilitySummary } from '@/types';
import { LoadingSpinner } from '@/components/shared/loading-spinner';
import { AlertTriangle } from 'lucide-react';

interface VulnerabilityChartProps {
  data?: VulnerabilitySummary;
  isLoading?: boolean;
}

const SEVERITY_COLORS = {
  critical: '#DC2626', // red-600
  high: '#EA580C',     // orange-600
  medium: '#D97706',   // amber-600
  low: '#65A30D',      // lime-600
};

export function VulnerabilityChart({ data, isLoading }: VulnerabilityChartProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="md" text="Loading vulnerability data..." />
      </div>
    );
  }

  if (!data || data.total_vulnerabilities === 0) {
    return (
      <div className="text-center py-8">
        <div className="mx-auto w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
          <AlertTriangle className="h-6 w-6 text-green-600" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No vulnerabilities found</h3>
        <p className="text-gray-500">Great job! Your applications are secure.</p>
      </div>
    );
  }

  const chartData = [
    { name: 'Critical', value: data.critical_count, color: SEVERITY_COLORS.critical },
    { name: 'High', value: data.high_count, color: SEVERITY_COLORS.high },
    { name: 'Medium', value: data.medium_count, color: SEVERITY_COLORS.medium },
    { name: 'Low', value: data.low_count, color: SEVERITY_COLORS.low },
  ].filter(item => item.value > 0);

  const CustomTooltip = ({ active, payload }: { active?: boolean; payload?: Array<{ payload: { name: string; value: number; color: string; total: number } }> }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium">{data.payload.name} Severity</p>
          <p className="text-sm text-gray-600">
            {data.payload.value} vulnerabilities ({((data.payload.value / data.payload.total) * 100).toFixed(1)}%)
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-4">
      {/* Chart */}
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData.map(item => ({ ...item, total: data.total_vulnerabilities }))}
              cx="50%"
              cy="50%"
              innerRadius={40}
              outerRadius={80}
              paddingAngle={2}
              dataKey="value"
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </div>

      {/* Legend */}
      <div className="space-y-2">
        {chartData.map((item) => (
          <div key={item.name} className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: item.color }}
              />
              <span className="text-sm font-medium text-gray-700">{item.name}</span>
            </div>
            <div className="text-right">
              <span className="text-sm font-medium text-gray-900">{item.value}</span>
              <span className="text-xs text-gray-500 ml-1">
                ({((item.value / data.total_vulnerabilities) * 100).toFixed(1)}%)
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Total */}
      <div className="pt-2 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Total</span>
          <span className="text-sm font-bold text-gray-900">
            {data.total_vulnerabilities} vulnerabilities
          </span>
        </div>
      </div>
    </div>
  );
}
