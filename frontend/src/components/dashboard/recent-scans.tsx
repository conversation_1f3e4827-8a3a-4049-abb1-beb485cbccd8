import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { Clock, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { Scan, ScanStatus } from '@/types';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/shared/loading-spinner';
import { cn } from '@/lib/utils';

interface RecentScansProps {
  scans?: Scan[];
  isLoading?: boolean;
}

const statusConfig = {
  [ScanStatus.PENDING]: {
    icon: Clock,
    color: 'bg-yellow-100 text-yellow-800',
    label: 'Pending',
  },
  [ScanStatus.RUNNING]: {
    icon: Loader2,
    color: 'bg-blue-100 text-blue-800',
    label: 'Running',
  },
  [ScanStatus.COMPLETED]: {
    icon: CheckCircle,
    color: 'bg-green-100 text-green-800',
    label: 'Completed',
  },
  [ScanStatus.FAILED]: {
    icon: XCircle,
    color: 'bg-red-100 text-red-800',
    label: 'Failed',
  },
  [ScanStatus.CANCELLED]: {
    icon: XCircle,
    color: 'bg-gray-100 text-gray-800',
    label: 'Cancelled',
  },
};

export function RecentScans({ scans, isLoading }: RecentScansProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-40">
        <LoadingSpinner size="md" text="Loading recent scans..." />
      </div>
    );
  }

  if (!scans || scans.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="mx-auto w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
          <Clock className="h-6 w-6 text-purple-600" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No scans yet</h3>
        <p className="text-gray-500 mb-4">Start your first security scan to see results here.</p>
        <Button className="gradient-primary text-white hover:opacity-90">
          Start Your First Scan
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {scans.map((scan) => {
        const config = statusConfig[scan.status];
        const StatusIcon = config.icon;
        
        return (
          <div
            key={scan.id}
            className="flex items-center justify-between p-4 border border-purple-100 rounded-lg hover:bg-purple-50/50 transition-colors"
          >
            <div className="flex items-center gap-4">
              <div className={cn("p-2 rounded-lg", config.color)}>
                <StatusIcon 
                  className={cn(
                    "h-4 w-4",
                    scan.status === ScanStatus.RUNNING && "animate-spin"
                  )} 
                />
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-gray-900 truncate">
                    {scan.scan_name || `Scan #${scan.id}`}
                  </h4>
                  <Badge variant="outline" className="text-xs">
                    {scan.scan_type.replace('_', ' ')}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <span className="truncate">{scan.target_url}</span>
                  <span>•</span>
                  <span>
                    {formatDistanceToNow(new Date(scan.created_at), { addSuffix: true })}
                  </span>
                </div>
                
                {scan.status === ScanStatus.RUNNING && (
                  <div className="mt-2">
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <span>Progress: {scan.progress_percentage}%</span>
                      <div className="flex-1 bg-gray-200 rounded-full h-1.5 max-w-20">
                        <div 
                          className="gradient-primary h-1.5 rounded-full transition-all duration-300"
                          style={{ width: `${scan.progress_percentage}%` }}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {scan.status === ScanStatus.COMPLETED && (
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {scan.total_vulnerabilities} issues
                  </div>
                  <div className="text-xs text-gray-500">
                    {scan.critical_count + scan.high_count} critical/high
                  </div>
                </div>
              )}
              
              <Button variant="ghost" size="sm" asChild>
                <Link href={`/dashboard/scans/${scan.id}`}>
                  View
                </Link>
              </Button>
            </div>
          </div>
        );
      })}
      
      {scans.length >= 5 && (
        <div className="text-center pt-4">
          <Button variant="outline" asChild>
            <Link href="/dashboard/scans">
              View All Scans
            </Link>
          </Button>
        </div>
      )}
    </div>
  );
}
