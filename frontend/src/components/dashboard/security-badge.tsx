'use client';

import { Shield } from 'lucide-react';
import { SecurityGrade } from '@/lib/grading';

interface SecurityBadgeProps {
  style: 'standard' | 'light';
  size: 'small' | 'medium' | 'large';
  grade: SecurityGrade;
  companyName?: string;
  websiteUrl?: string;
  testCategory?: string;
}

export function SecurityBadge({
  style,
  size,
  grade,
  websiteUrl = 'example.com',
  testCategory = 'Security Audit'
}: SecurityBadgeProps) {
  // Improved sizing with better proportions
  const sizeConfig = {
    small: {
      container: 'w-48 h-12',
      shield: 'h-4 w-4',
      text: 'text-xs',
      subtext: 'text-[10px]',
      gradeText: 'text-sm',
      padding: 'px-3 py-2',
      gap: 'gap-2'
    },
    medium: {
      container: 'w-56 h-14',
      shield: 'h-5 w-5',
      text: 'text-sm',
      subtext: 'text-xs',
      gradeText: 'text-base',
      padding: 'px-4 py-3',
      gap: 'gap-3'
    },
    large: {
      container: 'w-64 h-16',
      shield: 'h-6 w-6',
      text: 'text-base',
      subtext: 'text-sm',
      gradeText: 'text-lg',
      padding: 'px-5 py-3',
      gap: 'gap-3'
    }
  };

  const config = sizeConfig[size];

  // Grade color configuration
  const gradeColors = {
    'A+': 'from-emerald-500 to-green-600',
    'A': 'from-green-500 to-emerald-600',
    'B+': 'from-blue-500 to-indigo-600',
    'B': 'from-blue-500 to-blue-600',
    'C+': 'from-yellow-500 to-orange-500',
    'C': 'from-yellow-500 to-yellow-600',
    'D': 'from-orange-500 to-red-500',
    'F': 'from-red-500 to-red-600'
  };

  const gradeGradient = gradeColors[grade as keyof typeof gradeColors] || 'from-gray-500 to-gray-600';

  // Clean up website URL for display
  const displayUrl = websiteUrl.replace(/^https?:\/\//, '').replace(/\/$/, '');
  const shortUrl = displayUrl.length > 20 ? displayUrl.substring(0, 20) + '...' : displayUrl;

  if (style === 'standard') {
    return (
      <div className={`
        ${config.container}
        ${config.padding}
        bg-gradient-to-r ${gradeGradient} text-white rounded-lg font-medium
        flex items-center justify-between
        shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer
        relative overflow-hidden group
      `}>
        {/* Normal Content */}
        <div className="flex items-center gap-2 flex-1 min-w-0 group-hover:opacity-0 transition-opacity duration-300">
          <Shield className={`${config.shield} flex-shrink-0`} />
          <div className="text-left leading-tight min-w-0">
            <div className={`${config.text} font-bold truncate`}>{shortUrl}</div>
            <div className={`${config.subtext} opacity-90 truncate`}>{testCategory}</div>
          </div>
        </div>
        <div className="bg-white/20 rounded px-2 py-1 ml-2 flex-shrink-0 group-hover:opacity-0 transition-opacity duration-300">
          <div className={`${config.gradeText} font-black leading-none`}>{grade}</div>
        </div>

        {/* Hover Content */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="text-center">
            <div className={`${config.text} font-bold animate-pulse`}>AUDITED BY</div>
            <div className={`${config.subtext} opacity-90 font-medium`}>MostlySecure</div>
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div className={`
        ${config.container}
        ${config.padding}
        bg-white border-2 border-gray-200 rounded-lg font-medium
        flex items-center justify-between
        shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer
        text-gray-800 relative overflow-hidden group
        hover:border-purple-300
      `}>
        {/* Normal Content */}
        <div className="flex items-center gap-2 flex-1 min-w-0 group-hover:opacity-0 transition-opacity duration-300">
          <Shield className={`${config.shield} text-gray-600 flex-shrink-0`} />
          <div className="text-left leading-tight min-w-0">
            <div className={`${config.text} font-bold text-gray-800 truncate`}>{shortUrl}</div>
            <div className={`${config.subtext} text-gray-600 truncate`}>{testCategory}</div>
          </div>
        </div>
        <div className={`bg-gradient-to-r ${gradeGradient} text-white rounded px-2 py-1 ml-2 flex-shrink-0 group-hover:opacity-0 transition-opacity duration-300`}>
          <div className={`${config.gradeText} font-black leading-none`}>{grade}</div>
        </div>

        {/* Hover Content */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="text-center">
            <div className={`${config.text} font-bold text-purple-600 animate-pulse`}>AUDITED BY</div>
            <div className={`${config.subtext} text-purple-500 font-medium`}>MostlySecure</div>
          </div>
        </div>
      </div>
    );
  }
}