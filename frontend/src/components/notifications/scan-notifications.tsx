'use client';

import { useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';
import { toast } from 'sonner';
import { CheckCircle, XCircle, AlertTriangle, Info } from 'lucide-react';

interface ScanNotificationsProps {
  userId?: string;
}

export function ScanNotifications({ userId }: ScanNotificationsProps) {
  const queryClient = useQueryClient();

  // Poll for new notifications
  const { data: notifications } = useQuery({
    queryKey: ['notifications', 'unread'],
    queryFn: () => apiClient.getNotifications({ 
      unread_only: true, 
      limit: 10,
      notification_type: 'scan_completion'
    }),
    refetchInterval: 5000, // Check every 5 seconds
    enabled: !!userId,
  });

  // Handle new notifications
  useEffect(() => {
    if (!notifications || !Array.isArray(notifications)) return;

    notifications.forEach((notification: unknown) => {
      const notif = notification as Record<string, unknown>;
      // Check if we've already shown this notification
      const notificationKey = `notification_${notif.id}`;
      const hasShown = sessionStorage.getItem(notificationKey);

      if (!hasShown) {
        // Mark as shown
        sessionStorage.setItem(notificationKey, 'true');

        // Show appropriate toast based on notification type
        switch (notif.type) {
          case 'scan_completion':
            if (String(notif.title).includes('completed successfully')) {
              toast.success(String(notif.title), {
                description: String(notif.message),
                duration: 5000,
                icon: <CheckCircle className="h-4 w-4" />,
                action: {
                  label: 'View Scan',
                  onClick: () => {
                    if (notif.scan_id) {
                      window.location.href = `/dashboard/scans/${notif.scan_id}`;
                    }
                  },
                },
              });
            } else if (String(notif.title).includes('failed')) {
              toast.error(String(notif.title), {
                description: String(notif.message),
                duration: 7000,
                icon: <XCircle className="h-4 w-4" />,
                action: {
                  label: 'View Details',
                  onClick: () => {
                    if (notif.scan_id) {
                      window.location.href = `/dashboard/scans/${notif.scan_id}`;
                    }
                  },
                },
              });
            }
            break;
            
          case 'vulnerability_alert':
            toast.warning(String(notif.title), {
              description: String(notif.message),
              duration: 6000,
              icon: <AlertTriangle className="h-4 w-4" />,
              action: {
                label: 'View Vulnerabilities',
                onClick: () => {
                  if (notif.scan_id) {
                    window.location.href = `/dashboard/scans/${notif.scan_id}`;
                  }
                },
              },
            });
            break;

          default:
            toast.info(String(notif.title), {
              description: String(notif.message),
              duration: 4000,
              icon: <Info className="h-4 w-4" />,
            });
        }

        // Mark notification as read after showing
        setTimeout(() => {
          apiClient.markNotificationRead(Number(notif.id)).catch(() => {
            // Silently fail - not critical
          });
        }, 1000);
      }
    });
  }, [notifications]);

  // Auto-refresh related queries when notifications come in
  useEffect(() => {
    if (notifications && notifications.length > 0) {
      // Refresh scans data
      queryClient.invalidateQueries({ queryKey: ['scans'] });
      queryClient.invalidateQueries({ queryKey: ['scan-summary'] });
      queryClient.invalidateQueries({ queryKey: ['vulnerability-summary'] });
    }
  }, [notifications, queryClient]);

  // This component doesn't render anything visible
  return null;
}

// Hook for managing scan notifications
export function useScanNotifications() {
  const queryClient = useQueryClient();

  const showScanStartedNotification = (scanName: string, scanId: number) => {
    toast.info(`Scan "${scanName}" started`, {
      description: 'Your security scan is now running...',
      duration: 3000,
      icon: <Info className="h-4 w-4" />,
      action: {
        label: 'View Progress',
        onClick: () => {
          window.location.href = `/dashboard/scans/${scanId}`;
        },
      },
    });
  };

  const showScanCompletedNotification = (scanName: string, scanId: number, vulnerabilityCount: number) => {
    toast.success(`Scan "${scanName}" completed!`, {
      description: `Found ${vulnerabilityCount} vulnerabilities`,
      duration: 5000,
      icon: <CheckCircle className="h-4 w-4" />,
      action: {
        label: 'View Results',
        onClick: () => {
          window.location.href = `/dashboard/scans/${scanId}`;
        },
      },
    });
    
    // Refresh all scan-related data
    queryClient.invalidateQueries({ queryKey: ['scans'] });
    queryClient.invalidateQueries({ queryKey: ['scan', scanId.toString()] });
    queryClient.invalidateQueries({ queryKey: ['vulnerabilities', scanId.toString()] });
  };

  const showScanFailedNotification = (scanName: string, scanId: number, errorMessage?: string) => {
    toast.error(`Scan "${scanName}" failed`, {
      description: errorMessage || 'An error occurred during scanning',
      duration: 7000,
      icon: <XCircle className="h-4 w-4" />,
      action: {
        label: 'View Details',
        onClick: () => {
          window.location.href = `/dashboard/scans/${scanId}`;
        },
      },
    });
    
    // Refresh scan data
    queryClient.invalidateQueries({ queryKey: ['scans'] });
    queryClient.invalidateQueries({ queryKey: ['scan', scanId.toString()] });
  };

  const showVulnerabilityAlertNotification = (scanName: string, scanId: number, criticalCount: number, highCount: number) => {
    if (criticalCount > 0 || highCount > 0) {
      toast.warning(`High-priority vulnerabilities found in "${scanName}"`, {
        description: `${criticalCount} critical and ${highCount} high severity issues detected`,
        duration: 8000,
        icon: <AlertTriangle className="h-4 w-4" />,
        action: {
          label: 'Review Now',
          onClick: () => {
            window.location.href = `/dashboard/scans/${scanId}`;
          },
        },
      });
    }
  };

  return {
    showScanStartedNotification,
    showScanCompletedNotification,
    showScanFailedNotification,
    showVulnerabilityAlertNotification,
  };
}
