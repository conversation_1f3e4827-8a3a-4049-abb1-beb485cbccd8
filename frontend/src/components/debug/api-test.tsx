'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ApiTestResult {
  status?: number;
  statusText?: string;
  ok?: boolean;
  data?: unknown;
  headers?: Record<string, string>;
  error?: string;
  type?: string;
}

export function ApiTest() {
  const [results, setResults] = useState<Record<string, ApiTestResult>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  const testEndpoint = async (name: string, endpoint: string, options?: RequestInit) => {
    setLoading(prev => ({ ...prev, [name]: true }));
    
    try {
      const response = await fetch(endpoint, options);
      const data = await response.json().catch(() => null);
      
      setResults(prev => ({
        ...prev,
        [name]: {
          status: response.status,
          statusText: response.statusText,
          ok: response.ok,
          data: data,
          headers: Object.fromEntries(response.headers.entries())
        }
      }));
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [name]: {
          error: error instanceof Error ? error.message : 'Unknown error',
          type: error instanceof Error ? error.name : 'Error'
        }
      }));
    } finally {
      setLoading(prev => ({ ...prev, [name]: false }));
    }
  };

  const runTests = () => {
    // Test various endpoints
    testEndpoint('GET /api/scans', '/api/scans');
    testEndpoint('GET /api/auth/session', '/api/auth/session');
    testEndpoint('GET /api/health', '/api/health');
  };

  return (
    <Card className="w-full max-w-4xl mx-auto mt-8">
      <CardHeader>
        <CardTitle>API Endpoint Tester</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={runTests} className="w-full">
          Run API Tests
        </Button>
        
        {Object.entries(results).map(([name, result]) => (
          <div key={name} className="border rounded p-4 space-y-2">
            <h3 className="font-semibold">{name}</h3>
            <pre className="text-xs overflow-auto bg-gray-100 p-2 rounded">
              {JSON.stringify(result, null, 2)}
            </pre>
            {loading[name] && <p className="text-sm text-gray-500">Loading...</p>}
          </div>
        ))}
      </CardContent>
    </Card>
  );
}