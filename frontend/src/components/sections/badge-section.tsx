'use client';

import { motion } from 'framer-motion';
import { Shield, Star, Code } from 'lucide-react';
import { Button } from '@/components/ui/button';

const badges = [
  {
    type: 'Standard Badge',
    description: 'Eye-catching purple design that stands out on any website',
    features: [
      'Bold purple background',
      'Responsive design that scales perfectly',
      'Instant credibility boost for your users'
    ],
    preview: true
  },
  {
    type: 'Light Badge',
    description: 'Transparent outline design that adapts to any background',
    features: [
      'Transparent design fits any color scheme',
      'Clean outline style for minimalist sites',
      'Professional appearance that builds trust'
    ],
    preview: false
  }
];

const steps = [
  {
    number: '1',
    title: 'Unlock Your Trust Badge',
    description: 'Purchase a paid scanning plan to unlock badge access',
    icon: Star
  },
  {
    number: '2',
    title: 'Achieve Grade A',
    description: 'Scan your website and fix issues to earn an A security rating',
    icon: Shield
  },
  {
    number: '3',
    title: 'Copy/Paste to Embed',
    description: 'Get your personalized badge code and add it to your site',
    icon: Code
  }
];

export function BadgeSection() {
  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50">
      <div className="mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-purple-500/10 text-purple-400 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Star className="h-4 w-4" />
            TRUST BADGES
          </div>
          <h2 className="text-4xl font-display mb-4 text-balance">
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Showcase Your Security Excellence</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Display your Grade A security certification with pride. Boost user confidence by embedding your verified security badge on your website.
          </p>
        </motion.div>

        {/* Badge Previews */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {badges.map((badge, index) => (
            <motion.div
              key={badge.type}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.2 }}
              className="bg-white border border-gray-200 rounded-2xl p-8 shadow-sm"
            >
              {/* Badge Preview */}
              <div className="mb-8 flex justify-center">
                {badge.preview ? (
                  // Standard Badge
                  <div className="gradient-primary text-white px-6 py-3 rounded-lg font-medium flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    <div className="text-left">
                      <div className="text-xs opacity-90">AUDITED BY</div>
                      <div className="text-sm font-bold">MostlySecure</div>
                    </div>
                    <div className="bg-white text-purple-600 w-8 h-8 rounded-full flex items-center justify-center font-bold text-lg ml-2">
                      A
                    </div>
                  </div>
                ) : (
                  // Light Badge
                  <div className="border-2 border-purple-400 text-purple-400 px-6 py-3 rounded-lg font-medium flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    <div className="text-left">
                      <div className="text-xs opacity-90">AUDITED BY</div>
                      <div className="text-sm font-bold">MostlySecure</div>
                    </div>
                    <div className="border border-purple-400 text-purple-400 w-8 h-8 rounded-full flex items-center justify-center font-bold text-lg ml-2">
                      A
                    </div>
                  </div>
                )}
              </div>

              {/* Badge Info */}
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{badge.type}</h3>
              <p className="text-gray-600 mb-6">{badge.description}</p>

              {/* Features */}
              <ul className="space-y-3">
                {badge.features.map((feature, idx) => (
                  <li key={idx} className="flex items-center gap-3 text-gray-700">
                    <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                    {feature}
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* How to Get Badge */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="bg-white/90 backdrop-blur border border-purple-200 rounded-2xl p-8 lg:p-12 shadow-lg"
        >
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-purple-500/10 text-purple-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
              <div className="w-6 h-6 gradient-primary rounded-full flex items-center justify-center">
                <span className="text-white text-xs">✓</span>
              </div>
              <span className="font-semibold">How to Get Your Badge</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {steps.map((step, index) => (
              <motion.div
                key={step.number}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.2 }}
                className="text-center"
              >
                <div className="w-16 h-16 gradient-primary text-white rounded-2xl flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                  {step.number}
                </div>
                <div className="mb-4">
                  <step.icon className="h-8 w-8 text-purple-500 mx-auto" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
              </motion.div>
            ))}
          </div>

          <div className="text-center">
            <Button
              size="lg"
              className="gradient-primary hover:opacity-90 text-white px-8 py-4 text-lg"
            >
              <Star className="h-5 w-5 mr-2" />
              Get Your Badge
            </Button>
            <p className="text-gray-500 text-sm mt-4">
              Available exclusively for paying customers who achieve top security scores.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}