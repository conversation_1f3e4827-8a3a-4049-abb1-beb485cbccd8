'use client';

import { motion } from 'framer-motion';
import { Key, Shield, Flame, Unlock, <PERSON>, Package } from 'lucide-react';

const vulnerabilities = [
  {
    icon: Key,
    title: 'Exposed Secrets',
    description: 'API keys, tokens, and credentials leaked in your frontend bundle',
    color: 'from-yellow-500 to-orange-500'
  },
  {
    icon: Shield,
    title: 'Database Policies',
    description: 'Missing or misconfigured RLS in Supabase tables',
    color: 'from-blue-500 to-cyan-500'
  },
  {
    icon: Flame,
    title: 'Firebase Rules',
    description: 'Insecure read/write permissions on your Firestore',
    color: 'from-red-500 to-pink-500'
  },
  {
    icon: Unlock,
    title: 'Open Endpoints',
    description: 'Unprotected API routes exposing sensitive operations',
    color: 'from-purple-500 to-violet-500'
  },
  {
    icon: Link,
    title: 'Webhook Security',
    description: 'Missing signature verification on incoming webhooks',
    color: 'from-emerald-500 to-teal-500'
  },
  {
    icon: Package,
    title: 'ENV Variables',
    description: 'Production secrets exposed through environment configs',
    color: 'from-indigo-500 to-blue-500'
  }
];

export function VulnerabilitiesSection() {
  return (
    <section className="py-20 bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50">
      <div className="mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-display mb-4 text-balance">
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Zero In On Critical Threats</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our advanced scanner catches the security vulnerabilities that slip through even the most careful code reviews
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {vulnerabilities.map((vuln, index) => (
            <motion.div
              key={vuln.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className="group"
            >
              <div className="bg-white/80 backdrop-blur border border-gray-200 rounded-2xl p-8 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                {/* Icon */}
                <div className="mb-6">
                  <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${vuln.color} flex items-center justify-center`}>
                    <vuln.icon className="h-8 w-8 text-white" />
                  </div>
                </div>

                {/* Content */}
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {vuln.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {vuln.description}
                </p>

                {/* Hover indicator */}
                <div className={`h-1 bg-gradient-to-r ${vuln.color} rounded-full mt-6 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300`}></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.8 }}
          className="text-center mt-16"
        >
          <div className="bg-white/50 backdrop-blur border border-gray-200 rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Stop Threats Before They Start</span>
            </h3>
            <p className="text-gray-600 mb-6">
              Get comprehensive security analysis in under a minute
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <div className="flex-1 max-w-md">
                <input
                  type="url"
                  placeholder="https://your-app.com"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none"
                />
              </div>
              <button className="px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-lg hover:opacity-90 transition-opacity">
                Scan Now
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}