'use client';

import { motion } from 'framer-motion';
import { Globe, Search, Award } from 'lucide-react';
import { Button } from '@/components/ui/button';

const steps = [
  {
    number: '01',
    title: 'Drop your URL',
    description: 'No API keys. No installation. Just paste your deployed app\'s URL and we\'ll handle the rest. Works with any publicly accessible web app.',
    icon: Globe,
    demo: 'https://your-awesome-app.com'
  },
  {
    number: '02', 
    title: 'Intelligent scan',
    description: 'Our engine automatically detects your tech stack and runs targeted security checks. From exposed secrets to misconfigured DB policies.',
    icon: Search,
    demo: 'Checking 42 vulnerability patterns...'
  },
  {
    number: '03',
    title: 'Security grade + badge',
    description: 'Get a detailed report with exact locations and severity levels. No security expertise required, we explain everything in plain English.',
    icon: Award,
    demo: 'Grade A - MostlySecure Verified'
  }
];

export function ProcessSection() {
  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50">
      <div className="mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-display mb-4 text-balance">
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Security Made Simple</span>
          </h2>
          <p className="text-xl text-gray-600">
            Three steps to bulletproof security
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 lg:gap-8">
          {steps.map((step, index) => (
            <motion.div
              key={step.number}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.2 }}
              className="text-center lg:text-left"
            >
              {/* Step Number */}
              <div className="mb-6">
                <span className="text-6xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  {step.number}
                </span>
              </div>

              {/* Icon */}
              <div className="mb-6 flex justify-center lg:justify-start">
                <div className="w-16 h-16 rounded-2xl gradient-primary flex items-center justify-center">
                  <step.icon className="h-8 w-8 text-white" />
                </div>
              </div>

              {/* Content */}
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                {step.title}
              </h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                {step.description}
              </p>

              {/* Demo */}
              <div className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
                {index === 0 && (
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                    <input
                      type="text"
                      value={step.demo}
                      readOnly
                      className="bg-transparent text-gray-700 flex-1 outline-none"
                    />
                  </div>
                )}
                {index === 1 && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-purple-400 text-sm">
                      <div className="w-2 h-2 rounded-full bg-purple-400 animate-pulse"></div>
                      Scanning...
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <motion.div 
                        className="gradient-primary h-2 rounded-full"
                        initial={{ width: 0 }}
                        whileInView={{ width: '75%' }}
                        viewport={{ once: true }}
                        transition={{ duration: 2, delay: 0.5 }}
                      />
                    </div>
                    <div className="text-gray-600 text-sm">{step.demo}</div>
                  </div>
                )}
                {index === 2 && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded bg-red-500 flex items-center justify-center">
                        <span className="text-white text-xs">!</span>
                      </div>
                      <span className="text-red-400 text-sm">Critical: API key exposed in bundle.js:142</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded bg-yellow-500 flex items-center justify-center">
                        <span className="text-white text-xs">!</span>
                      </div>
                      <span className="text-yellow-400 text-sm">Warning: RLS policies misconfigured on 2 tables</span>
                    </div>
                    <div className="mt-3 text-purple-400 text-sm font-medium">{step.demo}</div>
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.8 }}
          className="text-center mt-12"
        >
          <Button
            size="lg"
            className="gradient-primary hover:opacity-90 text-white"
          >
            Start Your First Scan
          </Button>
        </motion.div>
      </div>
    </section>
  );
}