'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown } from 'lucide-react';

interface FAQItem {
  question: string;
  answer: string;
}

const faqData: FAQItem[] = [
  {
    question: 'What security vulnerabilities does MostlySecure detect?',
    answer: 'MostlySecure detects exposed secrets, database policy misconfigurations, insecure Firebase rules, unprotected API endpoints, webhook security issues, and exposed environment variables. Our comprehensive scanning covers the most critical security vulnerabilities that affect modern web applications.'
  },
  {
    question: 'Is MostlySecure safe to use on my production applications?',
    answer: 'Yes, MostlySecure is designed to be completely safe for production use. Our scanners only perform read-only analysis and never modify your application or data. We use secure, encrypted connections and follow strict data protection protocols.'
  },
  {
    question: 'How long does a security scan take?',
    answer: 'Most scans complete within 30-60 seconds, depending on your application size and complexity. Our async processing system ensures fast, efficient scanning without impacting your application performance.'
  },
  {
    question: 'What happens to my data during the scan?',
    answer: 'Your data security is our priority. We only access publicly available endpoints and never store sensitive information. All scan data is encrypted in transit and at rest, and results are automatically deleted after 30 days unless you choose to save them.'
  },
  {
    question: 'Do I need to install anything to use MostlySecure?',
    answer: 'No installation required! Simply provide your application URL and we handle the rest. MostlySecure works entirely through secure web-based scanning, making it easy to get started immediately.'
  },
  {
    question: 'What is the security badge system?',
    answer: 'Our badge system allows you to display your security grade on your website, building trust with users. After achieving a Grade A security score with a paid plan, you can embed our verified badge to showcase your commitment to security.'
  },
  {
    question: 'How do I get access to the trust badges?',
    answer: 'Trust badges are available exclusively for paying customers who achieve a Grade A security rating. Simply upgrade to a paid scanning plan, fix any issues to earn your A grade, then copy and paste the badge code into your website.'
  },
  {
    question: 'Can I customize the appearance of my security badge?',
    answer: 'Yes! We offer two badge styles: the Standard Badge with a bold purple background for maximum visibility, and the Light Badge with a transparent outline design that adapts to any background color scheme.'
  },
  {
    question: 'Does the security badge update automatically?',
    answer: 'Yes, your security badge reflects your current grade in real-time. If your security score changes due to new vulnerabilities or fixes, the badge will automatically update to show your current rating.'
  }
];

export function FAQSection() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-20 bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50">
      <div className="mx-auto max-w-4xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-display mb-4 text-balance">
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Questions & Answers</span>
          </h2>
          <p className="text-gray-600 text-lg">
            Everything you need to know about securing your applications
          </p>
        </motion.div>

        <div className="space-y-4">
          {faqData.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className="border border-purple-200 rounded-lg bg-white/80 backdrop-blur overflow-hidden shadow-sm"
            >
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-purple-50 transition-colors"
              >
                <span className="text-gray-900 font-medium text-lg">
                  {faq.question}
                </span>
                <motion.div
                  animate={{ rotate: openIndex === index ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <ChevronDown className="h-5 w-5 text-purple-400" />
                </motion.div>
              </button>
              
              <AnimatePresence>
                {openIndex === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3, ease: 'easeInOut' }}
                    className="overflow-hidden"
                  >
                    <div className="px-6 pb-4 text-gray-600 leading-relaxed">
                      {faq.answer}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}