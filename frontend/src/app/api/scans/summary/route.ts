import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get scan counts by status
    const { data: statusCounts, error: statusError } = await supabase
      .from('scans')
      .select('status')
      .eq('user_id', user.id)

    if (statusError) {
      console.error('Error fetching scan status counts:', statusError)
      return NextResponse.json({ error: 'Failed to fetch scan summary' }, { status: 500 })
    }

    // Calculate counts
    const summary = {
      total_scans: statusCounts?.length || 0,
      completed_scans: 0,
      running_scans: 0,
      failed_scans: 0,
      pending_scans: 0
    }

    statusCounts?.forEach(scan => {
      switch (scan.status) {
        case 'completed':
          summary.completed_scans++
          break
        case 'running':
          summary.running_scans++
          break
        case 'failed':
          summary.failed_scans++
          break
        case 'pending':
        case 'queued':
          summary.pending_scans++
          break
      }
    })

    // Get recent scans
    const { data: recentScans } = await supabase
      .from('scans')
      .select('id, scan_name, target_url, status, created_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(5)

    return NextResponse.json({
      ...summary,
      recent_scans: recentScans || []
    })
  } catch (error) {
    console.error('Error in GET /api/scans/summary:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}