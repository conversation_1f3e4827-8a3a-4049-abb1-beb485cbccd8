import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // First get user's scan IDs
    const { data: userScans, error: scansError } = await supabase
      .from('scans')
      .select('id')
      .eq('user_id', user.id)

    if (scansError) {
      console.error('Error fetching user scans:', scansError)
      return NextResponse.json({ error: 'Failed to fetch user scans' }, { status: 500 })
    }

    const scanIds = userScans?.map(scan => scan.id) || []

    // Get all vulnerabilities for user's scans
    const { data: vulnerabilities, error: vulnError } = await supabase
      .from('vulnerabilities')
      .select('severity, owasp_category, scan_id')
      .in('scan_id', scanIds)
      .eq('is_false_positive', false)

    if (vulnError) {
      console.error('Error fetching vulnerabilities:', vulnError)
      return NextResponse.json({ error: 'Failed to fetch vulnerability summary' }, { status: 500 })
    }

    // Calculate severity counts
    const severityCounts = {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
      info: 0
    }

    const categoryBreakdown: Record<string, number> = {}
    const uniqueScansWithVulnerabilities = new Set<string>()

    vulnerabilities?.forEach(vuln => {
      // Count by severity
      if (vuln.severity in severityCounts) {
        severityCounts[vuln.severity as keyof typeof severityCounts]++
      }

      // Count by category
      if (vuln.owasp_category) {
        categoryBreakdown[vuln.owasp_category] = (categoryBreakdown[vuln.owasp_category] || 0) + 1
      }

      // Track unique scans
      uniqueScansWithVulnerabilities.add(vuln.scan_id)
    })

    // Sort categories by count
    const topCategories = Object.entries(categoryBreakdown)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([category, count]) => ({ category, count }))

    return NextResponse.json({
      total_vulnerabilities: vulnerabilities?.length || 0,
      severity_breakdown: severityCounts,
      top_categories: topCategories,
      affected_scans: uniqueScansWithVulnerabilities.size
    })
  } catch (error) {
    console.error('Error in GET /api/scans/vulnerabilities/summary:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}