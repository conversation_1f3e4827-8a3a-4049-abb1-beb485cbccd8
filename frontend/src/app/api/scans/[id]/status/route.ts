import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch scan status
    const { data: scan, error } = await supabase
      .from('scans')
      .select('id, status, progress, created_at, started_at, completed_at')
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (error || !scan) {
      return NextResponse.json({ error: 'Scan not found' }, { status: 404 })
    }

    return NextResponse.json({
      scan_id: scan.id,
      status: scan.status,
      progress_percentage: scan.progress,
      created_at: scan.created_at,
      started_at: scan.started_at,
      completed_at: scan.completed_at
    })
  } catch (error) {
    console.error('Error in GET /api/scans/[id]/status:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}