import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if scan exists and can be cancelled
    const { data: scan, error: fetchError } = await supabase
      .from('scans')
      .select('id, status')
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (fetchError || !scan) {
      return NextResponse.json({ error: 'Scan not found' }, { status: 404 })
    }

    // Only allow cancelling pending, queued, or running scans
    if (!['pending', 'queued', 'running'].includes(scan.status)) {
      return NextResponse.json(
        { error: `Cannot cancel scan with status: ${scan.status}` },
        { status: 400 }
      )
    }

    // Update scan status to cancelled
    const { data: updatedScan, error: updateError } = await supabase
      .from('scans')
      .update({
        status: 'cancelled',
        completed_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (updateError) {
      console.error('Error cancelling scan:', updateError)
      return NextResponse.json({ error: 'Failed to cancel scan' }, { status: 500 })
    }

    // TODO: Send cancellation signal to edge function if running

    return NextResponse.json(updatedScan)
  } catch (error) {
    console.error('Error in POST /api/scans/[id]/cancel:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}