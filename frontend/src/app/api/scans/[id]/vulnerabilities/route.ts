import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify scan belongs to user
    const { data: scan, error: scanError } = await supabase
      .from('scans')
      .select('id')
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (scanError || !scan) {
      return NextResponse.json({ error: 'Scan not found' }, { status: 404 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const severity = searchParams.get('severity')
    const category = searchParams.get('category')
    const limit = parseInt(searchParams.get('limit') || '50', 10)
    const offset = parseInt(searchParams.get('skip') || '0', 10)

    // Build query
    let query = supabase
      .from('vulnerabilities')
      .select('*')
      .eq('scan_id', id)
      .order('severity', { ascending: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (severity) {
      query = query.eq('severity', severity)
    }
    if (category) {
      query = query.eq('owasp_category', category)
    }

    const { data: vulnerabilities, error } = await query

    if (error) {
      console.error('Error fetching vulnerabilities:', error)
      return NextResponse.json({ error: 'Failed to fetch vulnerabilities' }, { status: 500 })
    }

    return NextResponse.json(vulnerabilities || [])
  } catch (error) {
    console.error('Error in GET /api/scans/[id]/vulnerabilities:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}