import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'
import type { Database } from '@/types/supabase'

type CreateScanInput = Database['public']['Tables']['scans']['Insert']

export async function GET(request: Request) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const scanType = searchParams.get('scan_type')
    const limit = parseInt(searchParams.get('limit') || '10', 10)
    const offset = parseInt(searchParams.get('skip') || '0', 10)

    // Build query
    let query = supabase
      .from('scans')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (status) {
      query = query.eq('status', status)
    }
    if (scanType) {
      query = query.eq('scan_type', scanType)
    }

    const { data: scans, error } = await query

    if (error) {
      console.error('Error fetching scans:', error)
      return NextResponse.json({ error: 'Failed to fetch scans' }, { status: 500 })
    }

    return NextResponse.json(scans || [])
  } catch (error) {
    console.error('Unexpected error in GET /api/scans:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { scan_name, scan_description, target_url, scan_type, config } = body

    // Validate required fields
    if (!target_url || !scan_type) {
      return NextResponse.json(
        { error: 'Missing required fields: target_url, scan_type' },
        { status: 400 }
      )
    }
    
    // Use a default scan name if not provided
    const finalScanName = scan_name || `${scan_type.replace(/_/g, ' ')} scan - ${new Date().toLocaleDateString()}`

    // Validate scan type
    const validScanTypes = ['api_endpoints', 'database_security', 'secrets', 'firebase', 'webhooks', 'comprehensive']
    if (!validScanTypes.includes(scan_type)) {
      return NextResponse.json(
        { error: `Invalid scan_type. Must be one of: ${validScanTypes.join(', ')}` },
        { status: 400 }
      )
    }

    // Check concurrent scan limit
    const { count: activeScanCount } = await supabase
      .from('scans')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .in('status', ['pending', 'queued', 'running'])

    // Get user's max concurrent scans limit
    const { data: profile } = await supabase
      .from('profiles')
      .select('max_concurrent_scans')
      .eq('id', user.id)
      .single()

    const maxConcurrentScans = profile?.max_concurrent_scans || 5

    if (activeScanCount && activeScanCount >= maxConcurrentScans) {
      return NextResponse.json(
        { error: `Maximum concurrent scans limit (${maxConcurrentScans}) reached` },
        { status: 429 }
      )
    }

    // Create scan
    const scanData: CreateScanInput = {
      user_id: user.id,
      scan_name: finalScanName,
      scan_description: scan_description || '',
      target_url,
      scan_type: scan_type as CreateScanInput['scan_type'],
      config: config || {},
      status: 'queued',
      progress: 0
    }

    const { data: scan, error: createError } = await supabase
      .from('scans')
      .insert(scanData)
      .select()
      .single()

    if (createError) {
      console.error('Error creating scan:', createError)
      return NextResponse.json({ error: 'Failed to create scan' }, { status: 500 })
    }

    // Invoke scanner edge function
    const { error: invokeError } = await supabase.functions.invoke('scan-orchestrator', {
      body: {
        scanId: scan.id,
        scanType: scan.scan_type,
        targetUrl: scan.target_url,
        config: scan.config
      }
    })

    if (invokeError) {
      console.error('Error invoking scanner:', invokeError)
      // Update scan status to failed
      await supabase
        .from('scans')
        .update({ status: 'failed' })
        .eq('id', scan.id)
        
      return NextResponse.json(
        { error: 'Failed to start scan' },
        { status: 500 }
      )
    }

    return NextResponse.json(scan, { status: 201 })
  } catch (error) {
    console.error('Unexpected error in POST /api/scans:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}