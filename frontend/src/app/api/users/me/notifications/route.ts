import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function PATCH(request: Request) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const {
      email_notifications,
      browser_notifications,
      scan_completion_alerts,
      vulnerability_alerts,
      weekly_reports
    } = body

    // Update notification preferences
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        email_notifications,
        browser_notifications,
        scan_completion_alerts,
        vulnerability_alerts,
        weekly_reports
      })
      .eq('id', user.id)

    if (updateError) {
      console.error('Error updating notification preferences:', updateError)
      return NextResponse.json({ error: 'Failed to update preferences' }, { status: 500 })
    }

    return NextResponse.json({ message: 'Notification preferences updated successfully' })
  } catch (error) {
    console.error('Error in PATCH /api/users/me/notifications:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}