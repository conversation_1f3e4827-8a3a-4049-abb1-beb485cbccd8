import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Verify cron secret
    const authHeader = request.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = await createClient()
    
    // Clean up old completed scans (older than 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    const { error: deleteScansError } = await supabase
      .from('scans')
      .delete()
      .lt('created_at', thirtyDaysAgo.toISOString())
      .eq('status', 'completed')

    if (deleteScansError) {
      console.error('Error deleting old scans:', deleteScansError)
    }

    // Clean up read notifications older than 7 days
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    
    const { error: deleteNotificationsError } = await supabase
      .from('notifications')
      .delete()
      .lt('created_at', sevenDaysAgo.toISOString())
      .eq('is_read', true)

    if (deleteNotificationsError) {
      console.error('Error deleting old notifications:', deleteNotificationsError)
    }

    // Clean up old API usage logs (older than 90 days)
    const ninetyDaysAgo = new Date()
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90)
    
    const { error: deleteApiLogsError } = await supabase
      .from('api_usage')
      .delete()
      .lt('created_at', ninetyDaysAgo.toISOString())

    if (deleteApiLogsError) {
      console.error('Error deleting old API logs:', deleteApiLogsError)
    }

    return NextResponse.json({ 
      success: true,
      message: 'Cleanup completed successfully',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Cleanup cron error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}