'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  Shield, 
  Copy, 
  Download, 
  Eye, 
  Code, 
  Palette,
  CheckCircle,
  AlertTriangle,
  TrendingUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { useAuthStore } from '@/stores/auth';
import { apiClient } from '@/lib/api';
import { calculateSecurityGrade, getSecurityGradeInfo, getBadgeEligibilityStatus, type VulnerabilityCount, type SecurityGrade } from '@/lib/grading';
import { SecurityBadge } from '@/components/dashboard/security-badge';
import { LoadingSpinner } from '@/components/shared/loading-spinner';

type BadgeStyle = 'standard' | 'light';
type BadgeSize = 'small' | 'medium' | 'large';

interface BadgeConfig {
  style: BadgeStyle;
  size: BadgeSize;
  grade: string;
  companyName: string;
}

export default function BadgePage() {
  const [badgeConfig, setBadgeConfig] = useState<BadgeConfig>({
    style: 'standard',
    size: 'medium',
    grade: 'A',
    companyName: 'Your Organization'
  });
  const [showPreview, setShowPreview] = useState(true);
  const [selectedWebsite, setSelectedWebsite] = useState<string>('');
  const [selectedScanType, setSelectedScanType] = useState<string>('overall');
  const { user, profile } = useAuthStore();

  // Fetch user's scan data to calculate real grade
  const { data: scans, isLoading: scansLoading, error: scansError } = useQuery({
    queryKey: ['scans'],
    queryFn: () => apiClient.getScans({ limit: 100 }),
    enabled: !!user,
  });

  // Debug: Log the scan data to see what we're getting
  React.useEffect(() => {
    if (scans) {
      console.log('Badge page - All scans:', scans);
      console.log('Badge page - Scan statuses:', scans.map((scan: { id: number; status: string }) => ({ id: scan.id, status: scan.status })));
      console.log('Badge page - Completed scans:', scans.filter((scan: { status: string }) => scan.status === 'COMPLETED'));
      console.log('Badge page - All possible statuses:', [...new Set(scans.map((scan: { status: string }) => scan.status))]);
    }
    if (scansError) {
      console.error('Badge page - Scans error:', scansError);
    }
  }, [scans, scansError]);

  // Calculate real security grade based on scan data
  const securityData = React.useMemo(() => {
    if (!scans || scans.length === 0) {
      return {
        grade: 'N/A' as SecurityGrade,
        vulnerabilities: { critical: 0, high: 0, medium: 0, low: 0, info: 0 },
        totalScans: 0,
        completedScans: 0,
        eligibility: getBadgeEligibilityStatus({ critical: 0, high: 0, medium: 0, low: 0, info: 0 }, 0)
      };
    }

    // Filter only completed scans (status is lowercase from API)
    const completedScans = scans.filter((scan: { status: string }) => scan.status === 'completed');

    console.log('Badge page - Completed scans for calculation:', completedScans);

    if (completedScans.length === 0) {
      return {
        grade: 'N/A' as SecurityGrade,
        vulnerabilities: { critical: 0, high: 0, medium: 0, low: 0, info: 0 },
        totalScans: scans.length,
        completedScans: 0,
        eligibility: getBadgeEligibilityStatus({ critical: 0, high: 0, medium: 0, low: 0, info: 0 }, 0),
        scanTypeGrades: {}
      };
    }

    // Calculate grades per scan type
    const scanTypeGrades: Record<string, { grade: SecurityGrade; vulnerabilities: VulnerabilityCount; scanCount: number }> = {};

    completedScans.forEach((scan: { scan_type: string; critical_count?: number; high_count?: number; medium_count?: number; low_count?: number; info_count?: number }) => {
      const scanType = scan.scan_type;
      if (!scanTypeGrades[scanType]) {
        scanTypeGrades[scanType] = {
          grade: 'A+' as SecurityGrade,
          vulnerabilities: { critical: 0, high: 0, medium: 0, low: 0, info: 0 },
          scanCount: 0
        };
      }

      // Aggregate vulnerabilities for this scan type
      scanTypeGrades[scanType].vulnerabilities.critical += scan.critical_count || 0;
      scanTypeGrades[scanType].vulnerabilities.high += scan.high_count || 0;
      scanTypeGrades[scanType].vulnerabilities.medium += scan.medium_count || 0;
      scanTypeGrades[scanType].vulnerabilities.low += scan.low_count || 0;
      scanTypeGrades[scanType].vulnerabilities.info += scan.info_count || 0;
      scanTypeGrades[scanType].scanCount++;
    });

    // Calculate grade for each scan type
    Object.keys(scanTypeGrades).forEach(scanType => {
      const typeData = scanTypeGrades[scanType];
      typeData.grade = calculateSecurityGrade(typeData.vulnerabilities, typeData.scanCount);
    });

    // Aggregate vulnerability counts from completed scans for overall grade calculation
    const totalVulnerabilities: VulnerabilityCount = completedScans.reduce((acc: VulnerabilityCount, scan: { critical_count?: number; high_count?: number; medium_count?: number; low_count?: number; info_count?: number }) => {
      return {
        critical: acc.critical + (scan.critical_count || 0),
        high: acc.high + (scan.high_count || 0),
        medium: acc.medium + (scan.medium_count || 0),
        low: acc.low + (scan.low_count || 0),
        info: acc.info + (scan.info_count || 0),
      };
    }, { critical: 0, high: 0, medium: 0, low: 0, info: 0 });

    // Calculate overall grade based on total vulnerabilities across all scans
    const grade = calculateSecurityGrade(totalVulnerabilities, completedScans.length);

    console.log('Badge page - Scan type grades:', scanTypeGrades);
    console.log('Badge page - Overall grade:', grade);
    console.log('Badge page - Total vulnerabilities:', totalVulnerabilities);

    const gradeInfo = getSecurityGradeInfo(grade);
    const eligibility = getBadgeEligibilityStatus(totalVulnerabilities, completedScans.length);

    console.log('Badge page - Final grade:', grade);
    console.log('Badge page - Grade info:', gradeInfo);

    return {
      grade,
      gradeInfo,
      vulnerabilities: totalVulnerabilities,
      totalScans: scans.length,
      completedScans: completedScans.length,
      eligibility,
      scanTypeGrades
    };
  }, [scans]);

  const companyName = profile?.full_name || 'Your Organization';
  const userGrade = securityData.grade;
  const scanCount = securityData.totalScans;
  const completedScanCount = securityData.completedScans;

  // Get unique website URLs from scans
  const uniqueWebsites = useMemo(() => {
    if (!scans || scans.length === 0) return [];

    const websites = scans
      .filter(scan => scan.target_url)
      .map(scan => {
        const url = scan.target_url.replace(/^https?:\/\//, '').replace(/^www\./, '').replace(/\/$/, '');
        return url;
      })
      .filter((url, index, arr) => arr.indexOf(url) === index);
    return websites;
  }, [scans]);

  // Set default selected website
  useEffect(() => {
    if (uniqueWebsites.length > 0 && !selectedWebsite) {
      setSelectedWebsite(uniqueWebsites[0]);
    }
  }, [uniqueWebsites, selectedWebsite]);

  // Update badge config when real data changes
  useEffect(() => {
    if (userGrade !== 'N/A') {
      setBadgeConfig(prev => ({
        ...prev,
        grade: userGrade,
        companyName: companyName
      }));
    }
  }, [userGrade, companyName]);

  // Update badge config when real data changes
  useEffect(() => {
    setBadgeConfig(prev => ({
      ...prev,
      grade: userGrade,
      companyName: companyName
    }));
  }, [userGrade, companyName]);

  const badgeSizes = {
    small: { width: 120, height: 40, fontSize: 'text-xs' },
    medium: { width: 160, height: 50, fontSize: 'text-sm' },
    large: { width: 200, height: 60, fontSize: 'text-base' }
  };

  const generateBadgeCode = () => {
    const { width, height } = badgeSizes[badgeConfig.size];
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://mostlysecure.com';

    return `<!-- MostlySecure Security Badge -->
<div style="display: inline-block; margin: 10px 0;">
  <img
    src="${baseUrl}/api/badge?style=${badgeConfig.style}&size=${badgeConfig.size}&grade=${badgeConfig.grade}&company=${encodeURIComponent(companyName)}"
    alt="Security Badge - Grade ${badgeConfig.grade} for ${companyName}"
    width="${width}"
    height="${height}"
    style="border: none; max-width: 100%; height: auto;"
  />
</div>`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Code copied to clipboard!');
  };

  const downloadBadge = () => {
    // This would trigger a download of the badge as an image
    toast.success('Badge download started!');
  };

  const BadgePreview = ({ style, size, grade, company }: {
    style: BadgeStyle;
    size: BadgeSize;
    grade: string;
    company?: string;
  }) => {
    // Use selected website and scan type
    const websiteUrl = selectedWebsite || 'example.com';

    let displayGrade = grade;
    let testCategory = 'Security Audit';

    if (selectedScanType === 'overall') {
      displayGrade = userGrade;
      testCategory = 'Security Audit';
    } else if (securityData.scanTypeGrades && selectedScanType in securityData.scanTypeGrades) {
      const scanTypeData = (securityData.scanTypeGrades as Record<string, { grade: SecurityGrade; vulnerabilities: VulnerabilityCount; scanCount: number }>)[selectedScanType];
      if (scanTypeData) {
        displayGrade = scanTypeData.grade;
      }
      const scanTypeNames: Record<string, string> = {
        'secrets': 'Secrets Scanner',
        'database_security': 'Database Security',
        'api_endpoints': 'API Security',
        'comprehensive': 'Comprehensive Audit',
        'firebase': 'Firebase Security',
        'webhooks': 'Webhooks Security'
      };
      testCategory = scanTypeNames[selectedScanType] || selectedScanType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    return (
      <SecurityBadge
        style={style}
        size={size}
        grade={displayGrade as SecurityGrade}
        companyName={company || companyName}
        websiteUrl={websiteUrl}
        testCategory={testCategory}
      />
    );
  };

  // Show loading state while fetching scan data
  if (scansLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-display text-gray-900 mb-4">Trust Badge</h1>
            <p className="text-gray-600">Showcase your security excellence with verified badges</p>
          </div>
          <div className="flex items-center justify-center h-64">
            <LoadingSpinner size="lg" text="Loading your security data..." />
          </div>
        </div>
      </div>
    );
  }

  // Show message for users with no completed scans
  if (completedScanCount === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-display text-gray-900 mb-4">Trust Badge</h1>
            <p className="text-gray-600">Showcase your security excellence with verified badges</p>
          </div>

          <Card className="max-w-2xl mx-auto">
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="h-8 w-8 text-purple-600" />
              </div>
              <CardTitle>
                {scanCount > 0 ? 'Complete Your Security Scans' : 'Complete Your First Scan'}
              </CardTitle>
              <CardDescription>
                {scanCount > 0
                  ? `You have ${scanCount} scans but none are completed yet. Wait for your scans to finish to get your trust badge.`
                  : 'Trust badges are available for all users! Complete your first security scan to get started.'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-6">
              <div className="space-y-4">
                <div className="flex items-center gap-3 text-left">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 font-bold">1</span>
                  </div>
                  <span>Run your first security scan</span>
                </div>
                <div className="flex items-center gap-3 text-left">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 font-bold">2</span>
                  </div>
                  <span>Get your security grade (A+ to F)</span>
                </div>
                <div className="flex items-center gap-3 text-left">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 font-bold">3</span>
                  </div>
                  <span>Generate your personalized trust badge</span>
                </div>
              </div>
              <Button className="gradient-primary text-white" onClick={() => window.location.href = '/dashboard/scans'}>
                <TrendingUp className="h-4 w-4 mr-2" />
                Start First Scan
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-display text-gray-900 mb-2">Trust Badge</h1>
              <p className="text-gray-600">
                {userGrade === 'A+' || userGrade === 'A' ? (
                  `Showcase your excellent Grade ${userGrade} security certification - No critical vulnerabilities found!`
                ) : userGrade === 'B+' || userGrade === 'B' ? (
                  `Showcase your good Grade ${userGrade} security certification - ${securityData.vulnerabilities.critical + securityData.vulnerabilities.high} high-priority issues to address`
                ) : userGrade === 'C+' || userGrade === 'C' ? (
                  `Your Grade ${userGrade} security needs improvement - ${securityData.vulnerabilities.critical + securityData.vulnerabilities.high} high-priority issues to address`
                ) : userGrade === 'D' ? (
                  `Your Grade ${userGrade} security has significant issues - ${securityData.vulnerabilities.critical + securityData.vulnerabilities.high} high-priority issues to address`
                ) : userGrade === 'F' ? (
                  `Your Grade ${userGrade} security has critical issues - ${securityData.vulnerabilities.critical + securityData.vulnerabilities.high} high-priority issues need immediate attention`
                ) : (
                  'Complete your security scans to get your trust badge'
                )}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Badge
                variant="secondary"
                className={securityData.gradeInfo?.bgColor + ' ' + securityData.gradeInfo?.color}
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Grade {userGrade}
              </Badge>
              <Badge variant="outline">
                {completedScanCount} of {scanCount} scans completed
              </Badge>
              {securityData.vulnerabilities.critical + securityData.vulnerabilities.high === 0 && (
                <Badge variant="secondary" className="bg-green-100 text-green-700">
                  <Shield className="h-3 w-3 mr-1" />
                  Secure
                </Badge>
              )}
            </div>
          </div>
        </motion.div>

        {/* Warning for poor grades */}
        {(userGrade === 'F' || userGrade === 'D') && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6"
          >
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="pt-6">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-orange-900 mb-1">
                      {userGrade === 'F' ? 'Critical Security Issues Detected' : 'Significant Security Issues Detected'}
                    </h3>
                    <p className="text-sm text-orange-800 mb-3">
                      {userGrade === 'F'
                        ? 'We strongly recommend fixing critical vulnerabilities before displaying a security badge on your website. A Grade F badge may harm user trust.'
                        : 'Consider addressing security issues before showcasing your badge. Improving your grade will build more user confidence.'
                      }
                    </p>
                    <Button
                      size="sm"
                      variant="outline"
                      className="border-orange-300 text-orange-700 hover:bg-orange-100"
                      onClick={() => window.location.href = '/dashboard/scans'}
                    >
                      View Scan Details & Fix Issues
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Badge Preview */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-2"
          >
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="h-5 w-5" />
                      Badge Preview
                    </CardTitle>
                    <CardDescription>
                      See how your badge will appear on your website
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="preview-mode"
                      checked={showPreview}
                      onCheckedChange={setShowPreview}
                    />
                    <Label htmlFor="preview-mode">Live Preview</Label>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {showPreview && (
                  <div className="space-y-8">
                    {/* Different Backgrounds */}
                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-900">On Different Backgrounds</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* White Background */}
                        <div className="bg-white p-6 rounded-lg border-2 border-gray-200 flex items-center justify-center">
                          <BadgePreview
                            style={badgeConfig.style}
                            size={badgeConfig.size}
                            grade={badgeConfig.grade}
                            company={companyName}
                          />
                        </div>

                        {/* Dark Background */}
                        <div className="bg-gray-900 p-6 rounded-lg flex items-center justify-center">
                          <BadgePreview
                            style={badgeConfig.style}
                            size={badgeConfig.size}
                            grade={badgeConfig.grade}
                            company={companyName}
                          />
                        </div>

                        {/* Colored Background */}
                        <div className="bg-blue-500 p-6 rounded-lg flex items-center justify-center">
                          <BadgePreview
                            style={badgeConfig.style}
                            size={badgeConfig.size}
                            grade={badgeConfig.grade}
                            company={companyName}
                          />
                        </div>

                        {/* Gradient Background */}
                        <div className="bg-gradient-to-r from-purple-400 to-pink-400 p-6 rounded-lg flex items-center justify-center">
                          <BadgePreview
                            style={badgeConfig.style}
                            size={badgeConfig.size}
                            grade={badgeConfig.grade}
                            company={companyName}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Configuration Panel */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  Customize Badge
                </CardTitle>
                <CardDescription>
                  Personalize your security badge
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Website Selection */}
                {uniqueWebsites.length > 0 && (
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Website</Label>
                    <select
                      value={selectedWebsite}
                      onChange={(e) => setSelectedWebsite(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                    >
                      {uniqueWebsites.map(website => (
                        <option key={website} value={website}>{website}</option>
                      ))}
                    </select>
                    <p className="text-xs text-gray-500">
                      Choose which website to display on your badge
                    </p>
                  </div>
                )}

                {/* Scan Type Selection */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Security Test Type</Label>
                  <select
                    value={selectedScanType}
                    onChange={(e) => setSelectedScanType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="overall">Overall Security Grade</option>
                    {Object.entries(securityData.scanTypeGrades || {}).map(([scanType, data]) => {
                      const typedData = data as { grade: SecurityGrade; vulnerabilities: VulnerabilityCount; scanCount: number };
                      const scanTypeNames: Record<string, string> = {
                        'secrets': 'Secrets Scanner',
                        'database_security': 'Database Security',
                        'api_endpoints': 'API Security',
                        'comprehensive': 'Comprehensive Audit',
                        'firebase': 'Firebase Security',
                        'webhooks': 'Webhooks Security'
                      };
                      const displayName = scanTypeNames[scanType] || scanType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
                      return (
                        <option key={scanType} value={scanType}>
                          {displayName} (Grade {typedData.grade})
                        </option>
                      );
                    })}
                  </select>
                  <p className="text-xs text-gray-500">
                    Choose which security test to showcase
                  </p>
                </div>

                {/* Style Selection */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Badge Style</Label>
                  <Tabs value={badgeConfig.style} onValueChange={(value) => setBadgeConfig(prev => ({ ...prev, style: value as BadgeStyle }))}>
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="standard">Standard</TabsTrigger>
                      <TabsTrigger value="light">Light</TabsTrigger>
                    </TabsList>
                  </Tabs>
                  <p className="text-xs text-gray-500">
                    {badgeConfig.style === 'standard'
                      ? 'Colorful gradient background'
                      : 'Clean white background with colored grade'
                    }
                  </p>
                </div>

                {/* Size Selection */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Badge Size</Label>
                  <Tabs value={badgeConfig.size} onValueChange={(value) => setBadgeConfig(prev => ({ ...prev, size: value as BadgeSize }))}>
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="small">Small</TabsTrigger>
                      <TabsTrigger value="medium">Medium</TabsTrigger>
                      <TabsTrigger value="large">Large</TabsTrigger>
                    </TabsList>
                  </Tabs>
                  <p className="text-xs text-gray-500">
                    {badgeConfig.size === 'small' && '120×40px - Perfect for sidebars'}
                    {badgeConfig.size === 'medium' && '160×50px - Recommended for most sites'}
                    {badgeConfig.size === 'large' && '200×60px - Great for headers'}
                  </p>
                </div>

                {/* Actions */}
                <div className="space-y-3 pt-4 border-t">
                  <Button 
                    onClick={() => copyToClipboard(generateBadgeCode())}
                    className="w-full"
                    variant="default"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Embed Code
                  </Button>
                  
                  <Button 
                    onClick={downloadBadge}
                    variant="outline"
                    className="w-full"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download Image
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Per-Scan-Type Grades */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Security Grades by Test Type
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Overall Grade Option */}
                <button
                  onClick={() => setSelectedScanType('overall')}
                  className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
                    selectedScanType === 'overall'
                      ? 'bg-purple-100 border-2 border-purple-300'
                      : 'bg-gray-50 hover:bg-gray-100 border-2 border-transparent'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className={`px-2 py-1 rounded text-sm font-semibold ${
                      userGrade === 'A+' || userGrade === 'A' ? 'bg-green-100 text-green-800' :
                      userGrade === 'B+' || userGrade === 'B' ? 'bg-blue-100 text-blue-800' :
                      userGrade === 'C+' || userGrade === 'C' ? 'bg-yellow-100 text-yellow-800' :
                      userGrade === 'D' ? 'bg-orange-100 text-orange-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {userGrade}
                    </div>
                    <span className="font-medium text-left">Overall Security Grade</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    All Tests Combined
                  </div>
                </button>

                {/* Individual Scan Type Grades */}
                {Object.entries(securityData.scanTypeGrades || {}).map(([scanType, data]) => {
                  const typedData = data as { grade: SecurityGrade; vulnerabilities: VulnerabilityCount; scanCount: number };
                  const scanTypeNames: Record<string, string> = {
                    'secrets': 'Secrets Scanner',
                    'database_security': 'Database Security',
                    'api_endpoints': 'API Endpoints',
                    'comprehensive': 'Comprehensive Scan',
                    'firebase': 'Firebase Security',
                    'webhooks': 'Webhooks Security'
                  };

                  const displayName = scanTypeNames[scanType] || scanType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

                  return (
                    <button
                      key={scanType}
                      onClick={() => setSelectedScanType(scanType)}
                      className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
                        selectedScanType === scanType
                          ? 'bg-purple-100 border-2 border-purple-300'
                          : 'bg-gray-50 hover:bg-gray-100 border-2 border-transparent'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`px-2 py-1 rounded text-sm font-semibold ${
                          typedData.grade === 'A+' || typedData.grade === 'A' ? 'bg-green-100 text-green-800' :
                          typedData.grade === 'B+' || typedData.grade === 'B' ? 'bg-blue-100 text-blue-800' :
                          typedData.grade === 'C+' || typedData.grade === 'C' ? 'bg-yellow-100 text-yellow-800' :
                          typedData.grade === 'D' ? 'bg-orange-100 text-orange-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {typedData.grade}
                        </div>
                        <span className="font-medium text-left">{displayName}</span>
                      </div>
                      <div className="text-sm text-gray-600">
                        {typedData.vulnerabilities.critical}C / {typedData.vulnerabilities.high}H
                      </div>
                    </button>
                  );
                })}
              </CardContent>
            </Card>

            {/* Overall Security Summary */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Overall Security Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-gray-500">Critical</div>
                    <div className="font-semibold text-red-600">{securityData.vulnerabilities.critical}</div>
                  </div>
                  <div>
                    <div className="text-gray-500">High</div>
                    <div className="font-semibold text-orange-600">{securityData.vulnerabilities.high}</div>
                  </div>
                  <div>
                    <div className="text-gray-500">Medium</div>
                    <div className="font-semibold text-yellow-600">{securityData.vulnerabilities.medium}</div>
                  </div>
                  <div>
                    <div className="text-gray-500">Low</div>
                    <div className="font-semibold text-blue-600">{securityData.vulnerabilities.low}</div>
                  </div>
                </div>
                <div className="pt-2 border-t">
                  <div className="text-xs text-gray-500">
                    Grade based on {completedScanCount} completed scans
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Code Preview */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  Embed Code
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-900 rounded-lg p-4 text-sm font-mono text-gray-100 overflow-x-auto">
                  <pre>{generateBadgeCode()}</pre>
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  className="mt-2 w-full"
                  onClick={() => copyToClipboard(generateBadgeCode())}
                >
                  <Copy className="h-3 w-3 mr-1" />
                  Copy
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}