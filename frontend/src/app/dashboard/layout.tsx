'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/auth';
import { DashboardNav } from '@/components/dashboard/dashboard-nav';
import { LoadingSpinner } from '@/components/shared/loading-spinner';
import { ScanNotifications } from '@/components/notifications/scan-notifications';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isAuthenticated, isLoading, isInitialized, getCurrentUser, user } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    // Only check current user if not initialized
    if (!isInitialized && !isLoading) {
      getCurrentUser();
    }
  }, [isInitialized, isLoading, getCurrentUser]);

  useEffect(() => {
    // Only redirect if we're initialized and confirmed not authenticated
    if (isInitialized && !isAuthenticated && !user) {
      router.push('/');
    }
  }, [isAuthenticated, isInitialized, user, router]);

  // Show loading while checking auth status
  if (!isInitialized || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading dashboard..." />
      </div>
    );
  }

  // Show redirecting message if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" text="Redirecting to login..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50">
      <DashboardNav />
      <main className="pt-16">
        {children}
      </main>

      {/* Real-time scan notifications */}
      <ScanNotifications userId={user?.id} />
    </div>
  );
}
