'use client';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  ArrowLeft, 
  Download, 
  RefreshCw, 
  Play, 
  Pause, 
  ExternalLink,
  Clock,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react';
import { apiClient } from '@/lib/api';
import { ScanStatus } from '@/types';
import { toast } from 'sonner';
import { useRealTimeScan } from '@/hooks/use-real-time-scans';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LoadingSpinner } from '@/components/shared/loading-spinner';
import { VulnerabilityList } from '@/components/scans/vulnerability-list';
import { ScanProgress } from '@/components/scans/scan-progress';
import { ScanMetrics } from '@/components/scans/scan-metrics';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

const statusConfig = {
  [ScanStatus.PENDING]: {
    icon: Clock,
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    label: 'Pending',
  },
  [ScanStatus.RUNNING]: {
    icon: Loader2,
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    label: 'Running',
  },
  [ScanStatus.COMPLETED]: {
    icon: CheckCircle,
    color: 'bg-green-100 text-green-800 border-green-200',
    label: 'Completed',
  },
  [ScanStatus.FAILED]: {
    icon: XCircle,
    color: 'bg-red-100 text-red-800 border-red-200',
    label: 'Failed',
  },
  [ScanStatus.CANCELLED]: {
    icon: XCircle,
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    label: 'Cancelled',
  },
};

export default function ScanDetailPage() {
  const params = useParams();
  const router = useRouter();
  const scanId = params.id as string;

  const handleDownloadReport = async (format: 'json' | 'pdf' | 'html') => {
    try {
      if (format === 'json') {
        // Stub for now - report generation not implemented
        const stubData = {
          scan_id: scanId,
          scan_name: scan?.scan_name,
          status: scan?.status,
          vulnerabilities: []
        };
        const blob = new Blob([JSON.stringify(stubData, null, 2)], {
          type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `security-report-${scanId}.json`;
        a.click();
        URL.revokeObjectURL(url);
      } else {
        // PDF/HTML export not implemented yet
        toast.error('PDF/HTML export not yet implemented');
      }
    } catch (error) {
      console.error('Failed to download report:', error);
      alert('Failed to download report. Please try again.');
    }
  };

  // Use real-time scan hook for better updates
  const {
    scan,
    isLoading: scanLoading,
    refetch: refetchScan
  } = useRealTimeScan(scanId);

  const { 
    data: vulnerabilities, 
    isLoading: vulnerabilitiesLoading
  } = useQuery({
    queryKey: ['vulnerabilities', scanId],
    queryFn: () => apiClient.getVulnerabilities(scanId),
    enabled: !!scan && scan.status === ScanStatus.COMPLETED,
  });

  if (scanLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" text="Loading scan details..." />
        </div>
      </div>
    );
  }

  if (!scan) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Scan Not Found</h1>
          <p className="text-gray-600 mb-6">The scan you&apos;re looking for doesn&apos;t exist.</p>
          <Button onClick={() => router.push('/dashboard/scans')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Scans
          </Button>
        </div>
      </div>
    );
  }

  const config = statusConfig[scan.status as keyof typeof statusConfig] || statusConfig[ScanStatus.PENDING];
  const StatusIcon = config.icon;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8"
      >
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/dashboard/scans')}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          
          <div>
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-3xl font-bold text-gray-900">
                {scan.scan_name || `Scan #${scan.id}`}
              </h1>
              <Badge className={cn("text-sm", config.color)}>
                <StatusIcon 
                  className={cn(
                    "mr-2 h-4 w-4",
                    scan.status === ScanStatus.RUNNING && "animate-spin"
                  )} 
                />
                {config.label}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2 text-gray-600">
              <ExternalLink className="h-4 w-4" />
              <span>{scan.target_url}</span>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={() => refetchScan()}
            size="sm"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          
          {scan.status === ScanStatus.COMPLETED && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleDownloadReport('json')}
            >
              <Download className="mr-2 h-4 w-4" />
              Download Report
            </Button>
          )}
          
          {scan.status === ScanStatus.RUNNING && (
            <Button variant="outline" size="sm">
              <Pause className="mr-2 h-4 w-4" />
              Cancel
            </Button>
          )}
          
          {scan.status === ScanStatus.FAILED && (
            <Button variant="outline" size="sm">
              <Play className="mr-2 h-4 w-4" />
              Retry
            </Button>
          )}
        </div>
      </motion.div>

      {/* Progress Bar for Running Scans */}
      {scan.status === ScanStatus.RUNNING && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <Card>
            <CardContent className="p-6">
              <ScanProgress scan={scan} />
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="vulnerabilities" disabled={scan.status !== ScanStatus.COMPLETED}>
              Vulnerabilities
            </TabsTrigger>
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Scan Information */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Scan Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-600">Scan Type</label>
                        <p className="text-gray-900 capitalize">
                          {scan.scan_type.replace('_', ' ')}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">Target URL</label>
                        <p className="text-gray-900 break-all">{scan.target_url}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">Created</label>
                        <p className="text-gray-900">
                          {formatDistanceToNow(new Date(scan.created_at), { addSuffix: true })}
                        </p>
                      </div>
                      {scan.completed_at && (
                        <div>
                          <label className="text-sm font-medium text-gray-600">Completed</label>
                          <p className="text-gray-900">
                            {formatDistanceToNow(new Date(scan.completed_at), { addSuffix: true })}
                          </p>
                        </div>
                      )}
                    </div>
                    
                    {scan.scan_description && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Description</label>
                        <p className="text-gray-900">{scan.scan_description}</p>
                      </div>
                    )}
                    
                    {scan.error_message && (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <label className="text-sm font-medium text-red-800">Error Message</label>
                        <p className="text-red-700 mt-1">{scan.error_message}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Quick Stats */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle>Quick Stats</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {scan.status === ScanStatus.COMPLETED ? (
                      <div className="space-y-4">
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className="text-2xl font-bold text-gray-900">
                            {scan.vulnerabilities_count}
                          </div>
                          <div className="text-sm text-gray-600">Total Vulnerabilities</div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div className="text-center p-3 bg-red-50 rounded-lg">
                            <div className="font-semibold text-red-600">{scan.critical_count}</div>
                            <div className="text-red-600">Critical</div>
                          </div>
                          <div className="text-center p-3 bg-orange-50 rounded-lg">
                            <div className="font-semibold text-orange-600">{scan.high_count}</div>
                            <div className="text-orange-600">High</div>
                          </div>
                          <div className="text-center p-3 bg-yellow-50 rounded-lg">
                            <div className="font-semibold text-yellow-600">{scan.medium_count}</div>
                            <div className="text-yellow-600">Medium</div>
                          </div>
                          <div className="text-center p-3 bg-green-50 rounded-lg">
                            <div className="font-semibold text-green-600">{scan.low_count}</div>
                            <div className="text-green-600">Low</div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <div className="text-gray-500">
                          {scan.status === ScanStatus.RUNNING 
                            ? 'Scan in progress...' 
                            : 'No results available'
                          }
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="vulnerabilities">
            <VulnerabilityList 
              vulnerabilities={vulnerabilities} 
              isLoading={vulnerabilitiesLoading}
              scanId={scanId}
            />
          </TabsContent>

          <TabsContent value="metrics">
            <ScanMetrics scan={scan} />
          </TabsContent>

          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Scan Configuration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Max Depth</label>
                    <p className="text-gray-900">{scan.max_depth}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Timeout (seconds)</label>
                    <p className="text-gray-900">{scan.timeout_seconds}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Concurrent Requests</label>
                    <p className="text-gray-900">{scan.concurrent_requests}</p>
                  </div>
                  {scan.custom_headers && Object.keys(scan.custom_headers).length > 0 && (
                    <div className="md:col-span-2">
                      <label className="text-sm font-medium text-gray-600">Custom Headers</label>
                      <pre className="mt-1 p-3 bg-gray-50 rounded-lg text-sm overflow-x-auto">
                        {JSON.stringify(scan.custom_headers, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  );
}
