'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Plus,
  Search,
  RefreshCw,
  MoreHorizontal,
  Download
} from 'lucide-react';
import { Scan, ScanStatus, ScanType } from '@/types';
import { useRealTimeScans } from '@/hooks/use-real-time-scans';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { LoadingSpinner } from '@/components/shared/loading-spinner';
import { ScanCard } from '@/components/scans/scan-card';
import { NewScanDialog } from '@/components/scans/new-scan-dialog';
import { EmptyState } from '@/components/shared/empty-state';

export default function ScansPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<ScanStatus | 'all'>('all');
  const [typeFilter, setTypeFilter] = useState<ScanType | 'all'>('all');
  const [showNewScanDialog, setShowNewScanDialog] = useState(false);

  // Use the new real-time scans hook
  const {
    scans,
    isLoading,
    refetch,
    isRefetching,
    hasRunningScans
  } = useRealTimeScans({
    statusFilter,
    typeFilter,
    onScanCompleted: (scan) => {
      // Optionally navigate to the completed scan
      console.log('Scan completed:', scan);
    },
    onScanFailed: (scan) => {
      console.log('Scan failed:', scan);
    },
    onScanStarted: (scan) => {
      console.log('Scan started:', scan);
    }
  });

  const filteredScans = scans?.filter((scan: Scan) => {
    if (!searchQuery) return true;
    return (
      scan.scan_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      scan.target_url.toLowerCase().includes(searchQuery.toLowerCase()) ||
      scan.scan_description?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  const handleRefresh = () => {
    refetch();
  };

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export scans');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8"
      >
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold text-gray-900">Security Scans</h1>
            {hasRunningScans && (
              <div className="flex items-center gap-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse" />
                <span className="font-medium">Live Updates Active</span>
              </div>
            )}
          </div>
          <p className="text-gray-600 mt-2">
            Manage and monitor your security scans
            {hasRunningScans && (
              <span className="text-blue-600 font-medium"> • Real-time updates enabled</span>
            )}
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefetching}
            className="hidden sm:flex"
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isRefetching ? 'animate-spin' : ''}`} />
            Refresh
            {hasRunningScans && (
              <span className="ml-2 h-2 w-2 bg-green-500 rounded-full animate-pulse" />
            )}
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleRefresh}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleExport}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          <Button 
            onClick={() => setShowNewScanDialog(true)}
            className="gradient-primary text-white hover:opacity-90"
          >
            <Plus className="mr-2 h-4 w-4" />
            New Scan
          </Button>
        </div>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search scans by name, URL, or description..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="flex gap-3">
                <Select value={statusFilter} onValueChange={(value: ScanStatus | 'all') => setStatusFilter(value)}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value={ScanStatus.PENDING}>Pending</SelectItem>
                    <SelectItem value={ScanStatus.RUNNING}>Running</SelectItem>
                    <SelectItem value={ScanStatus.COMPLETED}>Completed</SelectItem>
                    <SelectItem value={ScanStatus.FAILED}>Failed</SelectItem>
                    <SelectItem value={ScanStatus.CANCELLED}>Cancelled</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select value={typeFilter} onValueChange={(value: ScanType | 'all') => setTypeFilter(value)}>
                  <SelectTrigger className="w-[160px]">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value={ScanType.API_ENDPOINTS}>API Endpoints</SelectItem>
                    <SelectItem value={ScanType.DATABASE_SECURITY}>Database Security</SelectItem>
                    <SelectItem value={ScanType.SECRETS}>Secrets</SelectItem>
                    <SelectItem value={ScanType.FIREBASE}>Firebase</SelectItem>
                    <SelectItem value={ScanType.WEBHOOKS}>Webhooks</SelectItem>
                    <SelectItem value={ScanType.COMPREHENSIVE}>Comprehensive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Scans List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <LoadingSpinner size="lg" text="Loading scans..." />
          </div>
        ) : !filteredScans || filteredScans.length === 0 ? (
          <EmptyState
            icon={Search}
            title={searchQuery || statusFilter !== 'all' || typeFilter !== 'all' 
              ? "No scans found" 
              : "No scans yet"
            }
            description={searchQuery || statusFilter !== 'all' || typeFilter !== 'all'
              ? "Try adjusting your search or filters"
              : "Create your first security scan to get started"
            }
            action={
              <Button 
                onClick={() => setShowNewScanDialog(true)}
                className="gradient-primary text-white hover:opacity-90"
              >
                <Plus className="mr-2 h-4 w-4" />
                Create First Scan
              </Button>
            }
          />
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredScans.map((scan: Scan, index: number) => (
              <motion.div
                key={scan.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 + index * 0.05 }}
              >
                <ScanCard scan={scan} />
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>

      {/* New Scan Dialog */}
      <NewScanDialog 
        open={showNewScanDialog} 
        onOpenChange={setShowNewScanDialog}
        onScanCreated={() => refetch()}
      />
    </div>
  );
}
