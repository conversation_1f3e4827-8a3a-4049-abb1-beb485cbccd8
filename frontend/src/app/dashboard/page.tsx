'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  Shield, 
  Search, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  Plus,
  Activity
} from 'lucide-react';
import { useAuthStore } from '@/stores/auth';
import { apiClient } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { ApiTest } from '@/components/debug/api-test';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { StatsCard } from '@/components/dashboard/stats-card';
import { RecentScans } from '@/components/dashboard/recent-scans';
import { VulnerabilityChart } from '@/components/dashboard/vulnerability-chart';
import { NewScanDialog } from '@/components/scans/new-scan-dialog';

export default function DashboardPage() {
  const { profile } = useAuthStore();
  const [showNewScanDialog, setShowNewScanDialog] = useState(false);

  // Fetch dashboard data
  const { data: scanSummary, isLoading: scanSummaryLoading } = useQuery({
    queryKey: ['scan-summary'],
    queryFn: () => apiClient.getScanSummary(),
  });

  const { data: vulnerabilitySummary, isLoading: vulnSummaryLoading } = useQuery({
    queryKey: ['vulnerability-summary'],
    queryFn: () => apiClient.getVulnerabilitySummary(),
  });

  const { data: recentScans, isLoading: recentScansLoading, refetch: refetchRecentScans } = useQuery({
    queryKey: ['recent-scans'],
    queryFn: () => apiClient.getScans({ limit: 5 }),
  });

  const isLoading = scanSummaryLoading || vulnSummaryLoading || recentScansLoading;

  const handleScanCreated = () => {
    // Refetch dashboard data after scan creation
    refetchRecentScans();
  };

  const stats = [
    {
      title: 'Total Scans',
      value: scanSummary?.total_scans || 0,
      icon: Search,
      description: 'All time scans',
      trend: '+12%',
      trendUp: true,
    },
    {
      title: 'Active Scans',
      value: (scanSummary?.running_scans || 0) + (scanSummary?.pending_scans || 0),
      icon: Activity,
      description: 'Currently running',
      trend: `${scanSummary?.running_scans || 0} running`,
      trendUp: true,
    },
    {
      title: 'Vulnerabilities',
      value: vulnerabilitySummary?.total_vulnerabilities || 0,
      icon: AlertTriangle,
      description: 'Total found',
      trend: `${vulnerabilitySummary?.critical_count || 0} critical`,
      trendUp: false,
    },
    {
      title: 'Completed',
      value: scanSummary?.completed_scans || 0,
      icon: CheckCircle,
      description: 'Successfully finished',
      trend: '+8%',
      trendUp: true,
    },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Welcome back, {profile?.full_name || profile?.username}! 👋
            </h1>
            <p className="text-gray-600 mt-2">
              Here&apos;s what&apos;s happening with your security scans today.
            </p>
          </div>
          <Button 
            onClick={() => setShowNewScanDialog(true)}
            className="gradient-primary text-white hover:opacity-90"
          >
            <Plus className="mr-2 h-4 w-4" />
            New Scan
          </Button>
        </div>
      </motion.div>

      {/* Stats Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
      >
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 + index * 0.05 }}
          >
            <StatsCard {...stat} isLoading={isLoading} />
          </motion.div>
        ))}
      </motion.div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Scans */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="lg:col-span-2"
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-purple-600" />
                Recent Scans
              </CardTitle>
              <CardDescription>
                Your latest security scans and their status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RecentScans scans={recentScans} isLoading={recentScansLoading} />
            </CardContent>
          </Card>
        </motion.div>

        {/* Vulnerability Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-purple-600" />
                Vulnerability Overview
              </CardTitle>
              <CardDescription>
                Breakdown by severity level
              </CardDescription>
            </CardHeader>
            <CardContent>
              <VulnerabilityChart 
                data={vulnerabilitySummary} 
                isLoading={vulnSummaryLoading} 
              />
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="mt-8"
      >
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks to get you started
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button 
                variant="outline" 
                className="h-auto p-4 flex flex-col items-center gap-2"
                onClick={() => setShowNewScanDialog(true)}
              >
                <Search className="h-6 w-6 text-purple-600" />
                <span className="font-medium">Start New Scan</span>
                <span className="text-xs text-gray-500">Scan your application for vulnerabilities</span>
              </Button>
              <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
                <AlertTriangle className="h-6 w-6 text-orange-600" />
                <span className="font-medium">Review Vulnerabilities</span>
                <span className="text-xs text-gray-500">Check and resolve security issues</span>
              </Button>
              <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
                <Shield className="h-6 w-6 text-green-600" />
                <span className="font-medium">Security Reports</span>
                <span className="text-xs text-gray-500">Download detailed security reports</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* New Scan Dialog */}
      <NewScanDialog 
        open={showNewScanDialog} 
        onOpenChange={setShowNewScanDialog}
        onScanCreated={handleScanCreated}
      />
      
      {/* Temporary Debug Component */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-8">
          <ApiTest />
        </div>
      )}
    </div>
  );
}
