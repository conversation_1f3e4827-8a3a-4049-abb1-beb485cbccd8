import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { createClient } from '@/lib/supabase/client';
import type { User } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

type Profile = Database['public']['Tables']['profiles']['Row'];

interface AuthState {
  user: User | null;
  profile: Profile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isInitialized: boolean;
  error: string | null;
  
  // Actions
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, username: string, fullName?: string) => Promise<void>;
  logout: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
  updateProfile: (updates: Partial<Profile>) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      profile: null,
      isAuthenticated: false,
      isLoading: false,
      isInitialized: false,
      error: null,

      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null });
          const supabase = createClient();
          
          const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password,
          });

          if (error) throw error;

          if (data.user) {
            // Fetch profile
            const { data: profile } = await supabase
              .from('profiles')
              .select('*')
              .eq('id', data.user.id)
              .single();

            set({ 
              user: data.user, 
              profile,
              isAuthenticated: true,
              isLoading: false 
            });
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Login failed';
          set({ 
            error: errorMessage, 
            isLoading: false,
            isAuthenticated: false,
            user: null,
            profile: null
          });
          throw error;
        }
      },

      register: async (email: string, password: string, username: string, fullName?: string) => {
        try {
          set({ isLoading: true, error: null });
          const supabase = createClient();
          
          const { data, error } = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: {
                username,
                full_name: fullName,
              }
            }
          });

          if (error) throw error;

          if (data.user) {
            // Profile will be created automatically by database trigger
            // Fetch the created profile
            const { data: profile } = await supabase
              .from('profiles')
              .select('*')
              .eq('id', data.user.id)
              .single();

            set({ 
              user: data.user, 
              profile,
              isAuthenticated: true,
              isLoading: false 
            });
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Registration failed';
          set({ 
            error: errorMessage, 
            isLoading: false 
          });
          throw error;
        }
      },

      logout: async () => {
        try {
          set({ isLoading: true });
          const supabase = createClient();
          
          const { error } = await supabase.auth.signOut();
          if (error) throw error;
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          // Clear all auth-related localStorage items
          if (typeof window !== 'undefined') {
            localStorage.removeItem('mostlysecure-auth');
            localStorage.removeItem('supabase.auth.token');
            // Clear any session storage as well
            sessionStorage.clear();
          }
          
          set({ 
            user: null,
            profile: null,
            isAuthenticated: false, 
            error: null,
            isLoading: false,
            isInitialized: false // Reset this to allow re-initialization
          });
        }
      },

      getCurrentUser: async () => {
        try {
          // Prevent multiple simultaneous calls
          const state = get();
          if (state.isLoading) {
            return;
          }
          
          set({ isLoading: true });
          const supabase = createClient();
          
          // Add retry logic for auth calls
          let retries = 3;
          let lastError: any = null;
          
          while (retries > 0) {
            try {
              // First try to get the session
              const { data: { session } } = await supabase.auth.getSession();
              
              if (!session) {
                // No session, user is not authenticated
                set({ 
                  user: null, 
                  profile: null,
                  isAuthenticated: false, 
                  isLoading: false,
                  isInitialized: true 
                });
                return;
              }
              
              // If we have a session, get the user
              const { data: { user }, error } = await supabase.auth.getUser();
              
              if (error) {
                // Check if it's an auth session missing error
                if (error.message === 'Auth session missing!') {
                  set({ 
                    user: null, 
                    profile: null,
                    isAuthenticated: false, 
                    isLoading: false,
                    isInitialized: true 
                  });
                  return;
                }
                throw error;
              }
              
              if (!user) {
                set({ 
                  user: null, 
                  profile: null,
                  isAuthenticated: false, 
                  isLoading: false,
                  isInitialized: true 
                });
                return;
              }

              // Fetch profile
              const { data: profile, error: profileError } = await supabase
                .from('profiles')
                .select('*')
                .eq('id', user.id)
                .single();

              if (profileError) {
                console.error('Failed to fetch profile:', profileError);
              }

              set({ 
                user, 
                profile: profile || null,
                isAuthenticated: true, 
                isLoading: false,
                isInitialized: true 
              });
              
              return; // Success, exit retry loop
              
            } catch (error) {
              lastError = error;
              retries--;
              
              if (retries > 0) {
                // Wait before retrying (exponential backoff)
                await new Promise(resolve => setTimeout(resolve, (3 - retries) * 1000));
              }
            }
          }
          
          // All retries failed
          throw lastError || new Error('Failed to get current user after retries');
        } catch (error) {
          console.error('Error in getCurrentUser:', error);
          set({ 
            user: null,
            profile: null,
            isAuthenticated: false, 
            isLoading: false,
            isInitialized: true 
          });
        }
      },

      updateProfile: async (updates: Partial<Profile>) => {
        try {
          const { user } = get();
          if (!user) throw new Error('Not authenticated');

          const supabase = createClient();
          
          const { data, error } = await supabase
            .from('profiles')
            .update(updates)
            .eq('id', user.id)
            .select()
            .single();

          if (error) throw error;

          set({ profile: data });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to update profile';
          set({ error: errorMessage });
          throw error;
        }
      },

      changePassword: async (currentPassword: string, newPassword: string) => {
        try {
          const supabase = createClient();
          const { user } = get();
          
          if (!user?.email) throw new Error('Not authenticated');

          // First verify current password
          const { error: signInError } = await supabase.auth.signInWithPassword({
            email: user.email,
            password: currentPassword,
          });

          if (signInError) throw new Error('Current password is incorrect');

          // Update password
          const { error } = await supabase.auth.updateUser({
            password: newPassword
          });

          if (error) throw error;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to change password';
          set({ error: errorMessage });
          throw error;
        }
      },

      clearError: () => set({ error: null }),

      setLoading: (loading: boolean) => set({ isLoading: loading }),
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user,
        profile: state.profile,
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
);

// Initialize auth state on app load
if (typeof window !== 'undefined') {
  // Track if we're in the middle of a sign-in process
  let isSigningIn = false;

  // Check if user is authenticated on load
  const initAuth = async () => {
    const state = useAuthStore.getState();

    // Skip if already initialized or if we're in the middle of signing in
    if (state.isInitialized || isSigningIn) {
      return;
    }

    try {
      // Just call getCurrentUser without timeout to avoid delays
      await useAuthStore.getState().getCurrentUser();
    } catch (error) {
      // Don't override auth state if we're in the middle of signing in
      if (isSigningIn) {
        console.log('Skipping auth error handling - sign in in progress');
        return;
      }

      if (error instanceof Error && error.message === 'Auth session missing!') {
        console.log('No auth session found - user is not logged in');
        useAuthStore.setState({
          isLoading: false,
          isInitialized: true,
          isAuthenticated: false,
          user: null,
          profile: null
        });
      } else {
        console.error('Failed to get current user:', error);
        // On other errors, check persisted state before clearing auth
        const persistedState = useAuthStore.getState();
        if (persistedState.user && persistedState.isAuthenticated) {
          console.log('Error occurred but using persisted auth state');
          useAuthStore.setState({
            isLoading: false,
            isInitialized: true
          });
        } else {
          useAuthStore.setState({
            isLoading: false,
            isInitialized: true,
            isAuthenticated: false,
            user: null,
            profile: null
          });
        }
      }
    }
  };
  
  // Run initialization
  initAuth();

  // Set up auth state change listener
  const supabase = createClient();
  
  supabase.auth.onAuthStateChange(async (event, session) => {
    console.log('Auth state change:', event, session?.user?.id);

    if (event === 'SIGNED_IN' && session?.user) {
      // User signed in - set flag to prevent timeout interference
      isSigningIn = true;
      console.log('User signed in, updating auth state...');

      try {
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();

        useAuthStore.setState({
          user: session.user,
          profile,
          isAuthenticated: true,
          isLoading: false,
          isInitialized: true,
        });

        console.log('Sign in complete - auth state updated', {
          isAuthenticated: true,
          userId: session.user.id,
          profileId: profile?.id
        });
      } catch (error) {
        console.error('Error fetching profile during sign in:', error);
        // Still mark as authenticated even if profile fetch fails
        useAuthStore.setState({
          user: session.user,
          profile: null,
          isAuthenticated: true,
          isLoading: false,
          isInitialized: true,
        });
        console.log('Sign in complete (no profile) - auth state updated', {
          isAuthenticated: true,
          userId: session.user.id
        });
      } finally {
        // Clear the signing in flag after a delay
        setTimeout(() => {
          isSigningIn = false;
          console.log('Signing in flag cleared');
        }, 3000); // Increased to 3 seconds
      }
    } else if (event === 'SIGNED_OUT') {
      // User signed out
      isSigningIn = false;
      useAuthStore.setState({
        user: null,
        profile: null,
        isAuthenticated: false,
        isLoading: false,
        isInitialized: true,
      });
    } else if (event === 'TOKEN_REFRESHED' && session?.user) {
      // Token was refreshed - ensure we maintain auth state
      console.log('Token refreshed for user:', session.user.id);
      const currentState = useAuthStore.getState();
      if (!currentState.isAuthenticated || !currentState.user) {
        // If we're not marked as authenticated but have a valid session, update state
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();

        useAuthStore.setState({
          user: session.user,
          profile,
          isAuthenticated: true,
          isLoading: false,
          isInitialized: true,
        });
      }
    } else if (event === 'INITIAL_SESSION' && session?.user) {
      // Initial session detected - this helps with hydration
      console.log('Initial session detected for user:', session.user.id);
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single();

      useAuthStore.setState({
        user: session.user,
        profile,
        isAuthenticated: true,
        isLoading: false,
        isInitialized: true,
      });
    }
  });
}
