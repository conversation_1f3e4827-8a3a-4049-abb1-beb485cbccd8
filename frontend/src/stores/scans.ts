import { create } from 'zustand';
import { createClient } from '@/lib/supabase/client';
import type { Database } from '@/types/supabase';

type Scan = Database['public']['Tables']['scans']['Row'];
type Vulnerability = Database['public']['Tables']['vulnerabilities']['Row'];
type CreateScanInput = Database['public']['Tables']['scans']['Insert'];

interface ScanState {
  scans: Scan[];
  currentScan: Scan | null;
  vulnerabilities: Vulnerability[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchScans: (params?: { skip?: number; limit?: number; status?: string; scan_type?: string }) => Promise<void>;
  fetchScan: (scanId: string) => Promise<void>;
  createScan: (scanData: Omit<CreateScanInput, 'user_id'>) => Promise<Scan>;
  cancelScan: (scanId: string) => Promise<void>;
  deleteScan: (scanId: string) => Promise<void>;
  fetchVulnerabilities: (scanId: string, params?: { skip?: number; limit?: number; severity?: string; category?: string }) => Promise<void>;
  updateScan: (scanId: string, updates: Partial<Scan>) => void;
  removeScan: (scanId: string) => void;
  addVulnerability: (scanId: string, vulnerability: Vulnerability) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useScanStore = create<ScanState>((set, get) => ({
  scans: [],
  currentScan: null,
  vulnerabilities: [],
  isLoading: false,
  error: null,

  fetchScans: async (params) => {
    try {
      set({ isLoading: true, error: null });
      const supabase = createClient();
      
      let query = supabase
        .from('scans')
        .select('*')
        .order('created_at', { ascending: false });

      if (params?.status) query = query.eq('status', params.status);
      if (params?.scan_type) query = query.eq('scan_type', params.scan_type);
      if (params?.skip !== undefined && params?.limit !== undefined) {
        query = query.range(params.skip, params.skip + params.limit - 1);
      }

      const { data, error } = await query;
      
      if (error) throw error;
      set({ scans: data || [], isLoading: false });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch scans';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  fetchScan: async (scanId: string) => {
    try {
      set({ isLoading: true, error: null });
      const supabase = createClient();
      
      const { data, error } = await supabase
        .from('scans')
        .select('*')
        .eq('id', scanId)
        .single();

      if (error) throw error;
      
      set({ currentScan: data, isLoading: false });
      
      // Update scan in list if it exists
      const { scans } = get();
      const updatedScans = scans.map(s => s.id === scanId ? data : s);
      set({ scans: updatedScans });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch scan';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  createScan: async (scanData) => {
    try {
      set({ isLoading: true, error: null });
      
      const response = await fetch('/api/scans', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(scanData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create scan');
      }

      const newScan = await response.json();
      
      // Add to scans list
      const { scans } = get();
      set({ 
        scans: [newScan, ...scans],
        currentScan: newScan,
        isLoading: false 
      });
      
      return newScan;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create scan';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  cancelScan: async (scanId: string) => {
    try {
      set({ isLoading: true, error: null });
      
      const response = await fetch(`/api/scans/${scanId}/cancel`, {
        method: 'POST',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to cancel scan');
      }

      const updatedScan = await response.json();
      
      // Update scan in state
      get().updateScan(scanId, updatedScan);
      
      if (get().currentScan?.id === scanId) {
        set({ currentScan: updatedScan });
      }
      
      set({ isLoading: false });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to cancel scan';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  deleteScan: async (scanId: string) => {
    try {
      set({ isLoading: true, error: null });
      
      const response = await fetch(`/api/scans/${scanId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete scan');
      }
      
      // Remove from scans list
      const { scans } = get();
      const updatedScans = scans.filter(s => s.id !== scanId);
      set({ scans: updatedScans });
      
      // Clear current scan if it was deleted
      if (get().currentScan?.id === scanId) {
        set({ currentScan: null });
      }
      
      set({ isLoading: false });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete scan';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  fetchVulnerabilities: async (scanId: string, params) => {
    try {
      set({ isLoading: true, error: null });
      const supabase = createClient();
      
      let query = supabase
        .from('vulnerabilities')
        .select('*')
        .eq('scan_id', scanId)
        .order('severity', { ascending: false });

      if (params?.severity) query = query.eq('severity', params.severity);
      if (params?.category) query = query.eq('owasp_category', params.category);
      if (params?.skip !== undefined && params?.limit !== undefined) {
        query = query.range(params.skip, params.skip + params.limit - 1);
      }

      const { data, error } = await query;
      
      if (error) throw error;
      set({ vulnerabilities: data || [], isLoading: false });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch vulnerabilities';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  updateScan: (scanId: string, updates: Partial<Scan>) => {
    const { scans, currentScan } = get();
    const updatedScans = scans.map(s => 
      s.id === scanId ? { ...s, ...updates } : s
    );
    set({ scans: updatedScans });
    
    if (currentScan?.id === scanId) {
      set({ currentScan: { ...currentScan, ...updates } });
    }
  },

  removeScan: (scanId: string) => {
    const { scans } = get();
    set({ scans: scans.filter(s => s.id !== scanId) });
    
    if (get().currentScan?.id === scanId) {
      set({ currentScan: null });
    }
  },

  addVulnerability: (scanId: string, vulnerability: Vulnerability) => {
    const { vulnerabilities, currentScan } = get();
    if (currentScan?.id === scanId) {
      set({ vulnerabilities: [...vulnerabilities, vulnerability] });
    }
  },

  clearError: () => set({ error: null }),
  
  setLoading: (loading: boolean) => set({ isLoading: loading }),
}));
