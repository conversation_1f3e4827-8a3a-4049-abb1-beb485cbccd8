// Security grading system for MostlySecure

export interface VulnerabilityCount {
  critical: number;
  high: number;
  medium: number;
  low: number;
  info: number;
}

export type SecurityGrade = 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D' | 'F' | 'N/A';

export interface SecurityGradeInfo {
  grade: SecurityGrade;
  score: number;
  eligibleForBadge: boolean;
  description: string;
  color: string;
  bgColor: string;
}

export function calculateSecurityGrade(vulnerabilities: VulnerabilityCount, scanCount: number = 1): SecurityGrade {
  // New logic: Critical/High should be separated
  // If no critical issues -> A grade possible
  // If there are critical issues -> B, C, D based on count

  const { critical, high } = vulnerabilities;

  // If no scans completed, return N/A
  if (scanCount === 0) {
    return 'N/A';
  }

  // Grade logic based on critical vulnerabilities first
  if (critical === 0) {
    // No critical vulnerabilities - A grade possible
    if (high === 0) {
      return 'A+'; // Perfect - no critical or high vulnerabilities
    } else if (high <= 2) {
      return 'A';  // Excellent - no critical, minimal high
    } else if (high <= 5) {
      return 'B+'; // Very good - no critical, some high
    } else {
      return 'B';  // Good - no critical, but many high
    }
  } else {
    // Has critical vulnerabilities - B, C, D, F based on count
    if (critical === 1) {
      return 'C+'; // One critical vulnerability
    } else if (critical <= 3) {
      return 'C';  // Few critical vulnerabilities
    } else if (critical <= 5) {
      return 'D';  // Several critical vulnerabilities
    } else {
      return 'F';  // Many critical vulnerabilities - immediate action required
    }
  }
}

export function getSecurityGradeInfo(grade: SecurityGrade): SecurityGradeInfo {
  let description: string;
  let color: string;
  let bgColor: string;
  let eligibleForBadge: boolean;
  let score: number;

  switch (grade) {
    case 'A+':
      description = 'Exceptional security - zero critical or high vulnerabilities';
      color = 'text-emerald-700';
      bgColor = 'bg-emerald-100';
      eligibleForBadge = true;
      score = 100;
      break;
    case 'A':
      description = 'Excellent security - no critical vulnerabilities';
      color = 'text-green-700';
      bgColor = 'bg-green-100';
      eligibleForBadge = true;
      score = 95;
      break;
    case 'B+':
      description = 'Very good security - no critical vulnerabilities';
      color = 'text-lime-700';
      bgColor = 'bg-lime-100';
      eligibleForBadge = false;
      score = 88;
      break;
    case 'B':
      description = 'Good security - no critical vulnerabilities';
      color = 'text-yellow-700';
      bgColor = 'bg-yellow-100';
      eligibleForBadge = false;
      score = 80;
      break;
    case 'C+':
      description = 'Moderate security - 1 critical vulnerability found';
      color = 'text-orange-700';
      bgColor = 'bg-orange-100';
      eligibleForBadge = false;
      score = 70;
      break;
    case 'C':
      description = 'Significant security issues - multiple critical vulnerabilities';
      color = 'text-red-600';
      bgColor = 'bg-red-100';
      eligibleForBadge = false;
      score = 60;
      break;
    case 'D':
      description = 'Major security vulnerabilities - immediate attention required';
      color = 'text-red-700';
      bgColor = 'bg-red-200';
      eligibleForBadge = false;
      score = 40;
      break;
    case 'F':
      description = 'Critical security failures - urgent action required';
      color = 'text-red-800';
      bgColor = 'bg-red-300';
      eligibleForBadge = false;
      score = 20;
      break;
    case 'N/A':
    default:
      description = 'No completed scans available';
      color = 'text-gray-600';
      bgColor = 'bg-gray-100';
      eligibleForBadge = false;
      score = 0;
      break;
  }

  return {
    grade,
    score,
    eligibleForBadge,
    description,
    color,
    bgColor
  };
}

export function getGradeRequirements(grade: SecurityGrade): string[] {
  const requirements: string[] = [];
  const gradeInfo = getSecurityGradeInfo(grade);

  if (!gradeInfo.eligibleForBadge) {
    if (grade === 'N/A') {
      requirements.push('Complete your first security scan');
    } else if (grade.startsWith('C') || grade === 'D' || grade === 'F') {
      requirements.push('Fix all critical vulnerabilities');
      requirements.push('Achieve Grade A or A+ for badge eligibility');
    } else {
      requirements.push('Resolve high-severity security issues');
      requirements.push('Achieve Grade A or A+ for badge eligibility');
    }
  }

  return requirements;
}

export function getBadgeEligibilityStatus(vulnerabilities: VulnerabilityCount, scanCount: number): {
  eligible: boolean;
  reason?: string;
} {
  const grade = calculateSecurityGrade(vulnerabilities, scanCount);
  const gradeInfo = getSecurityGradeInfo(grade);

  if (scanCount === 0) {
    return { eligible: false, reason: 'Complete your first security scan' };
  }

  if (!gradeInfo.eligibleForBadge) {
    return { eligible: false, reason: `Grade ${grade} is not eligible - Grade A or A+ required` };
  }

  return { eligible: true };
}