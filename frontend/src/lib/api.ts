import { 
  User, 
  AuthTokens, 
  LoginRequest, 
  RegisterRequest,
  Scan,
  CreateScanRequest,
  Vulnerability,
  ScanSummary,
  VulnerabilitySummary,
  ApiError
} from '@/types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';

class ApiClient {
  private baseURL: string;
  private accessToken: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    // Load token from localStorage if available
    if (typeof window !== 'undefined') {
      this.accessToken = localStorage.getItem('access_token');
    }
  }

  setAccessToken(token: string) {
    this.accessToken = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', token);
    }
  }

  clearTokens() {
    this.accessToken = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    retryOnAuth = true
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    if (this.accessToken) {
      headers.Authorization = `Bearer ${this.accessToken}`;
    }

    const config: RequestInit = {
      ...options,
      headers,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        // If 401 and we have a refresh token, try to refresh
        if (response.status === 401 && retryOnAuth && typeof window !== 'undefined') {
          const refreshToken = localStorage.getItem('refresh_token');
          if (refreshToken) {
            try {
              await this.refreshAccessToken(refreshToken);
              // Retry the original request with new token
              return this.request<T>(endpoint, options, false); // Don't retry again
            } catch {
              // Refresh failed, clear tokens and redirect to login
              this.clearTokens();
              if (typeof window !== 'undefined') {
                window.location.href = '/auth/login';
              }
              throw new Error('Session expired. Please login again.');
            }
          }
        }

        const errorData: ApiError = await response.json().catch(() => ({
          detail: 'An unexpected error occurred',
          status_code: response.status,
        }));
        throw new Error(errorData.detail || `HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Network error occurred');
    }
  }

  async refreshAccessToken(refreshToken: string): Promise<void> {
    const response = await fetch(`${this.baseURL}/api/v1/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh_token: refreshToken }),
    });

    if (!response.ok) {
      throw new Error('Failed to refresh token');
    }

    const tokens: AuthTokens = await response.json();
    this.setAccessToken(tokens.access_token);

    // Update refresh token if provided
    if (tokens.refresh_token && typeof window !== 'undefined') {
      localStorage.setItem('refresh_token', tokens.refresh_token);
    }
  }

  // Auth endpoints
  async login(credentials: LoginRequest): Promise<AuthTokens> {
    const response = await fetch(`${this.baseURL}/api/v1/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({
        detail: 'Login failed',
      }));
      throw new Error(errorData.detail || 'Login failed');
    }

    const tokens: AuthTokens = await response.json();
    this.setAccessToken(tokens.access_token);
    return tokens;
  }

  async register(userData: RegisterRequest): Promise<User> {
    return this.request<User>('/api/v1/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getCurrentUser(): Promise<User> {
    return this.request<User>('/api/v1/users/me');
  }

  async updateProfile(profileData: {
    full_name?: string;
    email?: string;
    phone?: string;
    location?: string;
  }): Promise<User> {
    return this.request<User>('/api/v1/users/me', {
      method: 'PATCH',
      body: JSON.stringify(profileData),
    });
  }

  async changePassword(passwordData: {
    current_password: string;
    new_password: string;
  }): Promise<{ message: string }> {
    return this.request<{ message: string }>('/api/v1/users/me/password', {
      method: 'POST',
      body: JSON.stringify(passwordData),
    });
  }

  async updateNotificationSettings(settings: {
    email_notifications?: boolean;
    browser_notifications?: boolean;
    scan_completion?: boolean;
    vulnerability_alerts?: boolean;
    weekly_reports?: boolean;
  }): Promise<{ message: string }> {
    return this.request<{ message: string }>('/api/v1/users/me/notifications', {
      method: 'PATCH',
      body: JSON.stringify(settings),
    });
  }

  async logout(): Promise<void> {
    // For JWT tokens, logout is handled client-side by clearing tokens
    // No need to call the backend since JWT tokens are stateless
    this.clearTokens();
  }

  // Scan endpoints
  async getScans(params?: {
    skip?: number;
    limit?: number;
    status?: string;
    scan_type?: string;
  }): Promise<Scan[]> {
    const searchParams = new URLSearchParams();
    if (params?.skip) searchParams.append('skip', params.skip.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.status) searchParams.append('status', params.status);
    if (params?.scan_type) searchParams.append('scan_type', params.scan_type);

    const query = searchParams.toString();
    return this.request<Scan[]>(`/api/scans${query ? `?${query}` : ''}`);
  }

  async getScan(scanId: number | string): Promise<Scan> {
    return this.request<Scan>(`/api/scans/${scanId}`);
  }

  async createScan(scanData: CreateScanRequest): Promise<Scan> {
    return this.request<Scan>('/api/scans', {
      method: 'POST',
      body: JSON.stringify(scanData),
    });
  }

  async cancelScan(scanId: number): Promise<Scan> {
    return this.request<Scan>(`/api/scans/${scanId}/cancel`, {
      method: 'POST',
    });
  }

  async deleteScan(scanId: number): Promise<void> {
    return this.request<void>(`/api/scans/${scanId}`, {
      method: 'DELETE',
    });
  }

  async getScanStatus(scanId: number | string): Promise<{
    scan_id: number | string;
    status: string;
    progress_percentage: number;
    error_message?: string;
    created_at: string;
    started_at?: string;
    completed_at?: string;
  }> {
    // Stub for now - not implemented
    return Promise.resolve({
      scan_id: scanId,
      status: 'pending',
      progress_percentage: 0,
      created_at: new Date().toISOString()
    });
  }

  // Vulnerability endpoints
  async getVulnerabilities(
    scanId: number | string,
    params?: {
      skip?: number;
      limit?: number;
      severity?: string;
      category?: string;
    }
  ): Promise<Vulnerability[]> {
    // Stub for now - not implemented
    return Promise.resolve([]);
  }

  async updateVulnerability(
    vulnerabilityId: number,
    updates: {
      is_false_positive?: boolean;
      is_resolved?: boolean;
      resolution_notes?: string;
    }
  ): Promise<Vulnerability> {
    return this.request<Vulnerability>(`/api/v1/vulnerabilities/${vulnerabilityId}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    });
  }

  // Dashboard endpoints
  async getScanSummary(): Promise<ScanSummary> {
    // Stub for now - not implemented
    return Promise.resolve({
      total_scans: 0,
      scans_today: 0,
      scans_this_week: 0,
      scans_this_month: 0,
      scans_by_status: {},
      scans_by_type: {}
    });
  }

  async getVulnerabilitySummary(): Promise<VulnerabilitySummary> {
    // Stub for now - not implemented
    return Promise.resolve({
      total_vulnerabilities: 0,
      critical_count: 0,
      high_count: 0,
      medium_count: 0,
      low_count: 0,
      info_count: 0,
      vulnerabilities_by_category: {},
      top_vulnerable_targets: []
    });
  }

  // Report endpoints
  async getScanReport(scanId: number, format: 'json' | 'pdf' | 'html' = 'json'): Promise<unknown> {
    return this.request(`/api/v1/scans/${scanId}/report?format=${format}`);
  }

  async downloadScanReport(scanId: number, format: 'json' | 'pdf' | 'html' = 'json'): Promise<Blob> {
    const url = `${this.baseURL}/api/v1/scans/${scanId}/report?format=${format}`;

    const headers: HeadersInit = {};
    if (this.accessToken) {
      headers.Authorization = `Bearer ${this.accessToken}`;
    }

    const response = await fetch(url, { headers });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({
        detail: 'Failed to download report',
      }));
      throw new Error(errorData.detail || 'Failed to download report');
    }

    return response.blob();
  }

  // Notification endpoints - stubbed for now
  async getNotifications(params?: {
    skip?: number;
    limit?: number;
    unread_only?: boolean;
    notification_type?: string;
  }): Promise<Array<{ id: number; message: string; is_read: boolean; created_at: string }>> {
    // Return empty array for now - notifications not implemented
    return Promise.resolve([]);
  }

  async getUnreadNotificationCount(): Promise<{ unread_count: number }> {
    // Return 0 for now - notifications not implemented
    return Promise.resolve({ unread_count: 0 });
  }

  async markNotificationRead(notificationId: number): Promise<{ message: string }> {
    // Stub - notifications not implemented
    return Promise.resolve({ message: 'OK' });
  }

  async markNotificationsRead(notificationIds: number[]): Promise<{ message: string }> {
    // Stub - notifications not implemented
    return Promise.resolve({ message: 'OK' });
  }

  async markAllNotificationsRead(): Promise<{ message: string }> {
    // Stub - notifications not implemented
    return Promise.resolve({ message: 'OK' });
  }

  async deleteNotification(notificationId: number): Promise<{ message: string }> {
    // Stub - notifications not implemented
    return Promise.resolve({ message: 'OK' });
  }

  // Health check
  async healthCheck(): Promise<{ status: string; service: string }> {
    return this.request<{ status: string; service: string }>('/api/v1/health/');
  }
}

export const apiClient = new ApiClient(API_BASE_URL);
export default apiClient;
