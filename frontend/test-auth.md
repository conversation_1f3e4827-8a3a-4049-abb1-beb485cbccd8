# Authentication Flow Test

## Test Steps

1. **Initial Load Test**
   - Open http://localhost:3001/
   - Should show landing page (not logged in)
   - Check console for any auth errors

2. **Login Test**
   - Click login button
   - Enter credentials: ka<PERSON><PERSON><PERSON>@gmail.com / password
   - Should redirect to dashboard after successful login
   - Check console for auth state changes

3. **Dashboard Access Test**
   - Should see dashboard with user info
   - Check that user profile loads correctly
   - Verify no loading loops or redirects

4. **Refresh Test (Critical)**
   - While on dashboard, refresh the page (F5 or Cmd+R)
   - Should stay on dashboard without redirecting to login
   - Should not show infinite loading
   - Check console for auth state initialization

5. **Direct URL Test**
   - Navigate directly to http://localhost:3001/dashboard
   - Should either:
     - Show dashboard if authenticated
     - Redirect to login if not authenticated

## Expected Behavior

- No infinite redirects
- No auth timeout errors
- Smooth transitions between authenticated/unauthenticated states
- Dashboard accessible after refresh when logged in

## Console Logs to Watch For

- "Auth state change: INITIAL_SESSION"
- "Auth state change: SIGNED_IN"
- "Token refreshed"
- No "Auth check timeout" errors
- No infinite redirect loops
