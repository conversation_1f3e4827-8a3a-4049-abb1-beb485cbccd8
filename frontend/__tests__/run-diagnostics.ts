#!/usr/bin/env node

import { config } from 'dotenv'
import { resolve } from 'path'

// Load environment variables
config({ path: resolve(__dirname, '../.env.local') })

console.log('🔍 MostlySecure Scan System Diagnostics')
console.log('=====================================\n')

console.log('📋 Environment Configuration:')
console.log('  NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)
console.log('  NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20) + '...')
console.log('  SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY?.substring(0, 20) + '...')
console.log('  NODE_ENV:', process.env.NODE_ENV)
console.log()

console.log('📝 Test Instructions:')
console.log('1. Make sure the Next.js dev server is running: npm run dev')
console.log('2. Make sure Supabase Edge Functions are deployed:')
console.log('   cd .. && supabase functions deploy')
console.log('3. Run the tests:')
console.log('   npm test')
console.log()

console.log('🧪 Running specific test suites:')
console.log('  - Edge Function tests: npm test edge-function.test.ts')
console.log('  - Scan flow tests: npm test scan-flow.test.ts')
console.log('  - API tests: npm test scans.test.ts')
console.log()

console.log('🔧 Common Issues:')
console.log('1. Edge Functions not deployed:')
console.log('   - Run: cd .. && supabase functions deploy')
console.log()
console.log('2. Wrong Supabase URL or keys:')
console.log('   - Check .env.local has correct values')
console.log('   - Ensure using the same Supabase project as Edge Functions')
console.log()
console.log('3. CORS issues:')
console.log('   - Edge Functions should have CORS headers configured')
console.log('   - Check if functions are accessible from browser')
console.log()
console.log('4. Authentication issues:')
console.log('   - Service role key needed for admin operations')
console.log('   - Anon key needed for client operations')
console.log()

// Quick connectivity test
async function quickTest() {
  console.log('🚀 Running quick connectivity test...\n')
  
  try {
    const { createClient } = await import('@supabase/supabase-js')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )
    
    // Test database connection
    console.log('📊 Testing database connection...')
    const { count, error: dbError } = await supabase
      .from('scans')
      .select('*', { count: 'exact', head: true })
    
    if (dbError) {
      console.error('  ❌ Database error:', dbError.message)
    } else {
      console.log('  ✅ Database connected! Total scans:', count)
    }
    
    // Test Edge Function
    console.log('\n🔧 Testing Edge Function...')
    const { data, error } = await supabase.functions.invoke('scan-orchestrator', {
      body: { test: true },
    })
    
    if (error) {
      console.error('  ❌ Edge Function error:', error.message)
      console.log('  💡 Make sure Edge Functions are deployed!')
    } else {
      console.log('  ✅ Edge Function responded:', data)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run quick test
quickTest().then(() => {
  console.log('\n✅ Diagnostics complete!')
}).catch(console.error)