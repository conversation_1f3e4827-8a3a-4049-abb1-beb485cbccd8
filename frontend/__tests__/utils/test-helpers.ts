import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/supabase'

// Test environment configuration
export const TEST_CONFIG = {
  supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL!,
  supabaseAnonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  testTimeout: 60000, // 60 seconds for edge function tests
}

// Create test Supabase clients
export function createTestClient() {
  return createClient<Database>(
    TEST_CONFIG.supabaseUrl,
    TEST_CONFIG.supabaseAnonKey
  )
}

export function createServiceClient() {
  return createClient<Database>(
    TEST_CONFIG.supabaseUrl,
    TEST_CONFIG.supabaseServiceKey
  )
}

// Test user management
export async function createTestUser() {
  const client = createServiceClient()
  const email = `test-${Date.now()}@example.com`
  const password = 'TestPassword123!'
  
  const { data: authData, error: authError } = await client.auth.admin.createUser({
    email,
    password,
    email_confirm: true,
  })
  
  if (authError) throw authError
  
  // Wait for profile to be created by trigger
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  const { data: profile } = await client
    .from('profiles')
    .select('*')
    .eq('id', authData.user.id)
    .single()
  
  return {
    user: authData.user,
    profile,
    email,
    password,
  }
}

export async function deleteTestUser(userId: string) {
  const client = createServiceClient()
  await client.auth.admin.deleteUser(userId)
}

// Scan management
export async function createTestScan(userId: string, scanType = 'database_security') {
  const client = createServiceClient()
  
  const { data, error } = await client
    .from('scans')
    .insert({
      user_id: userId,
      scan_name: `Test ${scanType} scan`,
      target_url: 'https://example.com',
      scan_type: scanType as any,
      status: 'pending',
      progress: 0,
    })
    .select()
    .single()
  
  if (error) throw error
  return data
}

export async function cleanupTestScans(userId: string) {
  const client = createServiceClient()
  await client
    .from('scans')
    .delete()
    .eq('user_id', userId)
}

// Wait for scan to complete
export async function waitForScanCompletion(scanId: string, maxWaitTime = 30000) {
  const client = createServiceClient()
  const startTime = Date.now()
  
  while (Date.now() - startTime < maxWaitTime) {
    const { data: scan } = await client
      .from('scans')
      .select('status, progress')
      .eq('id', scanId)
      .single()
    
    if (scan?.status === 'completed' || scan?.status === 'failed') {
      return scan
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  throw new Error(`Scan ${scanId} did not complete within ${maxWaitTime}ms`)
}

// Log helpers for debugging
export function logScanDetails(scanId: string) {
  return async () => {
    const client = createServiceClient()
    
    const { data: scan } = await client
      .from('scans')
      .select('*')
      .eq('id', scanId)
      .single()
    
    const { data: logs } = await client
      .from('scan_logs')
      .select('*')
      .eq('scan_id', scanId)
      .order('created_at', { ascending: true })
    
    const { data: vulnerabilities } = await client
      .from('vulnerabilities')
      .select('*')
      .eq('scan_id', scanId)
    
    console.log('=== Scan Details ===')
    console.log('Scan:', scan)
    console.log('Logs:', logs)
    console.log('Vulnerabilities:', vulnerabilities)
    console.log('==================')
  }
}

// API request helpers
export async function apiRequest(
  path: string,
  options: RequestInit = {},
  authToken?: string
) {
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'
  const url = `${baseUrl}${path}`
  
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...options.headers,
  }
  
  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`
  }
  
  const response = await fetch(url, {
    ...options,
    headers,
  })
  
  const data = await response.json()
  
  return {
    response,
    data,
    status: response.status,
    ok: response.ok,
  }
}