export const mockScans = {
  database: {
    scan_name: 'Database Security Test',
    scan_type: 'database_security',
    target_url: 'https://example.com',
    scan_description: 'Testing database security scan',
  },
  secrets: {
    scan_name: 'Secrets Scan Test',
    scan_type: 'secrets',
    target_url: 'https://example.com',
    scan_description: 'Testing secrets detection',
  },
  comprehensive: {
    scan_name: 'Comprehensive Scan Test',
    scan_type: 'comprehensive',
    target_url: 'https://example.com',
    scan_description: 'Testing all scanners',
  },
}

export const mockVulnerabilities = [
  {
    title: 'SQL Injection Vulnerability',
    description: 'Endpoint is vulnerable to SQL injection',
    severity: 'critical',
    affected_component: 'https://example.com/api/users',
    owasp_category: 'A03:2021 - Injection',
    evidence: { query: "' OR '1'='1", response: '200 OK' },
    recommendation: 'Use parameterized queries',
  },
  {
    title: 'Exposed API Key',
    description: 'API key found in JavaScript file',
    severity: 'high',
    affected_component: 'https://example.com/js/app.js',
    owasp_category: 'A02:2021 - Cryptographic Failures',
    evidence: { key: 'sk_test_4eC39HqLyjWDarjtT1zdp7dc' },
    recommendation: 'Remove API keys from client-side code',
  },
]

export const testUrls = {
  vulnerable: 'http://testphp.vulnweb.com',
  firebase: 'https://fir-demo-project.firebaseapp.com',
  webhook: 'https://webhook.site',
  safe: 'https://example.com',
}