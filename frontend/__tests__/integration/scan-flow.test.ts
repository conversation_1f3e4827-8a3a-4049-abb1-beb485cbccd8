import { 
  createTestUser, 
  deleteTestUser, 
  createServiceClient,
  waitForScanCompletion,
  logScanDetails,
  TEST_CONFIG
} from '../utils/test-helpers'
import { mockScans, testUrls } from '../utils/mock-data'

describe('Scan Flow Integration Tests', () => {
  let testUser: any
  let authToken: string
  let serviceClient: any

  beforeAll(async () => {
    // Create test user
    testUser = await createTestUser()
    serviceClient = createServiceClient()
    
    // Get auth token
    const { data: session } = await serviceClient.auth.signInWithPassword({
      email: testUser.email,
      password: testUser.password,
    })
    authToken = session?.session?.access_token || ''
  }, TEST_CONFIG.testTimeout)

  afterAll(async () => {
    // Cleanup
    if (testUser?.user?.id) {
      await deleteTestUser(testUser.user.id)
    }
  })

  describe('Scan Creation and Execution', () => {
    test('should create and execute a database security scan', async () => {
      console.log('🔍 Testing database security scan...')
      
      // Create scan via API
      const response = await fetch('http://localhost:3000/api/scans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          ...mockScans.database,
          target_url: testUrls.vulnerable,
        }),
      })

      expect(response.ok).toBe(true)
      const scan = await response.json()
      
      console.log('📋 Created scan:', scan.id)
      expect(scan.id).toBeDefined()
      expect(scan.status).toBe('queued')
      
      // Wait for scan to start
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Check if Edge Function was invoked
      const { data: updatedScan } = await serviceClient
        .from('scans')
        .select('*')
        .eq('id', scan.id)
        .single()
      
      console.log('📊 Scan status after 2s:', updatedScan?.status, 'Progress:', updatedScan?.progress)
      
      // Wait for completion
      try {
        const completedScan = await waitForScanCompletion(scan.id, 30000)
        console.log('✅ Scan completed:', completedScan)
        
        // Verify vulnerabilities were found
        const { data: vulnerabilities } = await serviceClient
          .from('vulnerabilities')
          .select('*')
          .eq('scan_id', scan.id)
        
        console.log(`🔍 Found ${vulnerabilities?.length || 0} vulnerabilities`)
        expect(vulnerabilities).toBeDefined()
        expect(vulnerabilities.length).toBeGreaterThan(0)
        
        // Check scan counts were updated
        const { data: finalScan } = await serviceClient
          .from('scans')
          .select('*')
          .eq('id', scan.id)
          .single()
        
        expect(finalScan.vulnerabilities_count).toBeGreaterThan(0)
        expect(finalScan.status).toBe('completed')
        
      } catch (error) {
        // Log details for debugging
        await logScanDetails(scan.id)()
        throw error
      }
    }, TEST_CONFIG.testTimeout)

    test('should handle edge function invocation correctly', async () => {
      console.log('🔧 Testing direct Edge Function invocation...')
      
      // Create a scan directly in database
      const { data: scan, error } = await serviceClient
        .from('scans')
        .insert({
          user_id: testUser.user.id,
          scan_name: 'Direct Edge Function Test',
          target_url: 'https://example.com',
          scan_type: 'secrets',
          status: 'pending',
          progress: 0,
        })
        .select()
        .single()
      
      expect(error).toBeNull()
      expect(scan).toBeDefined()
      
      // Invoke Edge Function directly
      console.log('🚀 Invoking scan-orchestrator Edge Function...')
      const { data: invokeData, error: invokeError } = await serviceClient.functions.invoke('scan-orchestrator', {
        body: {
          scanId: scan.id,
          scanType: scan.scan_type,
          targetUrl: scan.target_url,
          config: {},
        },
      })
      
      console.log('📨 Edge Function response:', { data: invokeData, error: invokeError })
      
      if (invokeError) {
        console.error('❌ Edge Function error:', invokeError)
        await logScanDetails(scan.id)()
      }
      
      expect(invokeError).toBeNull()
      expect(invokeData).toBeDefined()
      
      // Wait a bit for processing
      await new Promise(resolve => setTimeout(resolve, 5000))
      
      // Check scan status
      const { data: updatedScan } = await serviceClient
        .from('scans')
        .select('*')
        .eq('id', scan.id)
        .single()
      
      console.log('📊 Scan after Edge Function:', {
        status: updatedScan?.status,
        progress: updatedScan?.progress,
        vulnerabilities_count: updatedScan?.vulnerabilities_count,
      })
      
      // The scan should at least be running or completed
      expect(['running', 'completed', 'failed']).toContain(updatedScan?.status)
    }, TEST_CONFIG.testTimeout)

    test('should track scan progress in real-time', async () => {
      console.log('⏱️  Testing real-time scan progress...')
      
      // Create scan
      const response = await fetch('http://localhost:3000/api/scans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          ...mockScans.secrets,
          target_url: testUrls.vulnerable,
        }),
      })

      const scan = await response.json()
      const progressUpdates: any[] = []
      
      // Monitor progress
      const startTime = Date.now()
      while (Date.now() - startTime < 20000) { // 20 seconds max
        const { data: currentScan } = await serviceClient
          .from('scans')
          .select('status, progress')
          .eq('id', scan.id)
          .single()
        
        progressUpdates.push({
          time: Date.now() - startTime,
          status: currentScan?.status,
          progress: currentScan?.progress,
        })
        
        if (currentScan?.status === 'completed' || currentScan?.status === 'failed') {
          break
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
      
      console.log('📈 Progress updates:', progressUpdates)
      
      // Should have multiple progress updates
      expect(progressUpdates.length).toBeGreaterThan(1)
      
      // Progress should increase
      const progressValues = progressUpdates.map(u => u.progress).filter(p => p > 0)
      if (progressValues.length > 1) {
        const isIncreasing = progressValues.every((val, i) => 
          i === 0 || val >= progressValues[i - 1]
        )
        expect(isIncreasing).toBe(true)
      }
    }, TEST_CONFIG.testTimeout)

    test('should save vulnerabilities to database', async () => {
      console.log('🛡️  Testing vulnerability storage...')
      
      // Create a comprehensive scan to ensure vulnerabilities
      const response = await fetch('http://localhost:3000/api/scans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          scan_name: 'Vulnerability Storage Test',
          scan_type: 'comprehensive',
          target_url: testUrls.vulnerable,
        }),
      })

      const scan = await response.json()
      console.log('📋 Created comprehensive scan:', scan.id)
      
      // Wait for completion
      const completedScan = await waitForScanCompletion(scan.id, 60000)
      
      // Check vulnerabilities
      const { data: vulnerabilities } = await serviceClient
        .from('vulnerabilities')
        .select('*')
        .eq('scan_id', scan.id)
        .order('severity', { ascending: false })
      
      console.log(`🔍 Vulnerability summary:`)
      const severityCounts = vulnerabilities?.reduce((acc: any, vuln: any) => {
        acc[vuln.severity] = (acc[vuln.severity] || 0) + 1
        return acc
      }, {})
      console.log(severityCounts)
      
      // Sample vulnerabilities
      vulnerabilities?.slice(0, 3).forEach((vuln: any) => {
        console.log(`  - ${vuln.title} (${vuln.severity})`)
        console.log(`    ${vuln.description}`)
      })
      
      expect(vulnerabilities).toBeDefined()
      expect(vulnerabilities.length).toBeGreaterThan(0)
      
      // Verify vulnerability structure
      const vuln = vulnerabilities[0]
      expect(vuln).toHaveProperty('title')
      expect(vuln).toHaveProperty('description')
      expect(vuln).toHaveProperty('severity')
      expect(vuln).toHaveProperty('affected_component')
      expect(vuln).toHaveProperty('scan_id', scan.id)
    }, TEST_CONFIG.testTimeout)
  })

  describe('Edge Function Diagnostics', () => {
    test('should log edge function URL and response', async () => {
      console.log('🔍 Diagnosing Edge Function configuration...')
      
      // Get Supabase URL
      console.log('📍 Supabase URL:', TEST_CONFIG.supabaseUrl)
      console.log('📍 Expected Edge Function URL:', `${TEST_CONFIG.supabaseUrl}/functions/v1/scan-orchestrator`)
      
      // Test direct Edge Function call
      const testScan = {
        scanId: 'test-' + Date.now(),
        scanType: 'database_security',
        targetUrl: 'https://example.com',
        config: {},
      }
      
      console.log('📤 Sending test payload:', testScan)
      
      const { data, error } = await serviceClient.functions.invoke('scan-orchestrator', {
        body: testScan,
      })
      
      console.log('📥 Edge Function response:')
      console.log('  Data:', JSON.stringify(data, null, 2))
      console.log('  Error:', error)
      
      // Test if Edge Functions are deployed
      const functionsUrl = `${TEST_CONFIG.supabaseUrl}/functions/v1`
      console.log('📍 Testing Edge Functions endpoint:', functionsUrl)
      
      try {
        const healthResponse = await fetch(`${functionsUrl}/scan-orchestrator`, {
          method: 'OPTIONS',
          headers: {
            'Authorization': `Bearer ${TEST_CONFIG.supabaseAnonKey}`,
          },
        })
        console.log('  CORS preflight response:', healthResponse.status, healthResponse.statusText)
      } catch (err) {
        console.error('  Failed to reach Edge Functions:', err)
      }
    })
  })
})