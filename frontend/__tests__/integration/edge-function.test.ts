import { createServiceClient, TEST_CONFIG } from '../utils/test-helpers'

describe('Edge Function Direct Testing', () => {
  let serviceClient: any

  beforeAll(() => {
    serviceClient = createServiceClient()
  })

  test('should verify Edge Functions are accessible', async () => {
    console.log('\n🔍 Edge Function Accessibility Test\n')
    console.log('Supabase URL:', TEST_CONFIG.supabaseUrl)
    console.log('Service Key:', TEST_CONFIG.supabaseServiceKey?.substring(0, 20) + '...')
    
    // List of edge functions to test
    const edgeFunctions = [
      'scan-orchestrator',
      'scan-database',
      'scan-secrets',
      'scan-firebase',
      'scan-webhooks',
      'scan-api-endpoints',
    ]

    for (const functionName of edgeFunctions) {
      console.log(`\n📍 Testing ${functionName}...`)
      
      try {
        // Test CORS preflight
        const corsResponse = await fetch(`${TEST_CONFIG.supabaseUrl}/functions/v1/${functionName}`, {
          method: 'OPTIONS',
          headers: {
            'Authorization': `Bearer ${TEST_CONFIG.supabaseAnonKey}`,
          },
        })
        
        console.log(`  CORS Response: ${corsResponse.status} ${corsResponse.statusText}`)
        console.log(`  Headers:`, Object.fromEntries(corsResponse.headers.entries()))
        
      } catch (error) {
        console.error(`  ❌ Failed to reach ${functionName}:`, error)
      }
    }
  })

  test('should invoke scan-orchestrator with test payload', async () => {
    console.log('\n🚀 Direct Edge Function Invocation Test\n')
    
    // Create a minimal test scan
    const testScanId = `test-${Date.now()}`
    
    // Test payload
    const payload = {
      scanId: testScanId,
      scanType: 'database_security',
      targetUrl: 'https://example.com',
      config: {},
    }
    
    console.log('📤 Payload:', JSON.stringify(payload, null, 2))
    
    // Method 1: Using Supabase client
    console.log('\n📍 Method 1: Using Supabase client .functions.invoke()')
    const { data: data1, error: error1 } = await serviceClient.functions.invoke('scan-orchestrator', {
      body: payload,
    })
    
    console.log('Response:')
    console.log('  Data:', data1)
    console.log('  Error:', error1)
    
    // Method 2: Direct HTTP call
    console.log('\n📍 Method 2: Direct HTTP POST')
    try {
      const response = await fetch(`${TEST_CONFIG.supabaseUrl}/functions/v1/scan-orchestrator`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_CONFIG.supabaseServiceKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })
      
      console.log('  Status:', response.status, response.statusText)
      console.log('  Headers:', Object.fromEntries(response.headers.entries()))
      
      const responseText = await response.text()
      console.log('  Body:', responseText)
      
      try {
        const responseData = JSON.parse(responseText)
        console.log('  Parsed:', responseData)
      } catch {
        console.log('  (Response is not JSON)')
      }
    } catch (error) {
      console.error('  ❌ HTTP Error:', error)
    }
  })

  test('should check if Edge Functions are deployed', async () => {
    console.log('\n🔍 Edge Function Deployment Check\n')
    
    // Check Supabase project status
    const projectUrl = TEST_CONFIG.supabaseUrl
    const projectId = projectUrl.split('.')[0].replace('https://', '')
    
    console.log('Project ID:', projectId)
    console.log('Project URL:', projectUrl)
    
    // Test a simple edge function
    const testPayload = { test: true, timestamp: new Date().toISOString() }
    
    console.log('\n📍 Testing scan-database function...')
    const { data, error } = await serviceClient.functions.invoke('scan-database', {
      body: {
        scanId: 'test-deployment-check',
        targetUrl: 'https://example.com',
      },
    })
    
    if (error) {
      console.error('❌ Error:', error)
      console.log('\n⚠️  Possible issues:')
      console.log('  1. Edge Functions not deployed: Run "supabase functions deploy"')
      console.log('  2. Wrong Supabase URL or keys in .env.local')
      console.log('  3. CORS not configured for Edge Functions')
      console.log('  4. Edge Functions runtime error')
    } else {
      console.log('✅ Success:', data)
    }
  })

  test('should test scan creation to edge function flow', async () => {
    console.log('\n🔄 Complete Flow Test: API -> Edge Function\n')
    
    // Create a test user and scan
    const { data: { user } } = await serviceClient.auth.admin.createUser({
      email: `flow-test-${Date.now()}@example.com`,
      password: 'TestPassword123!',
      email_confirm: true,
    })
    
    if (!user) {
      throw new Error('Failed to create test user')
    }
    
    try {
      // Create scan in database
      const { data: scan, error: scanError } = await serviceClient
        .from('scans')
        .insert({
          user_id: user.id,
          scan_name: 'Flow Test Scan',
          target_url: 'https://example.com',
          scan_type: 'secrets',
          status: 'pending',
          progress: 0,
        })
        .select()
        .single()
      
      if (scanError) {
        throw scanError
      }
      
      console.log('📋 Created scan:', scan.id)
      
      // Log the exact invocation
      console.log('\n📍 Invoking Edge Function...')
      console.log('Function: scan-orchestrator')
      console.log('Payload:', {
        scanId: scan.id,
        scanType: scan.scan_type,
        targetUrl: scan.target_url,
        config: scan.config || {},
      })
      
      const startTime = Date.now()
      const { data: invokeData, error: invokeError } = await serviceClient.functions.invoke('scan-orchestrator', {
        body: {
          scanId: scan.id,
          scanType: scan.scan_type,
          targetUrl: scan.target_url,
          config: scan.config || {},
        },
      })
      const invokeDuration = Date.now() - startTime
      
      console.log(`\n📊 Invocation took ${invokeDuration}ms`)
      console.log('Response data:', invokeData)
      console.log('Response error:', invokeError)
      
      // Check scan status after invocation
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      const { data: updatedScan } = await serviceClient
        .from('scans')
        .select('*')
        .eq('id', scan.id)
        .single()
      
      console.log('\n📋 Scan after Edge Function:')
      console.log('  Status:', updatedScan?.status)
      console.log('  Progress:', updatedScan?.progress)
      console.log('  Started at:', updatedScan?.started_at)
      
      // Check logs
      const { data: logs } = await serviceClient
        .from('scan_logs')
        .select('*')
        .eq('scan_id', scan.id)
        .order('created_at', { ascending: true })
      
      console.log('\n📋 Scan logs:')
      logs?.forEach((log: any) => {
        console.log(`  [${log.level}] ${log.message}`)
      })
      
    } finally {
      // Cleanup
      await serviceClient.auth.admin.deleteUser(user.id)
    }
  })
})