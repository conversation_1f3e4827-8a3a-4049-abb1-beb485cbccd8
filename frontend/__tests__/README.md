# MostlySecure Frontend Integration Tests

This test suite helps diagnose and verify the scan system functionality, particularly the integration between the frontend API routes and Supabase Edge Functions.

## Setup

1. Install dependencies:
```bash
cd frontend
npm install
```

2. Ensure your `.env.local` file has the correct Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-key
```

3. Deploy Edge Functions (from the root directory):
```bash
supabase functions deploy
```

4. Start the Next.js dev server:
```bash
npm run dev
```

## Running Tests

### Run all tests:
```bash
npm test
```

### Run specific test suites:
```bash
# Edge Function connectivity tests
npm test edge-function.test.ts

# Full scan flow integration tests  
npm test scan-flow.test.ts

# API route tests
npm test scans.test.ts
```

### Run diagnostics:
```bash
npx ts-node __tests__/run-diagnostics.ts
```

## Test Structure

- **`integration/scan-flow.test.ts`**: Tests the complete scan lifecycle from creation to completion
- **`integration/edge-function.test.ts`**: Tests direct Edge Function invocation and connectivity
- **`api/scans.test.ts`**: Tests the Next.js API routes
- **`utils/`**: Test helpers and mock data

## What These Tests Check

1. **Edge Function Deployment**: Verifies Edge Functions are accessible
2. **API Route Functionality**: Tests scan creation and retrieval
3. **Edge Function Invocation**: Ensures API routes properly invoke Edge Functions
4. **Scan Progress Tracking**: Monitors scan status updates
5. **Vulnerability Storage**: Verifies vulnerabilities are saved to the database

## Common Issues and Solutions

### Issue: Scans complete instantly without results
**Diagnosis**: Run `npm test edge-function.test.ts` to check if Edge Functions are being invoked.

**Solutions**:
- Ensure Edge Functions are deployed: `supabase functions deploy`
- Check Edge Function logs: `supabase functions logs`
- Verify Supabase URL and keys are correct

### Issue: Edge Function invocation fails
**Diagnosis**: Check the console output for specific error messages.

**Solutions**:
- CORS issues: Edge Functions should include CORS headers
- Authentication: Ensure service role key is correct
- Network: Check if Supabase project is accessible

### Issue: Tests timeout
**Solutions**:
- Increase test timeout in `jest.config.js`
- Check network connectivity to Supabase
- Ensure Edge Functions are not rate-limited

## Debugging Tips

1. **Enable verbose logging**: Tests include detailed console output
2. **Check scan logs**: Database stores detailed logs for each scan
3. **Monitor Edge Function logs**: `supabase functions logs scan-orchestrator`
4. **Use the diagnostics script**: `npx ts-node __tests__/run-diagnostics.ts`