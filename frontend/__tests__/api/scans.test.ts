import { 
  createTestUser, 
  deleteTestUser, 
  createServiceClient,
  apiRequest,
  getAuthSession,
  TEST_CONFIG
} from '../utils/test-helpers'
import { mockScans } from '../utils/mock-data'

describe('Scans API Route Tests', () => {
  let testUser: any
  let authCookies: string
  let serviceClient: any

  beforeAll(async () => {
    testUser = await createTestUser()
    serviceClient = createServiceClient()
    
    // Get auth session with cookies for API requests
    const authSession = await getAuthSession(testUser.email, testUser.password)
    authCookies = authSession.cookieString
  })

  afterEach(async () => {
    // Clean up scans after each test to avoid interference
    if (testUser?.user?.id) {
      await serviceClient
        .from('scans')
        .delete()
        .eq('user_id', testUser.user.id)
    }
  })
  
  afterAll(async () => {
    if (testUser?.user?.id) {
      await deleteTestUser(testUser.user.id)
    }
  })

  describe('POST /api/scans', () => {
    test('should create a new scan with valid data', async () => {
      const { response, data } = await apiRequest('/api/scans', {
        method: 'POST',
        body: JSON.stringify(mockScans.database),
      }, authCookies)

      expect(response.status).toBe(201)
      expect(data.id).toBeDefined()
      expect(data.scan_type).toBe(mockScans.database.scan_type)
      expect(data.target_url).toBe(mockScans.database.target_url)
      expect(data.status).toBe('queued')
    })

    test('should handle missing authentication', async () => {
      const { response, data } = await apiRequest('/api/scans', {
        method: 'POST',
        body: JSON.stringify(mockScans.database),
      })

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })

    test('should validate required fields', async () => {
      const { response, data } = await apiRequest('/api/scans', {
        method: 'POST',
        body: JSON.stringify({
          scan_name: 'Test',
          // Missing required fields
        }),
      }, authCookies)

      expect(response.status).toBe(400)
      expect(data.error).toContain('Missing required fields')
    })

    test('should validate scan type', async () => {
      const { response, data } = await apiRequest('/api/scans', {
        method: 'POST',
        body: JSON.stringify({
          ...mockScans.database,
          scan_type: 'invalid_type',
        }),
      }, authCookies)

      expect(response.status).toBe(400)
      expect(data.error).toContain('Invalid scan_type')
    })

    test('should enforce concurrent scan limits', async () => {
      // Update user's max concurrent scans to 1
      await serviceClient
        .from('profiles')
        .update({ max_concurrent_scans: 1 })
        .eq('id', testUser.user.id)

      // Create first scan
      const { response: response1, data: data1 } = await apiRequest('/api/scans', {
        method: 'POST',
        body: JSON.stringify(mockScans.database),
      }, authCookies)
      expect(response1.status).toBe(201)
      
      // Manually set the scan to running state to simulate it's in progress
      await serviceClient
        .from('scans')
        .update({ status: 'running' })
        .eq('id', data1.id)

      // Try to create second scan
      const { response: response2, data: data2 } = await apiRequest('/api/scans', {
        method: 'POST',
        body: JSON.stringify(mockScans.secrets),
      }, authCookies)

      expect(response2.status).toBe(429)
      expect(data2.error).toContain('Maximum concurrent scans limit')

      // Reset limit
      await serviceClient
        .from('profiles')
        .update({ max_concurrent_scans: 5 })
        .eq('id', testUser.user.id)
    })

    test('should provide default scan name if not provided', async () => {
      const { response, data } = await apiRequest('/api/scans', {
        method: 'POST',
        body: JSON.stringify({
          target_url: 'https://example.com',
          scan_type: 'secrets',
          // No scan_name provided
        }),
      }, authCookies)

      expect(response.status).toBe(201)
      expect(data.scan_name).toContain('secrets scan')
    })
  })

  describe('GET /api/scans', () => {
    beforeAll(async () => {
      // Create some test scans
      for (const scanType of ['database_security', 'secrets', 'firebase']) {
        await serviceClient
          .from('scans')
          .insert({
            user_id: testUser.user.id,
            scan_name: `Test ${scanType}`,
            target_url: 'https://example.com',
            scan_type: scanType,
            status: scanType === 'database_security' ? 'completed' : 'running',
            progress: scanType === 'database_security' ? 100 : 50,
          })
      }
    })

    test('should fetch user scans', async () => {
      const { response, data } = await apiRequest('/api/scans', {}, authCookies)

      expect(response.status).toBe(200)
      expect(Array.isArray(data)).toBe(true)
      expect(data.length).toBeGreaterThanOrEqual(3)
    })

    test('should filter by status', async () => {
      const { response, data } = await apiRequest('/api/scans?status=completed', {}, authCookies)

      expect(response.status).toBe(200)
      expect(data.every((scan: any) => scan.status === 'completed')).toBe(true)
    })

    test('should filter by scan type', async () => {
      const { response, data } = await apiRequest('/api/scans?scan_type=secrets', {}, authCookies)

      expect(response.status).toBe(200)
      expect(data.every((scan: any) => scan.scan_type === 'secrets')).toBe(true)
    })

    test('should support pagination', async () => {
      const { response, data } = await apiRequest('/api/scans?limit=2&skip=0', {}, authCookies)

      expect(response.status).toBe(200)
      expect(data.length).toBeLessThanOrEqual(2)
    })

    test('should require authentication', async () => {
      const { response, data } = await apiRequest('/api/scans')

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })
  })

  describe('Edge Function Invocation', () => {
    test('should log edge function invocation details', async () => {
      console.log('\n🔍 Testing Edge Function invocation from API route...\n')

      // Create a scan and monitor logs
      const originalLog = console.log
      const logs: string[] = []
      console.log = (...args) => {
        logs.push(args.join(' '))
        originalLog(...args)
      }

      const { response, data } = await apiRequest('/api/scans', {
        method: 'POST',
        body: JSON.stringify({
          ...mockScans.database,
          scan_name: 'Edge Function Test',
        }),
      }, authCookies)

      console.log = originalLog

      expect(response.status).toBe(201)
      
      // Check scan logs
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const { data: scanLogs } = await serviceClient
        .from('scan_logs')
        .select('*')
        .eq('scan_id', data.id)
        .order('created_at', { ascending: true })

      console.log('\n📋 Scan Logs:')
      scanLogs?.forEach((log: any) => {
        console.log(`  [${log.level}] ${log.message}`)
        if (log.details) {
          console.log(`    Details:`, JSON.stringify(log.details, null, 2))
        }
      })

      // Check if orchestrator was called
      const orchestratorLog = scanLogs?.find((log: any) => 
        log.message.includes('Starting') && log.message.includes('scan')
      )
      expect(orchestratorLog).toBeDefined()
    })
  })
})