# Database
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost/mostlysecure

# Redis
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-super-secret-key-here-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_MINUTES=10080

# CORS
ALLOWED_HOSTS=["*"]

# Scanning
MAX_CONCURRENT_SCANS=5
SCAN_TIMEOUT_SECONDS=300

# External APIs (optional)
VIRUSTOTAL_API_KEY=
SHODAN_API_KEY=

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=JSON

# Development
DEBUG=false