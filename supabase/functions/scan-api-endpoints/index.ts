import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Common API endpoints to test
const COMMON_ENDPOINTS = [
  '/api', '/api/v1', '/api/v2', '/api/v3',
  '/api/users', '/api/auth', '/api/login', '/api/register',
  '/api/admin', '/api/config', '/api/status', '/api/health',
  '/graphql', '/query', '/rest', '/data',
  '/.well-known/openapi.json', '/swagger.json', '/openapi.json',
  '/docs', '/api-docs', '/swagger-ui'
]

// Test patterns for API vulnerabilities
const API_TESTS = [
  {
    name: 'Authentication Bypass',
    tests: [
      { method: 'GET', headers: {} },
      { method: 'GET', headers: { 'Authorization': 'Bearer invalid' } },
      { method: 'GET', headers: { 'X-API-Key': 'test' } }
    ]
  },
  {
    name: 'HTTP Method Testing',
    tests: [
      { method: 'OPTIONS' },
      { method: 'TRACE' },
      { method: 'PUT' },
      { method: 'DELETE' },
      { method: 'PATCH' }
    ]
  }
]

async function discoverEndpoints(baseUrl: string): Promise<string[]> {
  const discovered = new Set<string>()
  
  for (const endpoint of COMMON_ENDPOINTS) {
    try {
      const response = await fetch(`${baseUrl}${endpoint}`, {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      })
      
      if (response.status !== 404) {
        discovered.add(endpoint)
      }
    } catch {
      // Ignore errors
    }
  }
  
  return Array.from(discovered)
}

async function testEndpoint(baseUrl: string, endpoint: string) {
  const vulnerabilities = []
  
  for (const testCategory of API_TESTS) {
    for (const test of testCategory.tests) {
      try {
        const response = await fetch(`${baseUrl}${endpoint}`, {
          method: test.method,
          headers: test.headers || {},
          signal: AbortSignal.timeout(5000)
        })
        
        // Check for potential vulnerabilities
        if (test.method === 'TRACE' && response.status === 200) {
          vulnerabilities.push({
            title: 'HTTP TRACE Method Enabled',
            severity: 'medium',
            endpoint,
            description: 'TRACE method is enabled which could lead to XST attacks'
          })
        }
        
        if (!test.headers?.Authorization && response.status === 200) {
          vulnerabilities.push({
            title: 'Missing Authentication',
            severity: 'high',
            endpoint,
            description: 'Endpoint accessible without authentication'
          })
        }
      } catch {
        // Ignore errors
      }
    }
  }
  
  return vulnerabilities
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { scanId, targetUrl } = await req.json()
    
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Log scan start
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Starting API endpoint discovery',
      details: { targetUrl }
    })

    // Discover endpoints
    const endpoints = await discoverEndpoints(targetUrl)
    
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: `Discovered ${endpoints.length} endpoints`,
      details: { endpoints }
    })

    // Test each endpoint
    const allVulnerabilities = []
    for (const endpoint of endpoints) {
      const vulns = await testEndpoint(targetUrl, endpoint)
      allVulnerabilities.push(...vulns)
    }

    // Save vulnerabilities to database
    if (allVulnerabilities.length > 0) {
      const vulnerabilityRecords = allVulnerabilities.map(vuln => ({
        scan_id: scanId,
        title: vuln.title,
        description: vuln.description,
        severity: vuln.severity,
        affected_component: vuln.endpoint,
        owasp_category: 'A07:2021 - Identification and Authentication Failures',
        evidence: {
          endpoint: vuln.endpoint,
          method: 'GET'
        },
        recommendation: 'Implement proper authentication and disable unnecessary HTTP methods'
      }))

      await supabase
        .from('vulnerabilities')
        .insert(vulnerabilityRecords)
    }

    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: `API scan completed. Found ${allVulnerabilities.length} vulnerabilities`,
      details: { vulnerabilityCount: allVulnerabilities.length }
    })

    return new Response(
      JSON.stringify({ 
        success: true,
        vulnerabilities: allVulnerabilities.length 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})
