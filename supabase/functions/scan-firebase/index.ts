import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Firebase configuration patterns and endpoints
const FIREBASE_PATTERNS = {
  // Firebase project domains
  domains: [
    '.firebaseio.com',
    '.firebaseapp.com',
    '.firebase.com',
    '.googleapis.com'
  ],
  
  // Firebase configuration files
  configFiles: [
    '/firebase.json',
    '/.firebaserc',
    '/firebase-config.js',
    '/firebase-config.json',
    '/google-services.json',
    '/GoogleService-Info.plist',
    '/__/firebase/init.js',
    '/__/firebase/init.json'
  ],
  
  // Firebase database endpoints
  databaseEndpoints: [
    '/.json',
    '/.json?shallow=true',
    '/.settings/rules.json',
    '/.settings/rules/.json',
    '/users.json',
    '/data.json',
    '/config.json'
  ],
  
  // Firebase storage endpoints
  storageEndpoints: [
    '/o',
    '/b/',
    '/v0/b/'
  ],
  
  // Firebase auth endpoints
  authEndpoints: [
    '/__/auth/handler',
    '/__/auth/iframe',
    '/__/auth/action'
  ]
}

// Security rule patterns to check
const INSECURE_RULES_PATTERNS = [
  /read\s*:\s*true/gi,
  /write\s*:\s*true/gi,
  /\.read\s*:\s*true/gi,
  /\.write\s*:\s*true/gi,
  /allow\s+read\s*:\s*if\s+true/gi,
  /allow\s+write\s*:\s*if\s+true/gi,
  /auth\s*!=\s*null.*false/gi,
  /auth\s*==\s*null.*true/gi
]

async function detectFirebaseProject(targetUrl: string): Promise<{isFirebase: boolean, projectId?: string, urls: string[]}> {
  const firebaseUrls = []
  let projectId = null
  let isFirebase = false
  
  // Check if the domain itself is a Firebase domain
  const url = new URL(targetUrl)
  if (FIREBASE_PATTERNS.domains.some(domain => url.hostname.includes(domain))) {
    isFirebase = true
    // Extract project ID from subdomain
    const match = url.hostname.match(/^([a-zA-Z0-9-]+)\.(firebaseio|firebaseapp)\.com/)
    if (match) {
      projectId = match[1]
    }
  }
  
  // Check for Firebase configuration files
  for (const configFile of FIREBASE_PATTERNS.configFiles) {
    try {
      const testUrl = new URL(configFile, targetUrl).toString()
      const response = await fetch(testUrl, {
        signal: AbortSignal.timeout(5000)
      })
      
      if (response.status === 200) {
        const content = await response.text()
        
        // Look for Firebase project ID in config
        const projectMatch = content.match(/"projectId"\s*:\s*"([^"]+)"/i) ||
                           content.match(/"project_id"\s*:\s*"([^"]+)"/i) ||
                           content.match(/PROJECT_ID['"]\s*:\s*['"]([^'"]+)/i)
        
        if (projectMatch) {
          projectId = projectMatch[1]
          isFirebase = true
        }
        
        // Extract Firebase URLs from config
        const urlMatches = content.matchAll(/https?:\/\/[a-zA-Z0-9-]+\.(firebaseio|firebaseapp|firebase)\.com[^\s"'<>]*/g)
        for (const match of urlMatches) {
          firebaseUrls.push(match[0])
        }
        
        firebaseUrls.push(testUrl)
      }
    } catch {
      // Ignore errors
    }
  }
  
  return { isFirebase, projectId, urls: [...new Set(firebaseUrls)] }
}

async function checkDatabaseSecurity(baseUrl: string, projectId?: string): Promise<any[]> {
  const vulnerabilities = []
  const testedUrls = new Set<string>()
  
  // Build list of URLs to test
  const urlsToTest = []
  
  if (projectId) {
    urlsToTest.push(`https://${projectId}.firebaseio.com`)
    urlsToTest.push(`https://${projectId}-default-rtdb.firebaseio.com`)
    urlsToTest.push(`https://${projectId}.firebasedatabase.app`)
  }
  
  // Also test the base URL if it's a Firebase domain
  const url = new URL(baseUrl)
  if (FIREBASE_PATTERNS.domains.some(domain => url.hostname.includes(domain))) {
    urlsToTest.push(baseUrl)
  }
  
  for (const testBaseUrl of urlsToTest) {
    if (testedUrls.has(testBaseUrl)) continue
    testedUrls.add(testBaseUrl)
    
    // Test database endpoints
    for (const endpoint of FIREBASE_PATTERNS.databaseEndpoints) {
      try {
        const testUrl = new URL(endpoint, testBaseUrl).toString()
        const response = await fetch(testUrl, {
          signal: AbortSignal.timeout(5000)
        })
        
        if (response.status === 200) {
          const content = await response.text()
          
          // Check if we got actual data (not error page)
          if (content.includes('{') || content.includes('[')) {
            vulnerabilities.push({
              title: 'Firebase Realtime Database Publicly Accessible',
              severity: 'critical',
              description: `Firebase database is readable without authentication at ${endpoint}`,
              endpoint: testUrl,
              evidence: content.substring(0, 200)
            })
            
            // Try to write data (safe test)
            try {
              const writeResponse = await fetch(`${testBaseUrl}/test_security_${Date.now()}.json`, {
                method: 'PUT',
                body: JSON.stringify({ test: 'security_check' }),
                headers: { 'Content-Type': 'application/json' },
                signal: AbortSignal.timeout(5000)
              })
              
              if (writeResponse.status === 200) {
                vulnerabilities.push({
                  title: 'Firebase Database Write Access Enabled',
                  severity: 'critical',
                  description: 'Firebase database allows unauthenticated write access',
                  endpoint: testBaseUrl,
                  evidence: 'Successfully wrote test data'
                })
                
                // Try to delete our test data
                await fetch(`${testBaseUrl}/test_security_${Date.now()}.json`, {
                  method: 'DELETE',
                  signal: AbortSignal.timeout(5000)
                })
              }
            } catch {
              // Ignore write test errors
            }
            
            break // Don't test more endpoints if we found one vulnerable
          }
          
          // Check for exposed rules
          if (endpoint.includes('rules') && content.includes('rules')) {
            const hasInsecureRules = INSECURE_RULES_PATTERNS.some(pattern => 
              pattern.test(content)
            )
            
            if (hasInsecureRules) {
              vulnerabilities.push({
                title: 'Insecure Firebase Security Rules',
                severity: 'critical',
                description: 'Firebase security rules allow public read/write access',
                endpoint: testUrl,
                evidence: content.substring(0, 500)
              })
            } else {
              vulnerabilities.push({
                title: 'Firebase Security Rules Exposed',
                severity: 'medium',
                description: 'Firebase security rules are publicly accessible',
                endpoint: testUrl
              })
            }
          }
        }
      } catch {
        // Ignore errors
      }
    }
  }
  
  return vulnerabilities
}

async function checkStorageSecurity(baseUrl: string, projectId?: string): Promise<any[]> {
  const vulnerabilities = []
  
  if (!projectId) return vulnerabilities
  
  const storageUrls = [
    `https://firebasestorage.googleapis.com/v0/b/${projectId}.appspot.com/o`,
    `https://storage.googleapis.com/${projectId}.appspot.com`
  ]
  
  for (const storageUrl of storageUrls) {
    try {
      const response = await fetch(storageUrl, {
        signal: AbortSignal.timeout(5000)
      })
      
      if (response.status === 200) {
        const content = await response.text()
        
        if (content.includes('items') || content.includes('prefixes')) {
          vulnerabilities.push({
            title: 'Firebase Storage Bucket Listing Enabled',
            severity: 'high',
            description: 'Firebase storage bucket allows listing of files',
            endpoint: storageUrl,
            evidence: content.substring(0, 200)
          })
        }
      }
    } catch {
      // Ignore errors
    }
  }
  
  // Test for anonymous upload
  if (projectId) {
    try {
      const uploadUrl = `https://firebasestorage.googleapis.com/v0/b/${projectId}.appspot.com/o/test_security_${Date.now()}.txt`
      const response = await fetch(uploadUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'text/plain' },
        body: 'security test',
        signal: AbortSignal.timeout(5000)
      })
      
      if (response.status === 200 || response.status === 201) {
        vulnerabilities.push({
          title: 'Firebase Storage Allows Anonymous Upload',
          severity: 'critical',
          description: 'Firebase storage allows unauthenticated file uploads',
          endpoint: uploadUrl
        })
      }
    } catch {
      // Ignore errors
    }
  }
  
  return vulnerabilities
}

async function checkFirestoreSecurity(projectId?: string): Promise<any[]> {
  const vulnerabilities = []
  
  if (!projectId) return vulnerabilities
  
  // Firestore REST API endpoints
  const firestoreUrl = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents`
  
  try {
    const response = await fetch(firestoreUrl, {
      signal: AbortSignal.timeout(5000)
    })
    
    if (response.status === 200) {
      vulnerabilities.push({
        title: 'Firestore Database Publicly Accessible',
        severity: 'critical',
        description: 'Firestore database allows unauthenticated read access',
        endpoint: firestoreUrl
      })
    }
  } catch {
    // Ignore errors
  }
  
  return vulnerabilities
}

async function checkAuthConfiguration(targetUrl: string): Promise<any[]> {
  const vulnerabilities = []
  
  // Check for exposed auth configuration
  const authConfigUrls = [
    '/__/firebase/init.json',
    '/__/auth/handler',
    '/firebase-auth-config.json'
  ]
  
  for (const configPath of authConfigUrls) {
    try {
      const testUrl = new URL(configPath, targetUrl).toString()
      const response = await fetch(testUrl, {
        signal: AbortSignal.timeout(5000)
      })
      
      if (response.status === 200) {
        const content = await response.text()
        
        // Check for sensitive auth configuration
        if (content.includes('apiKey') || content.includes('authDomain')) {
          vulnerabilities.push({
            title: 'Firebase Auth Configuration Exposed',
            severity: 'medium',
            description: `Firebase authentication configuration exposed at ${configPath}`,
            endpoint: testUrl,
            evidence: content.substring(0, 200)
          })
          
          // Check for weak auth settings
          if (content.includes('"signInOptions"')) {
            if (content.includes('"requireDisplayName":false') || 
                content.includes('"requireEmail":false')) {
              vulnerabilities.push({
                title: 'Weak Firebase Auth Configuration',
                severity: 'medium',
                description: 'Firebase auth configured with weak requirements',
                endpoint: testUrl
              })
            }
          }
        }
      }
    } catch {
      // Ignore errors
    }
  }
  
  return vulnerabilities
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { scanId, targetUrl } = await req.json()
    
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Log scan start
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Starting Firebase security scan',
      details: { targetUrl }
    })

    const allVulnerabilities = []

    // 1. Detect if this is a Firebase project
    const { isFirebase, projectId, urls } = await detectFirebaseProject(targetUrl)
    
    if (isFirebase || projectId) {
      await supabase.from('scan_logs').insert({
        scan_id: scanId,
        level: 'info',
        message: `Firebase project detected`,
        details: { projectId, firebaseUrls: urls }
      })
    }

    // 2. Check Realtime Database security
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Checking Firebase Realtime Database security',
      details: {}
    })
    
    const dbVulns = await checkDatabaseSecurity(targetUrl, projectId)
    allVulnerabilities.push(...dbVulns)

    // 3. Check Cloud Storage security
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Checking Firebase Storage security',
      details: {}
    })
    
    const storageVulns = await checkStorageSecurity(targetUrl, projectId)
    allVulnerabilities.push(...storageVulns)

    // 4. Check Firestore security
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Checking Firestore security',
      details: {}
    })
    
    const firestoreVulns = await checkFirestoreSecurity(projectId)
    allVulnerabilities.push(...firestoreVulns)

    // 5. Check Auth configuration
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Checking Firebase Auth configuration',
      details: {}
    })
    
    const authVulns = await checkAuthConfiguration(targetUrl)
    allVulnerabilities.push(...authVulns)

    // Save vulnerabilities to database
    if (allVulnerabilities.length > 0) {
      const vulnerabilityRecords = allVulnerabilities.map(vuln => ({
        scan_id: scanId,
        title: vuln.title,
        description: vuln.description,
        severity: vuln.severity,
        affected_component: vuln.endpoint,
        owasp_category: 'A01:2021 - Broken Access Control',
        evidence: typeof vuln.evidence === 'object' ? 
          JSON.stringify(vuln.evidence) : vuln.evidence,
        recommendation: vuln.title.includes('Rules') 
          ? 'Implement proper Firebase security rules that require authentication'
          : vuln.title.includes('Storage')
          ? 'Configure Firebase Storage rules to prevent unauthorized access'
          : 'Enable authentication and configure proper access controls'
      }))

      await supabase
        .from('vulnerabilities')
        .insert(vulnerabilityRecords)
    }

    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: `Firebase scan completed. Found ${allVulnerabilities.length} vulnerabilities`,
      details: { 
        vulnerabilityCount: allVulnerabilities.length,
        isFirebaseProject: isFirebase,
        projectId 
      }
    })

    return new Response(
      JSON.stringify({ 
        success: true,
        vulnerabilities: allVulnerabilities.length,
        isFirebaseProject: isFirebase,
        projectId
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})