import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { scanId, scanType, targetUrl, config } = await req.json()

    // Create Supabase client with service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Update scan status to running
    const { error: updateError } = await supabase
      .from('scans')
      .update({ 
        status: 'running',
        started_at: new Date().toISOString(),
        progress: 5
      })
      .eq('id', scanId)

    if (updateError) {
      console.error('Error updating scan status:', updateError)
      throw new Error('Failed to update scan status')
    }

    // Log scan start
    await supabase
      .from('scan_logs')
      .insert({
        scan_id: scanId,
        level: 'info',
        message: `Starting ${scanType} scan for ${targetUrl}`,
        details: { scanType, targetUrl }
      })

    // Map scan types to their respective edge functions
    const scannerMap: Record<string, string> = {
      'api_endpoints': 'scan-api-endpoints',
      'database_security': 'scan-database',
      'secrets': 'scan-secrets',
      'firebase': 'scan-firebase',
      'webhooks': 'scan-webhooks',
      'comprehensive': 'scan-comprehensive'
    }

    const scannerFunction = scannerMap[scanType]
    
    if (!scannerFunction) {
      throw new Error(`Unknown scan type: ${scanType}`)
    }

    // Initialize scanner results outside the if block
    let scannerResults: Record<string, any> = {}
    
    // For comprehensive scans, run all scanners
    if (scanType === 'comprehensive') {
      const scanners = ['api_endpoints', 'database_security', 'secrets', 'firebase', 'webhooks']
      const totalScanners = scanners.length
      let completedScanners = 0
      const results = await Promise.allSettled(
        scanners.map(async (scanner) => {
          try {
            const { data, error } = await supabase.functions.invoke(scannerMap[scanner], {
              body: { scanId, targetUrl, config }
            })

            completedScanners++
            const progress = Math.floor((completedScanners / totalScanners) * 90) + 5

            // Update progress
            await supabase
              .from('scans')
              .update({ progress })
              .eq('id', scanId)

            if (error) {
              console.error(`Error in ${scanner} scanner:`, error)
              scannerResults[scanner] = { success: false, error: error.message }
              throw error
            }

            scannerResults[scanner] = { success: true, ...data }
            return data
          } catch (err) {
            scannerResults[scanner] = { success: false, error: err.message }
            throw err
          }
        })
      )

      // Check if any scanner failed
      const hasErrors = results.some(result => result.status === 'rejected')
      
      // Get aggregated vulnerability counts
      const { data: vulnCounts } = await supabase
        .from('vulnerabilities')
        .select('severity')
        .eq('scan_id', scanId)

      const severityCounts = {
        vulnerabilities_count: vulnCounts?.length || 0,
        critical_count: vulnCounts?.filter(v => v.severity === 'critical').length || 0,
        high_count: vulnCounts?.filter(v => v.severity === 'high').length || 0,
        medium_count: vulnCounts?.filter(v => v.severity === 'medium').length || 0,
        low_count: vulnCounts?.filter(v => v.severity === 'low').length || 0,
        info_count: vulnCounts?.filter(v => v.severity === 'info').length || 0
      }

      // Update scan completion with counts
      await supabase
        .from('scans')
        .update({ 
          status: hasErrors ? 'completed' : 'completed',
          completed_at: new Date().toISOString(),
          progress: 100,
          ...severityCounts
        })
        .eq('id', scanId)

      // Log completion
      await supabase
        .from('scan_logs')
        .insert({
          scan_id: scanId,
          level: hasErrors ? 'warning' : 'info',
          message: `Comprehensive scan completed ${hasErrors ? 'with warnings' : 'successfully'}`,
          details: { 
            results: results.map((r, i) => ({
              scanner: scanners[i],
              status: r.status
            }))
          }
        })

    } else {
      // Run single scanner
      const { data, error } = await supabase.functions.invoke(scannerFunction, {
        body: { scanId, targetUrl, config }
      })

      if (error) {
        console.error(`Error in ${scanType} scanner:`, error)
        
        // Update scan status to failed
        await supabase
          .from('scans')
          .update({ 
            status: 'failed',
            completed_at: new Date().toISOString()
          })
          .eq('id', scanId)

        // Log error
        await supabase
          .from('scan_logs')
          .insert({
            scan_id: scanId,
            level: 'error',
            message: `Scan failed: ${error.message}`,
            details: { error: error.message }
          })

        throw error
      }

      // Get vulnerability counts for single scanner
      const { data: vulnCounts } = await supabase
        .from('vulnerabilities')
        .select('severity')
        .eq('scan_id', scanId)

      const severityCounts = {
        vulnerabilities_count: vulnCounts?.length || 0,
        critical_count: vulnCounts?.filter(v => v.severity === 'critical').length || 0,
        high_count: vulnCounts?.filter(v => v.severity === 'high').length || 0,
        medium_count: vulnCounts?.filter(v => v.severity === 'medium').length || 0,
        low_count: vulnCounts?.filter(v => v.severity === 'low').length || 0,
        info_count: vulnCounts?.filter(v => v.severity === 'info').length || 0
      }

      // Update scan completion
      await supabase
        .from('scans')
        .update({ 
          status: 'completed',
          completed_at: new Date().toISOString(),
          progress: 100,
          ...severityCounts
        })
        .eq('id', scanId)

      // Log completion
      await supabase
        .from('scan_logs')
        .insert({
          scan_id: scanId,
          level: 'info',
          message: `${scanType} scan completed successfully`,
          details: { result: data }
        })
    }

    // Send scan completion notification
    const { data: scan } = await supabase
      .from('scans')
      .select('user_id, scan_name, vulnerabilities_count, critical_count, high_count')
      .eq('id', scanId)
      .single()

    if (scan) {
      const severity = scan.critical_count > 0 ? 'critical' : 
                      scan.high_count > 0 ? 'high' : 
                      scan.vulnerabilities_count > 0 ? 'medium' : 'low'

      await supabase
        .from('notifications')
        .insert({
          user_id: scan.user_id,
          title: 'Scan Completed',
          message: `Your scan "${scan.scan_name}" has completed with ${scan.vulnerabilities_count} vulnerabilities found.`,
          type: 'scan_complete',
          scan_id: scanId,
          data: {
            vulnerabilities_count: scan.vulnerabilities_count,
            severity
          }
        })
    }

    // Return appropriate response based on scan type
    const responseData: any = { 
      success: true, 
      message: 'Scan completed successfully',
      scanId 
    }
    
    // For comprehensive scans, include detailed results
    if (scanType === 'comprehensive' && scannerResults) {
      responseData.results = scannerResults
    }
    
    return new Response(
      JSON.stringify(responseData),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Orchestrator error:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message || 'Scan orchestration failed'
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})
