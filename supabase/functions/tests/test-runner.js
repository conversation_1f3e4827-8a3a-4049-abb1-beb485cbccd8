#!/usr/bin/env node

const SUPABASE_URL = 'http://127.0.0.1:54321'
const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'
const SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'

// Test targets - using real websites known to have certain characteristics
const TEST_TARGETS = {
  database: [
    'http://testphp.vulnweb.com', // Known vulnerable test site
    'https://example.com', // Safe baseline
  ],
  secrets: [
    'https://github.com', // Should not find secrets on main page
    'http://testphp.vulnweb.com', // May have exposed configs
  ],
  firebase: [
    'https://fir-demo-project.firebaseapp.com', // Demo Firebase app
    'https://example.com', // Non-Firebase site
  ],
  webhooks: [
    'https://webhook.site', // Webhook testing service
    'https://example.com', // Regular site
  ],
  api: [
    'https://jsonplaceholder.typicode.com', // Public API
    'http://testphp.vulnweb.com/rest/', // Vulnerable API
  ]
}

// Color codes for output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

async function createTestUser() {
  log('\nCreating test user...', 'cyan')
  
  // Create a test user using Supabase auth
  const response = await fetch(`${SUPABASE_URL}/auth/v1/signup`, {
    method: 'POST',
    headers: {
      'apikey': ANON_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      email: `test-${Date.now()}@example.com`,
      password: 'testpassword123',
      data: {
        username: `test-runner-${Date.now()}`
      }
    })
  })
  
  if (!response.ok) {
    log(`Failed to create test user: ${response.status} ${response.statusText}`, 'red')
    const error = await response.text()
    log(error, 'red')
    process.exit(1)
  }
  
  const data = await response.json()
  log(`Test user created: ${data.user.id}`, 'green')
  return data.user.id
}

async function createTestScan(userId) {
  // First, create a test scan record in the database
  // Generate a valid UUID v4
  const scanId = crypto.randomUUID()
  
  log(`Creating test scan with ID: ${scanId}`, 'cyan')
  
  // We need to disable RLS for testing since we don't have a real user
  // The Edge Functions use service role key which bypasses RLS
  const response = await fetch(`${SUPABASE_URL}/rest/v1/scans`, {
    method: 'POST',
    headers: {
      'apikey': SERVICE_KEY,
      'Authorization': `Bearer ${SERVICE_KEY}`,
      'Content-Type': 'application/json',
      'Prefer': 'return=minimal'
    },
    body: JSON.stringify({
      id: scanId,
      scan_name: 'Edge Function Test Scan',
      scan_description: 'Testing Edge Functions with real endpoints',
      target_url: 'https://example.com',
      scan_type: 'comprehensive',
      status: 'running',
      progress: 0,
      user_id: userId
    })
  })
  
  if (!response.ok) {
    log(`Failed to create test scan: ${response.status} ${response.statusText}`, 'red')
    const error = await response.text()
    log(error, 'red')
    process.exit(1)
  }
  
  return scanId
}

async function testEdgeFunction(functionName, scanId, targetUrl) {
  log(`\n  Testing ${functionName} with ${targetUrl}`, 'blue')
  
  try {
    const startTime = Date.now()
    const response = await fetch(`${SUPABASE_URL}/functions/v1/${functionName}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SERVICE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        scanId,
        targetUrl
      })
    })
    
    const elapsed = Date.now() - startTime
    
    if (!response.ok) {
      log(`    ❌ Failed: ${response.status} ${response.statusText}`, 'red')
      const error = await response.text()
      log(`    Error: ${error}`, 'red')
      return { success: false, error, elapsed }
    }
    
    const result = await response.json()
    log(`    ✅ Success in ${elapsed}ms`, 'green')
    
    if (result.vulnerabilities > 0) {
      log(`    🔍 Found ${result.vulnerabilities} vulnerabilities`, 'yellow')
    } else {
      log(`    ✨ No vulnerabilities found`, 'green')
    }
    
    // Additional info based on function type
    if (result.isFirebaseProject !== undefined) {
      log(`    📱 Firebase Project: ${result.isFirebaseProject}`, 'cyan')
    }
    if (result.webhookEndpoints !== undefined) {
      log(`    🔗 Webhook Endpoints: ${result.webhookEndpoints}`, 'cyan')
    }
    if (result.databaseProvider) {
      log(`    💾 Database Provider: ${result.databaseProvider}`, 'cyan')
    }
    if (result.categories) {
      log(`    📋 Categories: ${result.categories.join(', ')}`, 'cyan')
    }
    
    return { success: true, result, elapsed }
    
  } catch (error) {
    log(`    ❌ Error: ${error.message}`, 'red')
    return { success: false, error: error.message }
  }
}

async function checkVulnerabilities(scanId) {
  log(`\n📊 Checking vulnerabilities for scan ${scanId}...`, 'magenta')
  
  const response = await fetch(`${SUPABASE_URL}/rest/v1/vulnerabilities?scan_id=eq.${scanId}`, {
    headers: {
      'apikey': SERVICE_KEY,
      'Authorization': `Bearer ${SERVICE_KEY}`,
      'Content-Type': 'application/json'
    }
  })
  
  if (!response.ok) {
    log(`Failed to fetch vulnerabilities: ${response.status}`, 'red')
    return
  }
  
  const vulnerabilities = await response.json()
  
  if (vulnerabilities.length === 0) {
    log('No vulnerabilities found in this test run', 'green')
    return
  }
  
  log(`\nFound ${vulnerabilities.length} vulnerabilities:`, 'yellow')
  
  // Group by severity
  const bySeverity = vulnerabilities.reduce((acc, vuln) => {
    acc[vuln.severity] = (acc[vuln.severity] || 0) + 1
    return acc
  }, {})
  
  Object.entries(bySeverity).forEach(([severity, count]) => {
    const color = severity === 'critical' ? 'red' : 
                  severity === 'high' ? 'yellow' : 
                  severity === 'medium' ? 'blue' : 'cyan'
    log(`  ${severity.toUpperCase()}: ${count}`, color)
  })
  
  // Show first few vulnerabilities
  log('\nSample vulnerabilities:', 'yellow')
  vulnerabilities.slice(0, 5).forEach(vuln => {
    log(`  - ${vuln.title} (${vuln.severity})`, 'yellow')
    log(`    ${vuln.description}`, 'reset')
    if (vuln.affected_component) {
      log(`    Component: ${vuln.affected_component}`, 'cyan')
    }
  })
}

async function testOrchestrator(userId) {
  log('\n🎯 Testing Scan Orchestrator (Comprehensive Scan)', 'bright')
  
  const targetUrl = 'http://testphp.vulnweb.com'
  log(`  Target: ${targetUrl}`, 'blue')
  
  // Create a new scan record for the orchestrator test
  const orchestratorScanId = crypto.randomUUID()
  log(`  Creating orchestrator scan: ${orchestratorScanId}`, 'cyan')
  
  const scanResponse = await fetch(`${SUPABASE_URL}/rest/v1/scans`, {
    method: 'POST',
    headers: {
      'apikey': SERVICE_KEY,
      'Authorization': `Bearer ${SERVICE_KEY}`,
      'Content-Type': 'application/json',
      'Prefer': 'return=minimal'
    },
    body: JSON.stringify({
      id: orchestratorScanId,
      scan_name: 'Orchestrator Test - Comprehensive Scan',
      scan_description: 'Testing scan orchestrator with comprehensive scan',
      target_url: targetUrl,
      scan_type: 'comprehensive',
      status: 'pending',
      progress: 0,
      user_id: userId
    })
  })
  
  if (!scanResponse.ok) {
    log(`  ❌ Failed to create orchestrator scan: ${scanResponse.status}`, 'red')
    const error = await scanResponse.text()
    log(`  Error: ${error}`, 'red')
    return
  }
  
  try {
    const startTime = Date.now()
    const response = await fetch(`${SUPABASE_URL}/functions/v1/scan-orchestrator`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SERVICE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        scanId: orchestratorScanId,
        targetUrl,
        scanType: 'comprehensive'
      })
    })
    
    const elapsed = Date.now() - startTime
    
    if (!response.ok) {
      log(`  ❌ Orchestrator failed: ${response.status}`, 'red')
      const error = await response.text()
      log(`  Error: ${error}`, 'red')
      return
    }
    
    const result = await response.json()
    log(`  ✅ Orchestrator completed in ${elapsed}ms`, 'green')
    log(`  📊 Results:`, 'cyan')
    
    if (result.success) {
      log(`    Message: ${result.message}`, 'green')
      log(`    Scan ID: ${result.scanId}`, 'cyan')
    }
    
    // Check vulnerabilities for this scan
    await checkVulnerabilities(orchestratorScanId)
    
  } catch (error) {
    log(`  ❌ Orchestrator error: ${error.message}`, 'red')
  }
}

async function main() {
  log('\n🚀 Edge Functions Test Runner', 'bright')
  log('================================\n', 'bright')
  
  // Create test user
  const userId = await createTestUser()
  
  // Create a test scan
  const scanId = await createTestScan(userId)
  
  // Test individual scanners
  const scanners = [
    { name: 'scan-database', key: 'database' },
    { name: 'scan-secrets', key: 'secrets' },
    { name: 'scan-firebase', key: 'firebase' },
    { name: 'scan-webhooks', key: 'webhooks' },
    { name: 'scan-api-endpoints', key: 'api' }
  ]
  
  const results = {
    passed: 0,
    failed: 0,
    totalTime: 0
  }
  
  for (const scanner of scanners) {
    log(`\n🔍 Testing ${scanner.name}`, 'bright')
    log('----------------------------', 'reset')
    
    for (const target of TEST_TARGETS[scanner.key]) {
      const result = await testEdgeFunction(scanner.name, scanId, target)
      
      if (result.success) {
        results.passed++
      } else {
        results.failed++
      }
      
      if (result.elapsed) {
        results.totalTime += result.elapsed
      }
    }
  }
  
  // Test the orchestrator
  await testOrchestrator(userId)
  
  // Check vulnerabilities found
  await checkVulnerabilities(scanId)
  
  // Summary
  log('\n📈 Test Summary', 'bright')
  log('================', 'bright')
  const totalTests = results.passed + results.failed + 1 // +1 for orchestrator
  log(`Total Tests: ${totalTests}`, 'cyan')
  log(`Passed: ${results.passed + 1}`, 'green') // Assuming orchestrator passed if we got here
  log(`Failed: ${results.failed}`, results.failed > 0 ? 'red' : 'green')
  log(`Total Time: ${results.totalTime}ms`, 'cyan')
  log(`Average Time: ${Math.round(results.totalTime / totalTests)}ms`, 'cyan')
  
  if (results.failed > 0) {
    log('\n❌ Some tests failed!', 'red')
    process.exit(1)
  } else {
    log('\n✅ All tests passed!', 'green')
    process.exit(0)
  }
}

// Run the tests
main().catch(error => {
  log(`\n💥 Fatal error: ${error.message}`, 'red')
  console.error(error)
  process.exit(1)
})