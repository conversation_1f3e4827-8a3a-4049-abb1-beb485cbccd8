#!/usr/bin/env node

const SUPABASE_URL = 'http://127.0.0.1:54321'
const SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'

// Test a single scanner quickly
const TEST_CASES = {
  'scan-database': {
    url: 'http://testphp.vulnweb.com',
    expected: 'Should find SQL injection vulnerabilities'
  },
  'scan-secrets': {
    url: 'https://github.com',
    expected: 'Should not find secrets on GitHub main page'
  },
  'scan-firebase': {
    url: 'https://fir-demo-project.firebaseapp.com',
    expected: 'Should detect Firebase project'
  },
  'scan-webhooks': {
    url: 'https://example.com',
    expected: 'Should check for webhook endpoints'
  },
  'scan-api-endpoints': {
    url: 'https://jsonplaceholder.typicode.com',
    expected: 'Should discover API endpoints'
  }
}

async function testScanner(scannerName) {
  const testCase = TEST_CASES[scannerName]
  if (!testCase) {
    console.error(`Unknown scanner: ${scannerName}`)
    console.log('Available scanners:', Object.keys(TEST_CASES).join(', '))
    return
  }

  console.log(`\n🔍 Testing ${scannerName}`)
  console.log(`URL: ${testCase.url}`)
  console.log(`Expected: ${testCase.expected}`)
  
  const scanId = crypto.randomUUID()
  
  try {
    console.time('Request duration')
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/${scannerName}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SERVICE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        scanId,
        targetUrl: testCase.url
      }),
      signal: AbortSignal.timeout(30000) // 30 second timeout
    })
    
    console.timeEnd('Request duration')
    
    if (!response.ok) {
      console.error(`❌ Failed: ${response.status} ${response.statusText}`)
      const error = await response.text()
      console.error('Error:', error)
      return
    }
    
    const result = await response.json()
    console.log('✅ Success!')
    console.log('Result:', JSON.stringify(result, null, 2))
    
  } catch (error) {
    console.error('❌ Error:', error.message)
    if (error.name === 'AbortError') {
      console.error('Request timed out after 30 seconds')
    }
  }
}

// Get scanner name from command line
const scannerName = process.argv[2]

if (!scannerName) {
  console.log('Usage: node quick-test.js <scanner-name>')
  console.log('Available scanners:', Object.keys(TEST_CASES).join(', '))
  console.log('\nExample: node quick-test.js scan-database')
} else if (scannerName === 'all') {
  // Test all scanners
  (async () => {
    for (const scanner of Object.keys(TEST_CASES)) {
      await testScanner(scanner)
      console.log('\n' + '='.repeat(50) + '\n')
    }
  })()
} else {
  testScanner(scannerName)
}