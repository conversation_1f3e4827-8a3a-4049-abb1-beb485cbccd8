import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Secret patterns with their categories
const SECRET_PATTERNS = [
  // API Keys
  {
    name: 'AWS Access Key ID',
    pattern: /AKIA[0-9A-Z]{16}/g,
    severity: 'critical',
    category: 'AWS'
  },
  {
    name: 'AWS Secret Key',
    pattern: /aws[_\-\s]*secret[_\-\s]*(?:access[_\-\s]*)?key[_\-\s]*[:=][_\-\s]*["']?[A-Za-z0-9\/+=]{40}["']?/gi,
    severity: 'critical',
    category: 'AWS'
  },
  {
    name: 'Google API Key',
    pattern: /AIza[0-9A-Za-z\-_]{35}/g,
    severity: 'high',
    category: 'Google'
  },
  {
    name: 'Google OAuth',
    pattern: /[0-9]+-[0-9A-Za-z_]{32}\.apps\.googleusercontent\.com/g,
    severity: 'high',
    category: 'Google'
  },
  {
    name: 'GitHub Token',
    pattern: /g[ho]p_[0-9a-zA-Z]{36}/g,
    severity: 'critical',
    category: 'GitHub'
  },
  {
    name: 'GitHub Personal Access Token',
    pattern: /ghp_[0-9a-zA-Z]{36}/g,
    severity: 'critical',
    category: 'GitHub'
  },
  {
    name: 'Slack Token',
    pattern: /xox[baprs]-[0-9]{10,48}/g,
    severity: 'high',
    category: 'Slack'
  },
  {
    name: 'Stripe API Key',
    pattern: /sk_live_[0-9a-zA-Z]{24}/g,
    severity: 'critical',
    category: 'Stripe'
  },
  {
    name: 'Stripe Test Key',
    pattern: /sk_test_[0-9a-zA-Z]{24}/g,
    severity: 'medium',
    category: 'Stripe'
  },
  {
    name: 'Mailgun API Key',
    pattern: /key-[0-9a-zA-Z]{32}/g,
    severity: 'high',
    category: 'Mailgun'
  },
  {
    name: 'Twilio API Key',
    pattern: /SK[0-9a-fA-F]{32}/g,
    severity: 'high',
    category: 'Twilio'
  },
  
  // Database Connection Strings
  {
    name: 'PostgreSQL Connection String',
    pattern: /postgres(?:ql)?:\/\/[^\s<>"{}|\\^`\[\]]+/g,
    severity: 'critical',
    category: 'Database'
  },
  {
    name: 'MySQL Connection String',
    pattern: /mysql:\/\/[^\s<>"{}|\\^`\[\]]+/g,
    severity: 'critical',
    category: 'Database'
  },
  {
    name: 'MongoDB Connection String',
    pattern: /mongodb(?:\+srv)?:\/\/[^\s<>"{}|\\^`\[\]]+/g,
    severity: 'critical',
    category: 'Database'
  },
  
  // JWT Tokens
  {
    name: 'JWT Token',
    pattern: /eyJ[A-Za-z0-9-_=]+\.eyJ[A-Za-z0-9-_=]+\.[A-Za-z0-9-_.+\/=]*/g,
    severity: 'high',
    category: 'JWT'
  },
  
  // Private Keys
  {
    name: 'RSA Private Key',
    pattern: /-----BEGIN RSA PRIVATE KEY-----/g,
    severity: 'critical',
    category: 'Private Key'
  },
  {
    name: 'SSH Private Key',
    pattern: /-----BEGIN OPENSSH PRIVATE KEY-----/g,
    severity: 'critical',
    category: 'Private Key'
  },
  {
    name: 'EC Private Key',
    pattern: /-----BEGIN EC PRIVATE KEY-----/g,
    severity: 'critical',
    category: 'Private Key'
  },
  
  // Generic Patterns
  {
    name: 'Generic API Key',
    pattern: /api[_\-\s]*key[_\-\s]*[:=][_\-\s]*["']?[a-zA-Z0-9\-_]{16,}["']?/gi,
    severity: 'medium',
    category: 'Generic'
  },
  {
    name: 'Generic Secret',
    pattern: /secret[_\-\s]*[:=][_\-\s]*["']?[a-zA-Z0-9\-_]{8,}["']?/gi,
    severity: 'medium',
    category: 'Generic'
  },
  {
    name: 'Password in Config',
    pattern: /password[_\-\s]*[:=][_\-\s]*["']?[^\s"']{4,}["']?/gi,
    severity: 'high',
    category: 'Password'
  },
  
  // Firebase
  {
    name: 'Firebase URL',
    pattern: /https:\/\/[a-zA-Z0-9\-]+\.firebaseio\.com/g,
    severity: 'medium',
    category: 'Firebase'
  },
  {
    name: 'Firebase API Key',
    pattern: /AIza[0-9A-Za-z\-_]{35}/g,
    severity: 'high',
    category: 'Firebase'
  },
  
  // OAuth & Social Media
  {
    name: 'Facebook Access Token',
    pattern: /EAACEdEose0cBA[0-9A-Za-z]+/g,
    severity: 'high',
    category: 'Facebook'
  },
  {
    name: 'Twitter Bearer Token',
    pattern: /AAAAAAAAAAAAAAAAAAAAA[0-9A-Za-z%]+/g,
    severity: 'high',
    category: 'Twitter'
  }
]

// Common paths to check for secrets
const COMMON_PATHS = [
  '/.env',
  '/.env.local',
  '/.env.production',
  '/.env.development',
  '/config.json',
  '/config.js',
  '/config.yaml',
  '/config.yml',
  '/settings.json',
  '/settings.py',
  '/app.config.js',
  '/package.json',
  '/.git/config',
  '/.htaccess',
  '/web.config',
  '/wp-config.php',
  '/firebase.json',
  '/.firebase/hosting.*.cache',
  '/google-services.json',
  '/GoogleService-Info.plist'
]

// JavaScript/HTML paths that might contain inline secrets
const JS_HTML_PATHS = [
  '/main.js',
  '/app.js',
  '/bundle.js',
  '/index.js',
  '/config.js',
  '/settings.js',
  '/constants.js',
  '/index.html',
  '/app.html',
  '/.well-known/apple-app-site-association',
  '/.well-known/assetlinks.json'
]

async function scanForSecrets(url: string, content: string): Promise<any[]> {
  const findings = []
  
  for (const secretPattern of SECRET_PATTERNS) {
    const matches = content.matchAll(secretPattern.pattern)
    
    for (const match of matches) {
      const secret = match[0]
      // Mask the secret value for security
      const maskedSecret = secret.length > 8 
        ? secret.substring(0, 4) + '****' + secret.substring(secret.length - 4)
        : '****'
      
      // Get context around the match
      const startIndex = Math.max(0, match.index! - 50)
      const endIndex = Math.min(content.length, match.index! + secret.length + 50)
      const context = content.substring(startIndex, endIndex)
      
      findings.push({
        title: `${secretPattern.name} Exposed`,
        severity: secretPattern.severity,
        category: secretPattern.category,
        description: `Found exposed ${secretPattern.name} in ${url}`,
        secret: maskedSecret,
        context: context.replace(secret, maskedSecret),
        url
      })
    }
  }
  
  return findings
}

async function checkCommonPaths(targetUrl: string): Promise<any[]> {
  const allFindings = []
  
  // Check common configuration files
  for (const path of COMMON_PATHS) {
    try {
      const testUrl = new URL(path, targetUrl).toString()
      const response = await fetch(testUrl, {
        signal: AbortSignal.timeout(5000)
      })
      
      if (response.status === 200) {
        const content = await response.text()
        
        // Skip if content is HTML (likely a 404 page)
        if (!content.includes('<!DOCTYPE') && !content.includes('<html')) {
          const findings = await scanForSecrets(testUrl, content)
          allFindings.push(...findings)
          
          // Also record that the config file is exposed
          allFindings.push({
            title: 'Configuration File Exposed',
            severity: 'high',
            category: 'Information Disclosure',
            description: `Configuration file accessible at ${path}`,
            url: testUrl
          })
        }
      }
    } catch {
      // Ignore errors
    }
  }
  
  // Check JavaScript and HTML files
  for (const path of JS_HTML_PATHS) {
    try {
      const testUrl = new URL(path, targetUrl).toString()
      const response = await fetch(testUrl, {
        signal: AbortSignal.timeout(5000)
      })
      
      if (response.status === 200) {
        const content = await response.text()
        const findings = await scanForSecrets(testUrl, content)
        allFindings.push(...findings)
      }
    } catch {
      // Ignore errors
    }
  }
  
  return allFindings
}

async function checkGitExposure(targetUrl: string): Promise<any[]> {
  const findings = []
  const gitPaths = [
    '/.git/HEAD',
    '/.git/config',
    '/.git/index',
    '/.git/logs/HEAD',
    '/.git/COMMIT_EDITMSG'
  ]
  
  for (const path of gitPaths) {
    try {
      const testUrl = new URL(path, targetUrl).toString()
      const response = await fetch(testUrl, {
        signal: AbortSignal.timeout(5000)
      })
      
      if (response.status === 200) {
        findings.push({
          title: 'Git Repository Exposed',
          severity: 'critical',
          category: 'Information Disclosure',
          description: `Git repository files are publicly accessible at ${path}`,
          url: testUrl,
          recommendation: 'Remove .git directory from web root or block access via web server configuration'
        })
        break // One finding is enough
      }
    } catch {
      // Ignore errors
    }
  }
  
  return findings
}

async function checkSourceMaps(targetUrl: string): Promise<any[]> {
  const findings = []
  const mapPaths = [
    '/main.js.map',
    '/app.js.map',
    '/bundle.js.map',
    '/vendor.js.map'
  ]
  
  for (const path of mapPaths) {
    try {
      const testUrl = new URL(path, targetUrl).toString()
      const response = await fetch(testUrl, {
        signal: AbortSignal.timeout(5000)
      })
      
      if (response.status === 200) {
        const content = await response.text()
        if (content.includes('mappings') && content.includes('sources')) {
          findings.push({
            title: 'Source Map File Exposed',
            severity: 'medium',
            category: 'Information Disclosure',
            description: `JavaScript source map accessible at ${path}, exposing source code`,
            url: testUrl,
            recommendation: 'Remove source maps from production or restrict access'
          })
        }
      }
    } catch {
      // Ignore errors
    }
  }
  
  return findings
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { scanId, targetUrl } = await req.json()
    
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Log scan start
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Starting secrets and sensitive data scan',
      details: { targetUrl }
    })

    const allFindings = []

    // 1. Check common configuration paths
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Checking common configuration paths',
      details: { paths: COMMON_PATHS.length }
    })
    
    const configFindings = await checkCommonPaths(targetUrl)
    allFindings.push(...configFindings)

    // 2. Check for exposed Git repository
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Checking for exposed Git repository',
      details: {}
    })
    
    const gitFindings = await checkGitExposure(targetUrl)
    allFindings.push(...gitFindings)

    // 3. Check for source maps
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Checking for exposed source maps',
      details: {}
    })
    
    const mapFindings = await checkSourceMaps(targetUrl)
    allFindings.push(...mapFindings)

    // 4. Scan main page for inline secrets
    try {
      const response = await fetch(targetUrl, {
        signal: AbortSignal.timeout(10000)
      })
      
      if (response.status === 200) {
        const content = await response.text()
        const pageFindings = await scanForSecrets(targetUrl, content)
        allFindings.push(...pageFindings)
      }
    } catch {
      // Ignore errors
    }

    // Save vulnerabilities to database
    if (allFindings.length > 0) {
      const vulnerabilityRecords = allFindings.map(finding => ({
        scan_id: scanId,
        title: finding.title,
        description: finding.description,
        severity: finding.severity,
        affected_component: finding.url,
        owasp_category: finding.category === 'Information Disclosure' 
          ? 'A01:2021 - Broken Access Control' 
          : 'A02:2021 - Cryptographic Failures',
        evidence: {
          secret: finding.secret || null,
          context: finding.context || null,
          category: finding.category
        },
        recommendation: finding.recommendation || 
          'Remove sensitive data from publicly accessible locations. Use environment variables for secrets.'
      }))

      await supabase
        .from('vulnerabilities')
        .insert(vulnerabilityRecords)
    }

    // Count vulnerabilities by severity
    const counts = {
      critical: allFindings.filter(v => v.severity === 'critical').length,
      high: allFindings.filter(v => v.severity === 'high').length,
      medium: allFindings.filter(v => v.severity === 'medium').length,
      low: allFindings.filter(v => v.severity === 'low').length,
    }

    // Update scan with vulnerability counts
    await supabase
      .from('scans')
      .update({
        vulnerabilities_count: allFindings.length,
        critical_count: counts.critical,
        high_count: counts.high,
        medium_count: counts.medium,
        low_count: counts.low,
        progress: 90
      })
      .eq('id', scanId)

    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: `Secrets scan completed. Found ${allFindings.length} issues`,
      details: { 
        findingsCount: allFindings.length,
        categories: [...new Set(allFindings.map(f => f.category))]
      }
    })

    return new Response(
      JSON.stringify({ 
        success: true,
        findings: allFindings.length,
        categories: [...new Set(allFindings.map(f => f.category))]
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})