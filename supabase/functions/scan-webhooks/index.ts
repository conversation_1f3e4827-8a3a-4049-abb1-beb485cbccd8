import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Common webhook paths and patterns
const WEBHOOK_PATTERNS = {
  // Common webhook endpoints
  endpoints: [
    '/webhook', '/webhooks', '/hook', '/hooks',
    '/api/webhook', '/api/webhooks', '/api/hook', '/api/hooks',
    '/callback', '/callbacks', '/api/callback', '/api/callbacks',
    '/notify', '/notification', '/notifications',
    '/stripe/webhook', '/stripe/webhooks',
    '/paypal/webhook', '/paypal/ipn',
    '/github/webhook', '/github/hooks',
    '/gitlab/webhook', '/gitlab/hooks',
    '/bitbucket/webhook', '/bitbucket/hooks',
    '/slack/webhook', '/slack/events', '/slack/slash',
    '/discord/webhook', '/discord/webhooks',
    '/telegram/webhook', '/telegram/bot',
    '/twilio/webhook', '/twilio/sms', '/twilio/voice',
    '/sendgrid/webhook', '/sendgrid/events',
    '/mailgun/webhook', '/mailgun/hooks',
    '/shopify/webhook', '/shopify/webhooks',
    '/woocommerce/webhook',
    '/zapier/webhook', '/zapier/hooks',
    '/ifttt/webhook', '/ifttt/v1/actions',
    '/jenkins/webhook', '/jenkins/github-webhook',
    '/_hooks', '/__hooks', '/.hooks'
  ],
  
  // Service-specific patterns
  services: {
    stripe: {
      paths: ['/stripe/webhook', '/stripe-webhook', '/payments/stripe'],
      headers: ['stripe-signature'],
      methods: ['POST']
    },
    github: {
      paths: ['/github/webhook', '/github-webhook', '/gh/webhook'],
      headers: ['x-github-event', 'x-hub-signature', 'x-hub-signature-256'],
      methods: ['POST']
    },
    gitlab: {
      paths: ['/gitlab/webhook', '/gitlab-webhook', '/gl/webhook'],
      headers: ['x-gitlab-event', 'x-gitlab-token'],
      methods: ['POST']
    },
    slack: {
      paths: ['/slack/webhook', '/slack/events', '/slack/slash', '/slack/interactive'],
      headers: ['x-slack-signature', 'x-slack-request-timestamp'],
      methods: ['POST']
    },
    twilio: {
      paths: ['/twilio/webhook', '/twilio/sms', '/twilio/voice', '/twilio/status'],
      headers: ['x-twilio-signature'],
      methods: ['POST']
    }
  }
}

// Security headers that should be present
const SECURITY_HEADERS = [
  'x-hub-signature',
  'x-hub-signature-256',
  'x-gitlab-token',
  'x-slack-signature',
  'stripe-signature',
  'x-twilio-signature',
  'authorization',
  'x-api-key',
  'x-webhook-signature',
  'x-signature'
]

// Test payloads for different webhook types
const TEST_PAYLOADS = {
  generic: {
    event: 'test',
    timestamp: Date.now(),
    data: { test: true }
  },
  github: {
    action: 'opened',
    pull_request: { id: 1, title: 'Test PR' },
    repository: { name: 'test-repo' }
  },
  stripe: {
    type: 'payment_intent.succeeded',
    data: { object: { id: 'pi_test', amount: 1000 } }
  },
  slack: {
    type: 'url_verification',
    challenge: 'test_challenge_123'
  }
}

async function discoverWebhooks(targetUrl: string): Promise<Map<string, any>> {
  const discovered = new Map<string, any>()
  
  // Test common webhook endpoints
  for (const endpoint of WEBHOOK_PATTERNS.endpoints) {
    try {
      const testUrl = new URL(endpoint, targetUrl).toString()
      
      // Try OPTIONS to check allowed methods
      const optionsResponse = await fetch(testUrl, {
        method: 'OPTIONS',
        signal: AbortSignal.timeout(5000)
      })
      
      if (optionsResponse.status !== 404) {
        const allowedMethods = optionsResponse.headers.get('allow') || 
                              optionsResponse.headers.get('access-control-allow-methods') || ''
        
        discovered.set(endpoint, {
          url: testUrl,
          methods: allowedMethods.split(',').map(m => m.trim()).filter(Boolean),
          corsEnabled: optionsResponse.headers.has('access-control-allow-origin')
        })
      }
      
      // Also try GET/POST
      for (const method of ['GET', 'POST']) {
        const response = await fetch(testUrl, {
          method,
          signal: AbortSignal.timeout(5000)
        })
        
        if (response.status !== 404 && response.status !== 405) {
          const existing = discovered.get(endpoint) || { url: testUrl, methods: [] }
          if (!existing.methods.includes(method)) {
            existing.methods.push(method)
          }
          discovered.set(endpoint, existing)
        }
      }
    } catch {
      // Ignore errors
    }
  }
  
  return discovered
}

async function testWebhookSecurity(endpoint: string, webhookInfo: any): Promise<any[]> {
  const vulnerabilities = []
  
  // Test for missing authentication
  try {
    // Test without any auth headers
    const response = await fetch(webhookInfo.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(TEST_PAYLOADS.generic),
      signal: AbortSignal.timeout(5000)
    })
    
    if (response.status === 200 || response.status === 201 || response.status === 204) {
      vulnerabilities.push({
        title: 'Webhook Endpoint Lacks Authentication',
        severity: 'critical',
        description: `Webhook endpoint ${endpoint} accepts requests without authentication`,
        endpoint: webhookInfo.url,
        evidence: `Accepted POST request with status ${response.status}`
      })
    }
    
    // Test for verbose error messages
    if (response.status >= 400 && response.status < 500) {
      const errorText = await response.text()
      if (errorText.length > 100 && 
          (errorText.includes('stack') || 
           errorText.includes('trace') || 
           errorText.includes('SQL') ||
           errorText.includes('database'))) {
        vulnerabilities.push({
          title: 'Verbose Error Messages in Webhook',
          severity: 'medium',
          description: `Webhook endpoint exposes detailed error information`,
          endpoint: webhookInfo.url,
          evidence: errorText.substring(0, 200)
        })
      }
    }
  } catch {
    // Ignore errors
  }
  
  // Test for SSRF via webhook
  try {
    const ssrfPayloads = [
      { url: 'http://***************/latest/meta-data/' }, // AWS metadata
      { callback_url: 'http://localhost:8080/admin' },
      { webhook_url: 'file:///etc/passwd' }
    ]
    
    for (const payload of ssrfPayloads) {
      const response = await fetch(webhookInfo.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(5000)
      })
      
      if (response.status === 200) {
        vulnerabilities.push({
          title: 'Potential SSRF in Webhook Handler',
          severity: 'high',
          description: `Webhook may be vulnerable to Server-Side Request Forgery`,
          endpoint: webhookInfo.url,
          evidence: `Accepted payload with internal URL reference`
        })
        break
      }
    }
  } catch {
    // Ignore errors
  }
  
  // Test for webhook replay attacks
  const timestamp = Date.now() - (60 * 60 * 1000) // 1 hour ago
  try {
    const replayResponse = await fetch(webhookInfo.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Timestamp': timestamp.toString()
      },
      body: JSON.stringify({
        ...TEST_PAYLOADS.generic,
        timestamp
      }),
      signal: AbortSignal.timeout(5000)
    })
    
    if (replayResponse.status === 200) {
      vulnerabilities.push({
        title: 'Webhook Vulnerable to Replay Attacks',
        severity: 'high',
        description: `Webhook accepts old/replayed requests without timestamp validation`,
        endpoint: webhookInfo.url
      })
    }
  } catch {
    // Ignore errors
  }
  
  // Test for CORS misconfiguration
  if (webhookInfo.corsEnabled) {
    try {
      const corsResponse = await fetch(webhookInfo.url, {
        method: 'POST',
        headers: {
          'Origin': 'https://evil.com',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(TEST_PAYLOADS.generic),
        signal: AbortSignal.timeout(5000)
      })
      
      const allowOrigin = corsResponse.headers.get('access-control-allow-origin')
      if (allowOrigin === '*' || allowOrigin === 'https://evil.com') {
        vulnerabilities.push({
          title: 'Webhook CORS Misconfiguration',
          severity: 'medium',
          description: `Webhook allows requests from any origin`,
          endpoint: webhookInfo.url,
          evidence: `Access-Control-Allow-Origin: ${allowOrigin}`
        })
      }
    } catch {
      // Ignore errors
    }
  }
  
  return vulnerabilities
}

async function testServiceSpecificWebhooks(targetUrl: string): Promise<any[]> {
  const vulnerabilities = []
  
  for (const [service, config] of Object.entries(WEBHOOK_PATTERNS.services)) {
    for (const path of config.paths) {
      try {
        const testUrl = new URL(path, targetUrl).toString()
        
        // Test without required headers
        const response = await fetch(testUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(TEST_PAYLOADS[service] || TEST_PAYLOADS.generic),
          signal: AbortSignal.timeout(5000)
        })
        
        if (response.status === 200 || response.status === 201 || response.status === 204) {
          // Missing signature validation
          vulnerabilities.push({
            title: `${service.charAt(0).toUpperCase() + service.slice(1)} Webhook Missing Signature Validation`,
            severity: 'critical',
            description: `${service} webhook endpoint accepts requests without signature verification`,
            endpoint: testUrl,
            evidence: `Missing required headers: ${config.headers.join(', ')}`
          })
        }
        
        // Test for information disclosure
        if (response.status !== 404) {
          const responseText = await response.text()
          if (responseText.includes('api_key') || 
              responseText.includes('secret') ||
              responseText.includes('token')) {
            vulnerabilities.push({
              title: 'Webhook Response Contains Sensitive Data',
              severity: 'high',
              description: `Webhook endpoint may expose sensitive information in responses`,
              endpoint: testUrl
            })
          }
        }
      } catch {
        // Ignore errors
      }
    }
  }
  
  return vulnerabilities
}

async function checkWebhookDocumentation(targetUrl: string): Promise<any[]> {
  const vulnerabilities = []
  const docPaths = [
    '/webhooks/docs',
    '/webhook-docs',
    '/api/webhooks/documentation',
    '/.well-known/webhooks.json',
    '/webhooks.md',
    '/WEBHOOKS.md'
  ]
  
  for (const path of docPaths) {
    try {
      const testUrl = new URL(path, targetUrl).toString()
      const response = await fetch(testUrl, {
        signal: AbortSignal.timeout(5000)
      })
      
      if (response.status === 200) {
        const content = await response.text()
        
        // Check for exposed webhook URLs or secrets
        if (content.includes('secret') || 
            content.includes('signature') ||
            content.includes('private') ||
            content.includes('token')) {
          vulnerabilities.push({
            title: 'Webhook Documentation Exposes Sensitive Information',
            severity: 'medium',
            description: `Webhook documentation may contain sensitive configuration details`,
            endpoint: testUrl
          })
        }
      }
    } catch {
      // Ignore errors
    }
  }
  
  return vulnerabilities
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { scanId, targetUrl } = await req.json()
    
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Log scan start
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Starting webhook security scan',
      details: { targetUrl }
    })

    const allVulnerabilities = []

    // 1. Discover webhook endpoints
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Discovering webhook endpoints',
      details: {}
    })
    
    const webhooks = await discoverWebhooks(targetUrl)
    
    if (webhooks.size > 0) {
      await supabase.from('scan_logs').insert({
        scan_id: scanId,
        level: 'info',
        message: `Found ${webhooks.size} potential webhook endpoints`,
        details: { endpoints: Array.from(webhooks.keys()) }
      })
    }

    // 2. Test each discovered webhook
    for (const [endpoint, info] of webhooks) {
      const vulns = await testWebhookSecurity(endpoint, info)
      allVulnerabilities.push(...vulns)
    }

    // 3. Test service-specific webhooks
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Testing service-specific webhook patterns',
      details: {}
    })
    
    const serviceVulns = await testServiceSpecificWebhooks(targetUrl)
    allVulnerabilities.push(...serviceVulns)

    // 4. Check for webhook documentation
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Checking for exposed webhook documentation',
      details: {}
    })
    
    const docVulns = await checkWebhookDocumentation(targetUrl)
    allVulnerabilities.push(...docVulns)

    // Save vulnerabilities to database
    if (allVulnerabilities.length > 0) {
      const vulnerabilityRecords = allVulnerabilities.map(vuln => ({
        scan_id: scanId,
        title: vuln.title,
        description: vuln.description,
        severity: vuln.severity,
        affected_component: vuln.endpoint,
        owasp_category: vuln.title.includes('Authentication') || vuln.title.includes('Signature')
          ? 'A07:2021 - Identification and Authentication Failures'
          : vuln.title.includes('SSRF')
          ? 'A10:2021 - Server-Side Request Forgery'
          : 'A04:2021 - Insecure Design',
        evidence: typeof vuln.evidence === 'object' ? 
          JSON.stringify(vuln.evidence) : vuln.evidence,
        recommendation: vuln.title.includes('Signature')
          ? 'Implement proper webhook signature validation'
          : vuln.title.includes('SSRF')
          ? 'Validate and sanitize all URLs in webhook payloads'
          : vuln.title.includes('Replay')
          ? 'Implement timestamp validation to prevent replay attacks'
          : 'Implement proper authentication and validation for webhook endpoints'
      }))

      await supabase
        .from('vulnerabilities')
        .insert(vulnerabilityRecords)
    }

    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: `Webhook scan completed. Found ${allVulnerabilities.length} vulnerabilities`,
      details: { 
        vulnerabilityCount: allVulnerabilities.length,
        webhookEndpoints: webhooks.size
      }
    })

    return new Response(
      JSON.stringify({ 
        success: true,
        vulnerabilities: allVulnerabilities.length,
        webhookEndpoints: webhooks.size
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})