-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON> custom types
CREATE TYPE scan_status AS ENUM ('pending', 'queued', 'running', 'completed', 'failed', 'cancelled');
CREATE TYPE scan_type AS ENUM ('api_endpoints', 'database_security', 'secrets', 'firebase', 'webhooks', 'comprehensive');
CREATE TYPE severity_level AS ENUM ('critical', 'high', 'medium', 'low', 'info');
CREATE TYPE notification_type AS ENUM ('scan_complete', 'scan_failed', 'vulnerability_found', 'system');

-- Create user profiles table (extends auth.users)
CREATE TABLE public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  username TEXT UNIQUE NOT NULL,
  full_name TEXT,
  phone TEXT,
  location TEXT,
  company TEXT,
  avatar_url TEXT,
  -- Notification preferences
  email_notifications BOOLEAN DEFAULT true,
  browser_notifications B<PERSON><PERSON><PERSON>N DEFAULT true,
  scan_completion_alerts <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT true,
  vulnerability_alerts <PERSON>O<PERSON><PERSON><PERSON> DEFAULT true,
  weekly_reports BOOLEAN DEFAULT false,
  -- API settings
  api_key UUID DEFAULT gen_random_uuid() UNIQUE,
  rate_limit_per_minute INTEGER DEFAULT 60,
  max_concurrent_scans INTEGER DEFAULT 5,
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create scans table
CREATE TABLE public.scans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  scan_name TEXT NOT NULL,
  scan_description TEXT,
  target_url TEXT NOT NULL,
  scan_type scan_type NOT NULL,
  status scan_status DEFAULT 'pending',
  progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  -- Scan configuration
  config JSONB DEFAULT '{}',
  -- Results summary
  vulnerabilities_count INTEGER DEFAULT 0,
  critical_count INTEGER DEFAULT 0,
  high_count INTEGER DEFAULT 0,
  medium_count INTEGER DEFAULT 0,
  low_count INTEGER DEFAULT 0,
  info_count INTEGER DEFAULT 0,
  -- Timing
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  -- Constraints
  CONSTRAINT valid_dates CHECK (started_at IS NULL OR completed_at IS NULL OR started_at <= completed_at)
);

-- Create vulnerabilities table
CREATE TABLE public.vulnerabilities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scan_id UUID REFERENCES scans(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  severity severity_level NOT NULL,
  cvss_score DECIMAL(3,1) CHECK (cvss_score >= 0 AND cvss_score <= 10),
  cvss_vector TEXT,
  owasp_category TEXT,
  cwe_id TEXT,
  -- Evidence and details
  evidence JSONB DEFAULT '{}',
  affected_component TEXT,
  attack_vector TEXT,
  -- Recommendations
  recommendation TEXT,
  reference_links TEXT[],
  -- Status
  is_false_positive BOOLEAN DEFAULT false,
  is_resolved BOOLEAN DEFAULT false,
  resolved_at TIMESTAMPTZ,
  resolved_by UUID REFERENCES auth.users(id),
  notes TEXT,
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create notifications table
CREATE TABLE public.notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type notification_type NOT NULL,
  is_read BOOLEAN DEFAULT false,
  read_at TIMESTAMPTZ,
  -- Related data
  scan_id UUID REFERENCES scans(id) ON DELETE CASCADE,
  vulnerability_id UUID REFERENCES vulnerabilities(id) ON DELETE CASCADE,
  data JSONB DEFAULT '{}',
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create scan_logs table for detailed scan progress
CREATE TABLE public.scan_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scan_id UUID REFERENCES scans(id) ON DELETE CASCADE NOT NULL,
  level TEXT NOT NULL CHECK (level IN ('info', 'warning', 'error', 'debug')),
  message TEXT NOT NULL,
  details JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create api_usage table for tracking API usage
CREATE TABLE public.api_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  endpoint TEXT NOT NULL,
  method TEXT NOT NULL,
  status_code INTEGER,
  response_time_ms INTEGER,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_scans_user_id ON scans(user_id);
CREATE INDEX idx_scans_status ON scans(status);
CREATE INDEX idx_scans_created_at ON scans(created_at DESC);
CREATE INDEX idx_vulnerabilities_scan_id ON vulnerabilities(scan_id);
CREATE INDEX idx_vulnerabilities_severity ON vulnerabilities(severity);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_scan_logs_scan_id ON scan_logs(scan_id);
CREATE INDEX idx_api_usage_user_id ON api_usage(user_id);
CREATE INDEX idx_api_usage_created_at ON api_usage(created_at DESC);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE scans ENABLE ROW LEVEL SECURITY;
ALTER TABLE vulnerabilities ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE scan_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_usage ENABLE ROW LEVEL SECURITY;

-- RLS Policies for profiles
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- RLS Policies for scans
CREATE POLICY "Users can view own scans" ON scans
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own scans" ON scans
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own scans" ON scans
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own scans" ON scans
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for vulnerabilities
CREATE POLICY "Users can view vulnerabilities of own scans" ON vulnerabilities
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM scans 
      WHERE scans.id = vulnerabilities.scan_id 
      AND scans.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update vulnerabilities of own scans" ON vulnerabilities
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM scans 
      WHERE scans.id = vulnerabilities.scan_id 
      AND scans.user_id = auth.uid()
    )
  );

-- RLS Policies for notifications
CREATE POLICY "Users can view own notifications" ON notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON notifications
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own notifications" ON notifications
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for scan_logs
CREATE POLICY "Users can view logs of own scans" ON scan_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM scans 
      WHERE scans.id = scan_logs.scan_id 
      AND scans.user_id = auth.uid()
    )
  );

-- RLS Policies for api_usage
CREATE POLICY "Users can view own API usage" ON api_usage
  FOR SELECT USING (auth.uid() = user_id);

-- Functions
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_scans_updated_at BEFORE UPDATE ON scans
  FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_vulnerabilities_updated_at BEFORE UPDATE ON vulnerabilities
  FOR EACH ROW EXECUTE FUNCTION update_updated_at();

-- Function to create user profile on signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, username, full_name)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
    NEW.raw_user_meta_data->>'full_name'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to update scan vulnerability counts
CREATE OR REPLACE FUNCTION update_scan_vulnerability_counts()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE scans
  SET 
    vulnerabilities_count = (
      SELECT COUNT(*) FROM vulnerabilities 
      WHERE scan_id = COALESCE(NEW.scan_id, OLD.scan_id)
      AND NOT is_false_positive
    ),
    critical_count = (
      SELECT COUNT(*) FROM vulnerabilities 
      WHERE scan_id = COALESCE(NEW.scan_id, OLD.scan_id) 
      AND severity = 'critical'
      AND NOT is_false_positive
    ),
    high_count = (
      SELECT COUNT(*) FROM vulnerabilities 
      WHERE scan_id = COALESCE(NEW.scan_id, OLD.scan_id) 
      AND severity = 'high'
      AND NOT is_false_positive
    ),
    medium_count = (
      SELECT COUNT(*) FROM vulnerabilities 
      WHERE scan_id = COALESCE(NEW.scan_id, OLD.scan_id) 
      AND severity = 'medium'
      AND NOT is_false_positive
    ),
    low_count = (
      SELECT COUNT(*) FROM vulnerabilities 
      WHERE scan_id = COALESCE(NEW.scan_id, OLD.scan_id) 
      AND severity = 'low'
      AND NOT is_false_positive
    ),
    info_count = (
      SELECT COUNT(*) FROM vulnerabilities 
      WHERE scan_id = COALESCE(NEW.scan_id, OLD.scan_id) 
      AND severity = 'info'
      AND NOT is_false_positive
    )
  WHERE id = COALESCE(NEW.scan_id, OLD.scan_id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update scan counts when vulnerabilities change
CREATE TRIGGER update_scan_counts_on_vulnerability_change
  AFTER INSERT OR UPDATE OR DELETE ON vulnerabilities
  FOR EACH ROW EXECUTE FUNCTION update_scan_vulnerability_counts();

-- Create views for common queries
CREATE VIEW scan_summary AS
SELECT 
  s.*,
  p.username,
  p.full_name,
  COUNT(DISTINCT v.id) FILTER (WHERE NOT v.is_false_positive) as total_vulnerabilities
FROM scans s
JOIN profiles p ON s.user_id = p.id
LEFT JOIN vulnerabilities v ON s.id = v.scan_id
GROUP BY s.id, p.username, p.full_name;

-- Grant permissions for views
GRANT SELECT ON scan_summary TO authenticated;
