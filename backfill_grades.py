#!/usr/bin/env python3
"""
Backfill security grades for existing scans.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.scan import Scan, ScanStatus
from app.services.grading import calculate_security_grade

# Create sync database session
sync_database_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
sync_engine = create_engine(sync_database_url)
SyncSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=sync_engine)

def backfill_security_grades():
    """Calculate and save security grades for all completed scans that don't have grades."""
    db = SyncSessionLocal()
    try:
        # Get all completed scans without security grades
        scans = db.query(Scan).filter(
            Scan.status == ScanStatus.COMPLETED,
            Scan.security_grade.is_(None)
        ).all()
        
        print(f"Found {len(scans)} completed scans without security grades")
        
        updated_count = 0
        for scan in scans:
            try:
                # Calculate security grade
                grade = calculate_security_grade(
                    scan.critical_count or 0,
                    scan.high_count or 0,
                    scan.medium_count or 0,
                    scan.low_count or 0
                )
                
                # Update the scan with the grade value (grade is now a string)
                scan.security_grade = grade

                print(f"Scan {scan.id}: {scan.critical_count}C/{scan.high_count}H/{scan.medium_count}M/{scan.low_count}L -> Grade {grade}")
                updated_count += 1
                
            except Exception as e:
                print(f"Error calculating grade for scan {scan.id}: {e}")
        
        # Commit all changes
        db.commit()
        print(f"Successfully updated {updated_count} scans with security grades")
        
    except Exception as e:
        print(f"Error during backfill: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    backfill_security_grades()
