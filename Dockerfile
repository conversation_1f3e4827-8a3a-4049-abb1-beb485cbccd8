# Multi-stage Docker build for VibeRush Security Scanner

# Build stage
FROM python:3.11-slim as builder

WORKDIR /build

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better layer caching
COPY requirements/base.txt requirements/base.txt
RUN pip install --no-cache-dir --user -r requirements/base.txt

# Copy application code
COPY . .

# Production stage
FROM python:3.11-slim as production

# Create non-root user
RUN groupadd -r scanner && useradd --no-log-init -r -g scanner scanner

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy Python packages from builder stage
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application code
COPY --chown=scanner:scanner . .

# Make sure scripts are executable
RUN chmod +x scripts/entrypoint.sh scripts/wait-for-it.sh

# Set environment variables
ENV PYTHONPATH=/app
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/health/ || exit 1

# Switch to non-root user
USER scanner

# Expose port
EXPOSE 8000

# Default command
CMD ["scripts/entrypoint.sh"]