version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: mostlysecure
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Celery and Caching
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # MostlySecure API Server
  api:
    build:
      context: .
      target: production
    environment:
      DATABASE_URL: postgresql+asyncpg://postgres:${DB_PASSWORD:-password}@db:5432/mostlysecure
      REDIS_URL: redis://redis:6379
      SECRET_KEY: ${SECRET_KEY:-your-super-secret-key-change-in-production}
      DEBUG: ${DEBUG:-false}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_FORMAT: JSON
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Celery Worker for Background Tasks
  worker:
    build:
      context: .
      target: production
    command: ["celery", "-A", "app.services.celery_app", "worker", "--loglevel=info", "--queues=celery,scans,maintenance", "--events", "--task-events"]
    environment:
      DATABASE_URL: postgresql+asyncpg://postgres:${DB_PASSWORD:-password}@db:5432/mostlysecure
      REDIS_URL: redis://redis:6379
      SECRET_KEY: ${SECRET_KEY:-your-super-secret-key-change-in-production}
      DEBUG: ${DEBUG:-false}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_FORMAT: JSON
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    scale: 2

  # Celery Beat for Scheduled Tasks
  scheduler:
    build:
      context: .
      target: production
    command: ["celery", "-A", "app.services.celery_app", "beat", "--loglevel=info", "--schedule=/tmp/celerybeat-schedule"]
    environment:
      DATABASE_URL: postgresql+asyncpg://postgres:${DB_PASSWORD:-password}@db:5432/mostlysecure
      REDIS_URL: redis://redis:6379
      SECRET_KEY: ${SECRET_KEY:-your-super-secret-key-change-in-production}
      DEBUG: ${DEBUG:-false}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_FORMAT: JSON
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped


  # Flower for Celery Monitoring
  flower:
    build:
      context: .
      target: production
    command: ["celery", "-A", "app.services.celery_app", "flower", "--broker=redis://redis:6379", "--port=5555", "--persistent=true", "--db=/tmp/flower.db", "--max_tasks=10000"]
    environment:
      DATABASE_URL: postgresql+asyncpg://postgres:${DB_PASSWORD:-password}@db:5432/mostlysecure
      REDIS_URL: redis://redis:6379
      SECRET_KEY: ${SECRET_KEY:-your-super-secret-key-change-in-production}
      DEBUG: ${DEBUG:-false}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_FORMAT: JSON
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    ports:
      - "5555:5555"
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - api
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    driver: bridge