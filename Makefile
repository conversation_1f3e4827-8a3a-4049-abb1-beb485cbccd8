# MostlySecure Security Scanner Makefile

.PHONY: help build up down logs restart clean test lint format migrate shell

# Default target
help: ## Show this help message
	@echo "MostlySecure Security Scanner - Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-15s\033[0m %s\n", $$1, $$2}'

build: ## Build Docker images
	docker compose build

up: ## Start all services
	docker compose up -d

down: ## Stop all services
	docker compose down

logs: ## Show logs for all services
	docker compose logs -f

restart: ## Restart all services
	docker compose restart

clean: ## Clean up containers, images, and volumes
	docker compose down -v --remove-orphans
	docker system prune -f

test: ## Run tests
	docker compose run --rm api python -m pytest tests/

lint: ## Run linting
	docker compose run --rm api python -m flake8 app/
	docker compose run --rm api python -m mypy app/

format: ## Format code
	docker compose run --rm api python -m black app/

migrate: ## Run database migrations
	docker compose run --rm api alembic upgrade head

shell: ## Open Python shell in API container
	docker compose run --rm api python

db-shell: ## Open database shell
	docker compose exec db psql -U postgres -d viberush

redis-shell: ## Open Redis shell
	docker compose exec redis redis-cli

# Development commands
dev-setup: ## Set up development environment
	cp .env.example .env
	docker compose up -d db redis
	sleep 5
	docker compose run --rm api alembic upgrade head

dev-down: ## Stop development environment
	docker compose down

# Production commands
prod-build: ## Build production images
	docker compose -f docker-compose.yml -f docker-compose.prod.yml build

prod-up: ## Start production environment
	docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d

prod-down: ## Stop production environment
	docker compose -f docker-compose.yml -f docker-compose.prod.yml down

# Backup and restore
backup-db: ## Backup database
	docker compose exec db pg_dump -U postgres -d viberush > backup_$(shell date +%Y%m%d_%H%M%S).sql

restore-db: ## Restore database (requires BACKUP_FILE variable)
	docker compose exec -T db psql -U postgres -d viberush < $(BACKUP_FILE)

# Monitoring
status: ## Show service status
	docker compose ps

health: ## Check health of all services
	docker compose exec api curl -f http://localhost:8000/api/v1/health/ || echo "API unhealthy"
	docker compose exec db pg_isready -U postgres || echo "Database unhealthy"
	docker compose exec redis redis-cli ping || echo "Redis unhealthy"