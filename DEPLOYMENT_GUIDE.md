# MostlySecure Deployment Guide - Supabase & Vercel

This guide walks through deploying MostlySecure on Supabase (database & auth) and Vercel (frontend & API).

## Prerequisites

- [Vercel Account](https://vercel.com)
- [Supabase Account](https://supabase.com)
- [Supabase CLI](https://supabase.com/docs/guides/cli) installed
- [Vercel CLI](https://vercel.com/cli) installed (optional)
- Git repository with your code

## Step 1: Set Up Supabase Project

### 1.1 Create Supabase Project

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Click "New Project"
3. Fill in:
   - Project name: `mostlysecure-scanner`
   - Database Password: (save this securely)
   - Region: Choose closest to your users
   - Organization: `<EMAIL>` (Free tier)

### 1.2 Get Connection Details

Once created, go to Settings > API and note down:
- Project URL: `https://hmseiygynzyvdxlnotcs.supabase.co`
- Anon/Public Key: `eyJhbGc...` (safe for client-side)
- Service Role Key: `eyJhbGc...` (keep secret, server-side only)

### 1.3 Link Local Project

```bash
cd frontend
supabase link --project-ref hmseiygynzyvdxlnotcs
```

### 1.4 Push Database Schema

```bash
# Push migrations to production
supabase db push

# Deploy Edge Functions
supabase functions deploy
```

## Step 2: Configure Supabase

### 2.1 Enable Realtime

1. Go to Database > Replication
2. Enable replication for tables:
   - `scans`
   - `vulnerabilities`
   - `notifications`

### 2.2 Configure Authentication

1. Go to Authentication > Providers
2. Configure Email Auth:
   - Enable Email provider
   - Disable "Confirm email" for testing (enable in production)
   - Set up SMTP for production emails

### 2.3 Set Up Storage (Optional)

1. Go to Storage
2. Create bucket: `scan-reports`
3. Set policies for authenticated users

## Step 3: Deploy to Vercel

### 3.1 Connect Repository

1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Click "Add New" > "Project"
3. Import your Git repository
4. Select `frontend` as the root directory

### 3.2 Configure Environment Variables

Add these environment variables in Vercel:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://hmseiygynzyvdxlnotcs.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Cron Jobs
CRON_SECRET=generate-a-secure-random-string

# Optional: External Services
VIRUSTOTAL_API_KEY=your-api-key
SHODAN_API_KEY=your-api-key
```

### 3.3 Deploy

```bash
# Using Vercel CLI
cd frontend
vercel --prod

# Or push to main branch for automatic deployment
git push origin main
```

## Step 4: Post-Deployment Setup

### 4.1 Configure Cron Jobs

Vercel will automatically set up cron jobs based on `vercel.json`.

### 4.2 Set Up Custom Domain (Optional)

1. In Vercel Dashboard > Settings > Domains
2. Add your domain
3. Update DNS records as instructed

### 4.3 Configure Supabase Auth URLs

1. Go to Supabase Dashboard > Authentication > URL Configuration
2. Add your production URL to:
   - Site URL: `https://your-app.vercel.app`
   - Redirect URLs: `https://your-app.vercel.app/**`

## Step 5: Testing

### 5.1 Test Authentication

1. Register a new user
2. Check Supabase Dashboard > Authentication > Users
3. Verify login works

### 5.2 Test Scanning

1. Create a test scan
2. Monitor Edge Function logs in Supabase Dashboard
3. Check realtime updates are working

### 5.3 Monitor Performance

1. Vercel Dashboard > Analytics
2. Supabase Dashboard > Reports
3. Edge Function logs

## Environment Variables Reference

### Required

| Variable | Description | Where to Find |
|----------|-------------|---------------|
| `NEXT_PUBLIC_SUPABASE_URL` | Supabase project URL | Supabase Dashboard > Settings > API |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Public anonymous key | Supabase Dashboard > Settings > API |
| `SUPABASE_SERVICE_ROLE_KEY` | Service role key (secret) | Supabase Dashboard > Settings > API |
| `CRON_SECRET` | Secret for cron authentication | Generate with `openssl rand -hex 32` |

### Optional

| Variable | Description | Default |
|----------|-------------|---------|
| `NEXT_PUBLIC_APP_URL` | Your app's URL | Auto-detected by Vercel |
| `VIRUSTOTAL_API_KEY` | VirusTotal API key | - |
| `SHODAN_API_KEY` | Shodan API key | - |

## Troubleshooting

### Database Connection Issues

```bash
# Test connection
supabase db remote status

# Reset migrations if needed
supabase db reset --linked
```

### Edge Function Issues

```bash
# View logs
supabase functions logs scan-orchestrator

# Test locally
supabase functions serve scan-orchestrator
```

### Authentication Issues

1. Check Supabase Auth logs
2. Verify environment variables in Vercel
3. Check browser console for errors

## Security Checklist

- [ ] Enable RLS on all tables
- [ ] Set up proper CORS in Edge Functions
- [ ] Use environment variables for all secrets
- [ ] Enable 2FA on Supabase and Vercel accounts
- [ ] Set up proper backup strategy
- [ ] Configure rate limiting
- [ ] Enable audit logging

## Monitoring

### Recommended Services

1. **Uptime Monitoring**: Better Uptime, Pingdom
2. **Error Tracking**: Sentry, LogRocket
3. **Analytics**: Vercel Analytics, PostHog
4. **Performance**: Vercel Speed Insights

## Backup Strategy

### Database Backups

Supabase automatically backs up your database daily (Pro plan) or weekly (Free plan).

For manual backups:
```bash
# Export data
supabase db dump -f backup.sql

# Restore if needed
psql $DATABASE_URL < backup.sql
```

### Code Backups

- Use Git tags for releases
- Keep deployment history in Vercel

## Scaling Considerations

### When to Upgrade

1. **Supabase Free Tier Limits**:
   - 500MB database
   - 2GB bandwidth
   - 50MB file storage

2. **Vercel Free Tier Limits**:
   - 100GB bandwidth
   - 100 deployments/day
   - 10 second function timeout

### Optimization Tips

1. Enable Vercel Edge caching
2. Use Supabase connection pooling
3. Implement proper indexing
4. Use CDN for static assets

## Support

- **Supabase Support**: <EMAIL>
- **Vercel Support**: <EMAIL>
- **MostlySecure Issues**: [GitHub Issues](https://github.com/your-org/mostlysecure)

---

**Last Updated**: December 2024