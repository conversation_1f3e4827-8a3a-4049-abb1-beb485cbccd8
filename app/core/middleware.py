import time
import uuid
from typing import Callable
from fastapi import Request, Response
from fastapi.responses import JSONResponse
import structlog
from app.core.config import settings

logger = structlog.get_logger()


async def request_logging_middleware(request: Request, call_next: Callable) -> Response:
    """Log all requests and responses with performance metrics."""
    
    # Generate request ID for tracing
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id
    
    # Start timer
    start_time = time.time()
    
    # Log request
    logger.info(
        "Request started",
        request_id=request_id,
        method=request.method,
        url=str(request.url),
        client_ip=request.client.host,
        user_agent=request.headers.get("user-agent"),
        content_length=request.headers.get("content-length")
    )
    
    try:
        # Process request
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Log response
        logger.info(
            "Request completed",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            status_code=response.status_code,
            process_time=round(process_time, 4),
            response_size=response.headers.get("content-length")
        )
        
        # Add request ID to response headers
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Process-Time"] = str(round(process_time, 4))
        
        return response
        
    except Exception as e:
        process_time = time.time() - start_time
        
        logger.error(
            "Request failed",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            process_time=round(process_time, 4),
            error=str(e),
            error_type=type(e).__name__
        )
        
        # Return error response
        return JSONResponse(
            status_code=500,
            content={
                "detail": "Internal server error",
                "request_id": request_id
            },
            headers={"X-Request-ID": request_id}
        )


async def security_headers_middleware(request: Request, call_next: Callable) -> Response:
    """Add security headers to all responses."""

    response = await call_next(request)

    # Check if this is a docs page that needs external resources
    docs_paths = ["/docs", "/redoc", "/openapi.json"]
    is_docs_page = any(request.url.path == path or request.url.path.startswith(path + "/") for path in docs_paths)

    if is_docs_page:
        # For documentation pages, only add minimal security headers
        minimal_headers = {
            "X-Content-Type-Options": "nosniff",
            "Referrer-Policy": "strict-origin-when-cross-origin",
        }
        for header, value in minimal_headers.items():
            response.headers[header] = value
        # Explicitly do NOT add CSP for docs pages
        return response

    # Security headers for non-docs pages
    security_headers = {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0"
    }

    # Add security headers
    for header, value in security_headers.items():
        response.headers[header] = value

    # Add CSP header for non-API and non-docs responses
    if not request.url.path.startswith("/api/"):
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self'; "
            "connect-src 'self'; "
            "frame-ancestors 'none'"
        )

    return response


async def rate_limit_middleware(request: Request, call_next: Callable) -> Response:
    """Apply rate limiting middleware."""
    
    # Skip rate limiting for health checks
    if request.url.path in ["/api/v1/health/", "/health"]:
        return await call_next(request)
    
    try:
        response = await call_next(request)
        return response
    except Exception as e:
        if "rate limit" in str(e).lower():
            logger.warning(
                "Rate limit exceeded",
                client_ip=request.client.host,
                url=str(request.url),
                user_agent=request.headers.get("user-agent")
            )
        raise


async def error_handling_middleware(request: Request, call_next: Callable) -> Response:
    """Global error handling middleware."""
    
    try:
        return await call_next(request)
    except Exception as e:
        request_id = getattr(request.state, 'request_id', 'unknown')
        
        logger.error(
            "Unhandled exception",
            request_id=request_id,
            error=str(e),
            error_type=type(e).__name__,
            url=str(request.url),
            method=request.method
        )
        
        # Don't expose internal errors in production
        if settings.DEBUG:
            detail = str(e)
        else:
            detail = "Internal server error"
        
        return JSONResponse(
            status_code=500,
            content={
                "detail": detail,
                "request_id": request_id,
                "error_type": type(e).__name__ if settings.DEBUG else "InternalError"
            },
            headers={"X-Request-ID": request_id}
        )