from typing import Any, Dict, Optional
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
import structlog

logger = structlog.get_logger()


class MostlySecureException(Exception):
    """Base exception for MostlySecure application."""
    
    def __init__(
        self,
        message: str,
        status_code: int = 500,
        error_code: str = "INTERNAL_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ScanException(MostlySecureException):
    """Exception raised during scanning operations."""
    
    def __init__(
        self,
        message: str,
        scan_id: Optional[int] = None,
        scanner_name: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            status_code=422,
            error_code="SCAN_ERROR",
            details=details or {}
        )
        self.scan_id = scan_id
        self.scanner_name = scanner_name


class AuthenticationException(MostlySecureException):
    """Exception raised for authentication errors."""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(
            message=message,
            status_code=401,
            error_code="AUTH_ERROR"
        )


class AuthorizationException(MostlySecureException):
    """Exception raised for authorization errors."""

    def __init__(self, message: str = "Insufficient permissions"):
        super().__init__(
            message=message,
            status_code=403,
            error_code="AUTHORIZATION_ERROR"
        )


class ValidationException(MostlySecureException):
    """Exception raised for validation errors."""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            status_code=422,
            error_code="VALIDATION_ERROR",
            details=details or {}
        )
        self.field = field


class RateLimitException(MostlySecureException):
    """Exception raised when rate limits are exceeded."""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        retry_after: Optional[int] = None
    ):
        super().__init__(
            message=message,
            status_code=429,
            error_code="RATE_LIMIT_ERROR"
        )
        self.retry_after = retry_after


class ResourceNotFoundException(MostlySecureException):
    """Exception raised when a resource is not found."""
    
    def __init__(
        self,
        resource_type: str,
        resource_id: Any = None
    ):
        if resource_id:
            message = f"{resource_type} with ID {resource_id} not found"
        else:
            message = f"{resource_type} not found"
            
        super().__init__(
            message=message,
            status_code=404,
            error_code="RESOURCE_NOT_FOUND"
        )
        self.resource_type = resource_type
        self.resource_id = resource_id


class ConflictException(MostlySecureException):
    """Exception raised when there's a conflict with the current state."""
    
    def __init__(self, message: str, resource_type: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=409,
            error_code="CONFLICT_ERROR"
        )
        self.resource_type = resource_type


class ServiceUnavailableException(MostlySecureException):
    """Exception raised when a service is unavailable."""
    
    def __init__(
        self,
        service_name: str,
        message: Optional[str] = None
    ):
        if not message:
            message = f"Service {service_name} is currently unavailable"
            
        super().__init__(
            message=message,
            status_code=503,
            error_code="SERVICE_UNAVAILABLE"
        )
        self.service_name = service_name


# Exception handlers
async def mostlysecure_exception_handler(request: Request, exc: MostlySecureException) -> JSONResponse:
    """Handle MostlySecure custom exceptions."""
    
    request_id = getattr(request.state, 'request_id', 'unknown')
    
    logger.error(
        "VibeRush exception occurred",
        request_id=request_id,
        error_code=exc.error_code,
        message=exc.message,
        status_code=exc.status_code,
        details=exc.details,
        url=str(request.url),
        method=request.method
    )
    
    response_content = {
        "detail": exc.message,
        "error_code": exc.error_code,
        "request_id": request_id
    }
    
    # Add additional details if available
    if exc.details:
        response_content["details"] = exc.details
    
    # Add retry-after header for rate limit errors
    headers = {"X-Request-ID": request_id}
    if isinstance(exc, RateLimitException) and exc.retry_after:
        headers["Retry-After"] = str(exc.retry_after)
    
    return JSONResponse(
        status_code=exc.status_code,
        content=response_content,
        headers=headers
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle FastAPI HTTP exceptions."""
    
    request_id = getattr(request.state, 'request_id', 'unknown')
    
    logger.warning(
        "HTTP exception occurred",
        request_id=request_id,
        status_code=exc.status_code,
        detail=exc.detail,
        url=str(request.url),
        method=request.method
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "detail": exc.detail,
            "request_id": request_id
        },
        headers={"X-Request-ID": request_id}
    )


async def validation_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle Pydantic validation exceptions."""
    
    request_id = getattr(request.state, 'request_id', 'unknown')
    
    # Extract validation errors
    if hasattr(exc, 'errors'):
        errors = []
        for error in exc.errors():
            errors.append({
                "field": ".".join(str(x) for x in error["loc"]),
                "message": error["msg"],
                "type": error["type"]
            })
    else:
        errors = [{"message": str(exc)}]
    
    logger.warning(
        "Validation exception occurred",
        request_id=request_id,
        errors=errors,
        url=str(request.url),
        method=request.method
    )
    
    return JSONResponse(
        status_code=422,
        content={
            "detail": "Validation error",
            "errors": errors,
            "request_id": request_id
        },
        headers={"X-Request-ID": request_id}
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle all other exceptions."""
    
    request_id = getattr(request.state, 'request_id', 'unknown')
    
    logger.error(
        "Unhandled exception occurred",
        request_id=request_id,
        error=str(exc),
        error_type=type(exc).__name__,
        url=str(request.url),
        method=request.method,
        exc_info=True
    )
    
    # Don't expose internal errors in production
    from app.core.config import settings
    if settings.DEBUG:
        detail = str(exc)
        error_type = type(exc).__name__
    else:
        detail = "Internal server error"
        error_type = "InternalError"
    
    return JSONResponse(
        status_code=500,
        content={
            "detail": detail,
            "error_type": error_type,
            "request_id": request_id
        },
        headers={"X-Request-ID": request_id}
    )