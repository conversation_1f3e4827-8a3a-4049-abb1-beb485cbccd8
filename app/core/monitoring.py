import time
import psutil
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
import structlog
import redis

from app.core.config import settings
from app.core.database import AsyncSessionLocal
from app.models.scan import Scan, ScanStatus
from app.models.user import User

logger = structlog.get_logger()


class SystemMonitor:
    """System monitoring and health checks."""
    
    def __init__(self):
        self.redis_client = redis.from_url(settings.REDIS_URL)
    
    async def get_system_health(self) -> Dict[str, Any]:
        """Get comprehensive system health status."""
        health_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "status": "healthy",
            "services": {},
            "system": {},
            "application": {}
        }
        
        try:
            # Check database health
            db_health = await self._check_database_health()
            health_data["services"]["database"] = db_health
            
            # Check Redis health
            redis_health = await self._check_redis_health()
            health_data["services"]["redis"] = redis_health
            
            # Check system resources
            system_health = self._check_system_resources()
            health_data["system"] = system_health
            
            # Check application metrics
            app_health = await self._check_application_health()
            health_data["application"] = app_health
            
            # Determine overall status
            if any(service.get("status") == "unhealthy" for service in health_data["services"].values()):
                health_data["status"] = "unhealthy"
            elif any(service.get("status") == "degraded" for service in health_data["services"].values()):
                health_data["status"] = "degraded"
            
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            health_data["status"] = "unhealthy"
            health_data["error"] = str(e)
        
        return health_data
    
    async def _check_database_health(self) -> Dict[str, Any]:
        """Check database connectivity and performance."""
        start_time = time.time()
        
        try:
            async with AsyncSessionLocal() as db:
                # Test basic connectivity
                await db.execute("SELECT 1")
                
                # Test query performance
                result = await db.execute(select(func.count(Scan.id)))
                scan_count = result.scalar()
                
                response_time = time.time() - start_time
                
                return {
                    "status": "healthy" if response_time < 1.0 else "degraded",
                    "response_time": round(response_time, 3),
                    "total_scans": scan_count,
                    "last_check": datetime.utcnow().isoformat()
                }
                
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "error": str(e),
                "response_time": time.time() - start_time,
                "last_check": datetime.utcnow().isoformat()
            }
    
    async def _check_redis_health(self) -> Dict[str, Any]:
        """Check Redis connectivity and performance."""
        start_time = time.time()
        
        try:
            # Test connectivity and performance
            ping_result = self.redis_client.ping()
            
            # Test read/write operations
            test_key = "health_check_test"
            self.redis_client.set(test_key, "test_value", ex=10)
            value = self.redis_client.get(test_key)
            self.redis_client.delete(test_key)
            
            response_time = time.time() - start_time
            
            # Get Redis info
            info = self.redis_client.info()
            
            return {
                "status": "healthy" if ping_result and response_time < 0.1 else "degraded",
                "response_time": round(response_time, 3),
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory_human", "unknown"),
                "last_check": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("Redis health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "error": str(e),
                "response_time": time.time() - start_time,
                "last_check": datetime.utcnow().isoformat()
            }
    
    def _check_system_resources(self) -> Dict[str, Any]:
        """Check system resource usage."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk = psutil.disk_usage('/')
            
            # Load average (Unix only)
            try:
                load_avg = psutil.getloadavg()
                load_1min = load_avg[0]
            except (AttributeError, OSError):
                load_1min = None
            
            return {
                "cpu_percent": cpu_percent,
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent,
                    "used": memory.used
                },
                "disk": {
                    "total": disk.total,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                },
                "load_average_1min": load_1min,
                "boot_time": datetime.fromtimestamp(psutil.boot_time()).isoformat()
            }
            
        except Exception as e:
            logger.error("System resource check failed", error=str(e))
            return {"error": str(e)}
    
    async def _check_application_health(self) -> Dict[str, Any]:
        """Check application-specific health metrics."""
        try:
            async with AsyncSessionLocal() as db:
                # Count active scans
                active_scans = await db.execute(
                    select(func.count(Scan.id)).where(
                        Scan.status.in_([ScanStatus.PENDING, ScanStatus.RUNNING])
                    )
                )
                active_scan_count = active_scans.scalar()
                
                # Count completed scans in last 24 hours
                yesterday = datetime.utcnow() - timedelta(hours=24)
                recent_scans = await db.execute(
                    select(func.count(Scan.id)).where(
                        Scan.status == ScanStatus.COMPLETED,
                        Scan.completed_at >= yesterday
                    )
                )
                recent_scan_count = recent_scans.scalar()
                
                # Count total users
                total_users = await db.execute(select(func.count(User.id)))
                user_count = total_users.scalar()
                
                # Count active users (logged in within last 7 days)
                week_ago = datetime.utcnow() - timedelta(days=7)
                active_users = await db.execute(
                    select(func.count(User.id)).where(
                        User.last_login >= week_ago
                    )
                )
                active_user_count = active_users.scalar()
                
                return {
                    "active_scans": active_scan_count,
                    "scans_last_24h": recent_scan_count,
                    "total_users": user_count,
                    "active_users_7d": active_user_count,
                    "uptime": self._get_uptime(),
                    "version": "1.0.0"
                }
                
        except Exception as e:
            logger.error("Application health check failed", error=str(e))
            return {"error": str(e)}
    
    def _get_uptime(self) -> str:
        """Get application uptime."""
        try:
            boot_time = psutil.boot_time()
            uptime_seconds = time.time() - boot_time
            uptime_delta = timedelta(seconds=uptime_seconds)
            return str(uptime_delta).split('.')[0]  # Remove microseconds
        except Exception:
            return "unknown"


class MetricsCollector:
    """Collect and store application metrics."""
    
    def __init__(self):
        self.redis_client = redis.from_url(settings.REDIS_URL)
    
    async def record_scan_metric(
        self,
        scan_id: int,
        metric_type: str,
        value: Any,
        tags: Optional[Dict[str, str]] = None
    ):
        """Record a scan-related metric."""
        try:
            metric_data = {
                "timestamp": datetime.utcnow().isoformat(),
                "scan_id": scan_id,
                "metric_type": metric_type,
                "value": value,
                "tags": tags or {}
            }
            
            # Store metric in Redis with TTL
            metric_key = f"metrics:scan:{scan_id}:{metric_type}:{int(time.time())}"
            self.redis_client.setex(
                metric_key,
                86400,  # 24 hours TTL
                str(metric_data)
            )
            
            # Also store in time-series format for aggregation
            ts_key = f"timeseries:scans:{metric_type}"
            self.redis_client.zadd(
                ts_key,
                {f"{scan_id}:{value}": time.time()}
            )
            
            # Clean old time-series data (keep last 7 days)
            week_ago = time.time() - (7 * 24 * 60 * 60)
            self.redis_client.zremrangebyscore(ts_key, 0, week_ago)
            
        except Exception as e:
            logger.error("Failed to record scan metric", error=str(e))
    
    async def record_api_metric(
        self,
        endpoint: str,
        method: str,
        status_code: int,
        response_time: float,
        user_id: Optional[int] = None
    ):
        """Record an API request metric."""
        try:
            metric_data = {
                "timestamp": datetime.utcnow().isoformat(),
                "endpoint": endpoint,
                "method": method,
                "status_code": status_code,
                "response_time": response_time,
                "user_id": user_id
            }
            
            # Store in Redis
            metric_key = f"metrics:api:{endpoint}:{method}:{int(time.time())}"
            self.redis_client.setex(
                metric_key,
                86400,  # 24 hours TTL
                str(metric_data)
            )
            
            # Update counters
            counter_key = f"counters:api:{endpoint}:{method}"
            self.redis_client.incr(counter_key)
            self.redis_client.expire(counter_key, 86400)
            
            # Update response time average
            rt_key = f"response_times:api:{endpoint}:{method}"
            self.redis_client.lpush(rt_key, response_time)
            self.redis_client.ltrim(rt_key, 0, 999)  # Keep last 1000 values
            self.redis_client.expire(rt_key, 86400)
            
        except Exception as e:
            logger.error("Failed to record API metric", error=str(e))
    
    async def get_metrics_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get metrics summary for the specified time period."""
        try:
            summary = {
                "period_hours": hours,
                "api_requests": {},
                "scan_metrics": {},
                "system_metrics": {}
            }
            
            # Get API request metrics
            api_patterns = self.redis_client.keys("counters:api:*")
            for pattern in api_patterns:
                key = pattern.decode('utf-8')
                count = self.redis_client.get(key)
                endpoint_method = key.replace("counters:api:", "")
                summary["api_requests"][endpoint_method] = int(count) if count else 0
            
            # Get average response times
            rt_patterns = self.redis_client.keys("response_times:api:*")
            for pattern in rt_patterns:
                key = pattern.decode('utf-8')
                times = self.redis_client.lrange(key, 0, -1)
                if times:
                    avg_time = sum(float(t) for t in times) / len(times)
                    endpoint_method = key.replace("response_times:api:", "")
                    if endpoint_method not in summary["api_requests"]:
                        summary["api_requests"][endpoint_method] = {}
                    if isinstance(summary["api_requests"][endpoint_method], int):
                        count = summary["api_requests"][endpoint_method]
                        summary["api_requests"][endpoint_method] = {
                            "count": count,
                            "avg_response_time": round(avg_time, 3)
                        }
                    else:
                        summary["api_requests"][endpoint_method]["avg_response_time"] = round(avg_time, 3)
            
            return summary
            
        except Exception as e:
            logger.error("Failed to get metrics summary", error=str(e))
            return {"error": str(e)}


# Global instances
system_monitor = SystemMonitor()
metrics_collector = MetricsCollector()