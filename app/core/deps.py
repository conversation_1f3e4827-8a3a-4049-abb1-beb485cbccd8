from typing import Generator, Optional
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.core.database import Async<PERSON>ess<PERSON><PERSON>ocal
from app.core.security import verify_token
from app.models.user import User
from app.schemas.auth import TokenData
import structlog
import time
import redis
from app.core.config import settings

logger = structlog.get_logger()
security = HTTPBearer()
redis_client = redis.from_url(settings.REDIS_URL)


async def get_db() -> AsyncSession:
    """Dependency to get database session."""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


async def get_current_user_token(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> TokenData:
    """Get current user from JWT token."""
    try:
        payload = verify_token(credentials.credentials, "access")
        user_id_str: str = payload.get("sub")
        username: str = payload.get("username")
        
        # Convert user_id to int
        try:
            user_id = int(user_id_str)
        except (ValueError, TypeError):
            user_id = None
        
        if user_id is None or username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return TokenData(user_id=user_id, username=username)
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Token validation error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_user(
    db: AsyncSession = Depends(get_db),
    token_data: TokenData = Depends(get_current_user_token)
) -> User:
    """Get current user from database."""
    result = await db.execute(
        select(User).where(User.id == token_data.user_id, User.is_active == True)
    )
    user = result.scalar_one_or_none()
    
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )
    
    return user


async def get_current_active_superuser(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current active superuser."""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user doesn't have enough privileges"
        )
    return current_user


async def check_rate_limit(
    request: Request,
    current_user: User = Depends(get_current_user)
) -> bool:
    """Check rate limiting for user."""
    client_ip = request.client.host
    user_id = current_user.id
    
    # Create rate limit key
    rate_limit_key = f"rate_limit:user:{user_id}:{int(time.time() // 60)}"
    
    try:
        # Get current request count
        current_count = redis_client.get(rate_limit_key)
        current_count = int(current_count) if current_count else 0
        
        # Check if limit exceeded
        if current_count >= current_user.rate_limit_per_minute:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Rate limit exceeded. Max {current_user.rate_limit_per_minute} requests per minute."
            )
        
        # Increment counter
        pipe = redis_client.pipeline()
        pipe.incr(rate_limit_key)
        pipe.expire(rate_limit_key, 60)  # Expire in 60 seconds
        pipe.execute()
        
        return True
        
    except redis.RedisError as e:
        logger.warning("Redis rate limiting failed", error=str(e))
        # If Redis fails, allow the request but log the error
        return True


async def check_scan_limit(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> bool:
    """Check if user has reached maximum concurrent scans."""
    from app.models.scan import Scan, ScanStatus
    
    # Count running scans for user
    result = await db.execute(
        select(Scan).where(
            Scan.user_id == current_user.id,
            Scan.status.in_([ScanStatus.PENDING, ScanStatus.RUNNING])
        )
    )
    running_scans_count = len(result.scalars().all())
    
    if running_scans_count >= current_user.max_concurrent_scans:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Maximum concurrent scans reached. Limit: {current_user.max_concurrent_scans}"
        )
    
    return True