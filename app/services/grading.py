"""
Security grading service for VibeRush Scanner.
Calculates security grades based on vulnerability counts.
"""

def calculate_security_grade(critical_count: int, high_count: int, medium_count: int, low_count: int) -> str:
    """
    Calculate security grade based on vulnerability counts.
    
    Logic:
    - If no critical vulnerabilities -> A grades possible
    - If has critical vulnerabilities -> B, C, D, F based on count
    
    Args:
        critical_count: Number of critical vulnerabilities
        high_count: Number of high severity vulnerabilities  
        medium_count: Number of medium severity vulnerabilities
        low_count: Number of low severity vulnerabilities
        
    Returns:
        SecurityGrade enum value
    """
    
    # Grade logic based on critical vulnerabilities first
    if critical_count == 0:
        # No critical vulnerabilities - A grade possible
        if high_count == 0:
            return "A+"  # Perfect - no critical or high vulnerabilities
        elif high_count <= 2:
            return "A"   # Excellent - no critical, minimal high
        elif high_count <= 5:
            return "B+"  # Very good - no critical, some high
        else:
            return "B"   # Good - no critical, but many high
    else:
        # Has critical vulnerabilities - B, C, D, F based on count
        if critical_count == 1:
            return "C+"  # One critical vulnerability
        elif critical_count <= 3:
            return "C"   # Few critical vulnerabilities
        elif critical_count <= 5:
            return "D"   # Several critical vulnerabilities
        else:
            return "F"   # Many critical vulnerabilities - immediate action required


def get_grade_description(grade: str) -> str:
    """Get human-readable description for a security grade."""
    descriptions = {
        "A+": "Exceptional security - zero critical or high vulnerabilities",
        "A": "Excellent security - no critical vulnerabilities",
        "B+": "Very good security - no critical vulnerabilities",
        "B": "Good security - no critical vulnerabilities",
        "C+": "Moderate security - 1 critical vulnerability found",
        "C": "Significant security issues - multiple critical vulnerabilities",
        "D": "Major security vulnerabilities - immediate attention required",
        "F": "Critical security failures - urgent action required"
    }
    return descriptions.get(grade, "Unknown grade")


def is_grade_eligible_for_badge(grade: str) -> bool:
    """Check if a security grade is eligible for trust badge."""
    return grade in ["A+", "A"]


def get_grade_color_info(grade: str) -> dict:
    """Get color information for displaying the grade."""
    color_info = {
        "A+": {"color": "emerald", "bg": "bg-emerald-100", "text": "text-emerald-700"},
        "A": {"color": "green", "bg": "bg-green-100", "text": "text-green-700"},
        "B+": {"color": "lime", "bg": "bg-lime-100", "text": "text-lime-700"},
        "B": {"color": "yellow", "bg": "bg-yellow-100", "text": "text-yellow-700"},
        "C+": {"color": "orange", "bg": "bg-orange-100", "text": "text-orange-700"},
        "C": {"color": "red", "bg": "bg-red-100", "text": "text-red-600"},
        "D": {"color": "red", "bg": "bg-red-200", "text": "text-red-700"},
        "F": {"color": "red", "bg": "bg-red-300", "text": "text-red-800"}
    }
    return color_info.get(grade, {"color": "gray", "bg": "bg-gray-100", "text": "text-gray-600"})
