import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, Any
from celery import current_task
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy import select, delete, create_engine
import structlog

from .celery_app import celery_app
from app.core.database import AsyncSessionLocal
from app.core.config import settings
from app.models.scan import Scan, ScanResult, Vulnerability, ScanStatus, ScanType
from app.services.grading import calculate_security_grade
from app.scanners import (
    APIEndpointScanner, DatabaseSecurityChecker, SecretsScanner,
    FirebaseAuditor, WebhookValidator, ComprehensiveScanner
)
from app.scanners.ultimate_database_scanner import UltimateDatabaseScanner
from app.scanners.ultimate_secrets_scanner import UltimateSecretsScanner

logger = structlog.get_logger()

# Create synchronous database engine for Celery tasks
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Convert async database URL to sync
sync_database_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
sync_engine = create_engine(sync_database_url)
SyncSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=sync_engine)


def _serialize_for_json(obj):
    """Convert objects to JSON-serializable format."""
    try:
        if obj is None:
            return None
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, (str, int, float, bool)):
            return obj
        elif isinstance(obj, (list, tuple)):
            return [_serialize_for_json(item) for item in obj]
        elif isinstance(obj, dict):
            return {str(key): _serialize_for_json(value) for key, value in obj.items()}
        elif hasattr(obj, '__dict__'):
            # Convert object to dict and recursively serialize
            result = {}
            for key, value in obj.__dict__.items():
                if not key.startswith('_'):  # Skip private attributes
                    try:
                        result[str(key)] = _serialize_for_json(value)
                    except (TypeError, ValueError):
                        # Skip problematic attributes
                        result[str(key)] = str(value)
            return result
        else:
            # For any other type, convert to string
            return str(obj)
    except Exception as e:
        # If all else fails, return string representation
        return f"<serialization_error: {str(e)}>"


@celery_app.task(bind=True, name="run_scan_task")
def run_scan_task(self, scan_id: int):
    """Execute a security scan synchronously."""
    try:
        # Store task info for progress updates
        task_info = {
            'task_id': self.request.id,
            'task_instance': self
        }

        # Execute scan synchronously - no asyncio needed
        return _execute_scan_sync(task_info, scan_id)

    except Exception as e:
        logger.error("Scan task failed", scan_id=scan_id, error=str(e))
        # Update scan status to failed synchronously
        try:
            _update_scan_status_sync(scan_id, ScanStatus.FAILED, error_message=str(e))
        except Exception as update_error:
            logger.error("Failed to update scan status after error", scan_id=scan_id, error=str(update_error))
        raise


def _execute_scan_sync(task_info: Dict[str, Any], scan_id: int) -> Dict[str, Any]:
    """Execute the actual scan logic synchronously."""
    scan = None
    db = None
    try:
        # Create a synchronous database session
        db = SyncSessionLocal()

        # Get scan details
        scan = db.query(Scan).filter(Scan.id == scan_id).first()

        if not scan:
            raise ValueError(f"Scan {scan_id} not found")

        # Update scan status to running
        scan.status = ScanStatus.RUNNING
        scan.started_at = datetime.now()
        scan.progress_percentage = 0
        db.commit()

        logger.info("Starting scan", scan_id=scan_id, scan_type=scan.scan_type, target_url=scan.target_url)

        # Update progress
        _update_progress_sync(task_info, 10, "Initializing scanner...")

        # Initialize appropriate scanner
        scanner = _get_scanner(scan.scan_type, scan)

        # Update progress
        _update_progress_sync(task_info, 20, "Running security scan...")

        # Execute the scan synchronously
        # Create a new event loop just for the scanner
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            scan_result = loop.run_until_complete(scanner.scan(
                target_url=scan.target_url,
                max_depth=scan.max_depth,
                timeout=scan.timeout_seconds,
                concurrent_requests=scan.concurrent_requests,
                custom_headers=scan.custom_headers or {}
            ))
        finally:
            loop.close()
            asyncio.set_event_loop(None)

        # Update progress
        _update_progress_sync(task_info, 80, "Processing results...")

        # Save scan results
        _save_scan_results_sync(db, scan, scan_result)

        # Update progress
        _update_progress_sync(task_info, 90, "Finalizing...")

        # Update scan status to completed
        scan.status = ScanStatus.COMPLETED
        scan.completed_at = datetime.now()
        scan.progress_percentage = 100

        # Update vulnerability counts
        vulnerability_counts = _calculate_vulnerability_counts_sync(db, scan_id)
        scan.total_vulnerabilities = vulnerability_counts["total"]
        scan.critical_count = vulnerability_counts["critical"]
        scan.high_count = vulnerability_counts["high"]
        scan.medium_count = vulnerability_counts["medium"]
        scan.low_count = vulnerability_counts["low"]

        # Calculate and save security grade
        grade = calculate_security_grade(
            scan.critical_count,
            scan.high_count,
            scan.medium_count,
            scan.low_count
        )
        scan.security_grade = grade

        logger.info("Calculated security grade",
                   scan_id=scan_id,
                   grade=scan.security_grade,
                   critical=scan.critical_count,
                   high=scan.high_count)

        db.commit()

        # Create completion notification
        logger.info("About to create scan completion notification", scan_id=scan.id, user_id=scan.user_id)
        _create_scan_completion_notification_sync(db, scan)
        logger.info("Finished creating scan completion notification", scan_id=scan.id)

        logger.info("Scan completed successfully",
                   scan_id=scan_id,
                   vulnerabilities_found=scan.total_vulnerabilities)

        return {
            "scan_id": scan_id,
            "status": "completed",
            "vulnerabilities_found": scan.total_vulnerabilities,
            "scan_duration": scan_result.scan_duration
        }

    except Exception as e:
        logger.error("Scan execution failed", scan_id=scan_id, error=str(e))

        # Try to update scan status to failed
        try:
            if db is not None:
                # Rollback any pending transaction
                db.rollback()

                if scan is not None:
                    # Use the existing scan object if available
                    scan.status = ScanStatus.FAILED
                    scan.error_message = str(e)
                    scan.completed_at = datetime.now()
                    db.commit()
                else:
                    # If scan wasn't loaded, try to fetch and update it
                    scan_record = db.query(Scan).filter(Scan.id == scan_id).first()
                    if scan_record:
                        scan_record.status = ScanStatus.FAILED
                        scan_record.error_message = str(e)
                        scan_record.completed_at = datetime.now()
                        db.commit()
                    else:
                        logger.error("Could not find scan to update status", scan_id=scan_id)
        except Exception as update_error:
            logger.error("Failed to update scan status", scan_id=scan_id, error=str(update_error))

        raise

    finally:
        # Always close the database session
        if db is not None:
            db.close()


def _update_progress_sync(task_info: Dict[str, Any], percentage: int, message: str):
    """Update task progress synchronously."""
    try:
        if task_info and 'task_instance' in task_info:
            task_instance = task_info['task_instance']
            # Use the original task instance to update state
            task_instance.update_state(
                state="PROGRESS",
                meta={"percentage": percentage, "message": message}
            )
    except Exception as e:
        # If progress update fails, just log it and continue
        logger.debug("Failed to update task progress", error=str(e))


def _save_scan_results_sync(db: Session, scan: Scan, scan_result):
    """Save scan results to database synchronously."""
    if not scan:
        raise ValueError("Scan object is None")

    try:
        # Serialize the scan result data safely
        try:
            raw_data = _serialize_for_json(scan_result.__dict__)
        except Exception as e:
            logger.warning("Failed to serialize raw_data, using fallback", error=str(e))
            raw_data = {
                "scanner_name": scan_result.scanner_name,
                "success": getattr(scan_result, 'success', False),
                "scan_duration": getattr(scan_result, 'scan_duration', 0),
                "vulnerabilities_count": len(getattr(scan_result, 'vulnerabilities', []))
            }

        try:
            scan_metadata = _serialize_for_json(scan_result.metadata)
        except Exception as e:
            logger.warning("Failed to serialize metadata, using fallback", error=str(e))
            scan_metadata = {"error": f"Serialization failed: {str(e)}"}

        # Save raw scan result
        db_scan_result = ScanResult(
            scan_id=scan.id,
            scanner_name=scan_result.scanner_name,
            raw_data=raw_data,
            processed_data={
                "vulnerabilities_count": len(scan_result.vulnerabilities),
                "scan_duration": scan_result.scan_duration,
                "success": scan_result.success
            },
            scan_metadata=scan_metadata
        )
        db.add(db_scan_result)

        # Save individual vulnerabilities
        for vuln_data in scan_result.vulnerabilities:
            # Handle business scoring fields
            business_priority = vuln_data.get("business_priority", "medium")

            # More robust enum conversion
            from app.models.scan import BusinessPriority
            if isinstance(business_priority, BusinessPriority):
                business_priority = business_priority.value
            elif hasattr(business_priority, 'value') and hasattr(business_priority, 'name'):
                # This is likely an enum object
                business_priority = business_priority.value
            elif isinstance(business_priority, str):
                # Convert any uppercase enum names to lowercase values
                business_priority = business_priority.lower()

            # Ensure it's a valid business priority value
            valid_priorities = ['very_low', 'low', 'medium', 'high', 'critical']
            if business_priority not in valid_priorities:
                logger.warning("Invalid business_priority value, defaulting to medium",
                             invalid_value=business_priority, type=type(business_priority))
                business_priority = "medium"

            # Debug logging
            logger.info("Creating vulnerability with business_priority",
                       business_priority=business_priority,
                       type=type(business_priority),
                       vuln_title=vuln_data["title"][:50])

            db_vulnerability = Vulnerability(
                scan_id=scan.id,
                title=vuln_data["title"],
                description=vuln_data["description"],
                severity=vuln_data["severity"],
                category=vuln_data["category"],
                url=vuln_data.get("url"),
                method=vuln_data.get("method"),
                parameter=vuln_data.get("parameter"),
                payload=vuln_data.get("payload"),
                evidence=vuln_data.get("evidence"),
                cvss_score=vuln_data.get("cvss_score"),
                cwe_id=vuln_data.get("cwe_id"),
                owasp_category=vuln_data.get("owasp_category"),
                remediation=vuln_data.get("remediation"),
                references=vuln_data.get("references", []),
                business_priority=business_priority,
                endpoint_score=vuln_data.get("endpoint_score", 50),
                is_likely_false_positive=vuln_data.get("is_likely_false_positive", False),
                false_positive_confidence=vuln_data.get("false_positive_confidence", 0)
            )
            db.add(db_vulnerability)

        db.commit()

    except Exception as e:
        logger.error("Failed to save scan results", scan_id=scan.id, error=str(e))
        raise


def _calculate_vulnerability_counts_sync(db: Session, scan_id: int) -> Dict[str, int]:
    """Calculate vulnerability counts by severity synchronously."""
    vulnerabilities = db.query(Vulnerability).filter(Vulnerability.scan_id == scan_id).all()

    counts = {
        "total": len(vulnerabilities),
        "critical": 0,
        "high": 0,
        "medium": 0,
        "low": 0
    }

    for vuln in vulnerabilities:
        if vuln.severity in counts:
            counts[vuln.severity] += 1

    return counts


def _update_scan_status_sync(scan_id: int, status: ScanStatus, error_message: str = None):
    """Update scan status in database synchronously."""
    db = SyncSessionLocal()
    try:
        scan = db.query(Scan).filter(Scan.id == scan_id).first()

        if scan:
            scan.status = status
            if error_message:
                scan.error_message = error_message
            if status == ScanStatus.FAILED:
                scan.completed_at = datetime.now()

            db.commit()

    except Exception as e:
        logger.error("Failed to update scan status", scan_id=scan_id, error=str(e))
    finally:
        db.close()


def _create_scan_completion_notification_sync(db: Session, scan: Scan):
    """Create a notification for scan completion synchronously."""
    logger.info("🔔 NOTIFICATION: Starting notification creation",
               scan_id=scan.id,
               user_id=scan.user_id,
               total_vulnerabilities=scan.total_vulnerabilities)

    try:
        from app.models.notification import Notification

        logger.info("🔔 NOTIFICATION: Imported Notification model successfully")

        # Determine notification type and message based on vulnerabilities found
        if scan.total_vulnerabilities > 0:
            # Check for high-priority vulnerabilities
            high_priority_count = scan.critical_count + scan.high_count
            if high_priority_count > 0:
                notification_type = "HIGH_PRIORITY_VULNERABILITY"
                title = f"🚨 High Priority Vulnerabilities Found"
                message = f"Scan of {scan.target_url} found {high_priority_count} high-priority vulnerabilities ({scan.critical_count} critical, {scan.high_count} high). Total: {scan.total_vulnerabilities} vulnerabilities."
            else:
                notification_type = "VULNERABILITY_FOUND"
                title = f"⚠️ Vulnerabilities Found"
                message = f"Scan of {scan.target_url} found {scan.total_vulnerabilities} vulnerabilities ({scan.medium_count} medium, {scan.low_count} low)."
        else:
            notification_type = "SCAN_COMPLETED"
            title = f"✅ Scan Completed Successfully"
            message = f"Security scan of {scan.target_url} completed successfully with no vulnerabilities found."

        logger.info("🔔 NOTIFICATION: Determined notification details",
                   notification_type=notification_type,
                   title=title)

        # Create the notification
        notification = Notification(
            user_id=scan.user_id,
            type=notification_type,
            title=title,
            message=message,
            scan_id=scan.id
        )

        logger.info("🔔 NOTIFICATION: Created notification object, adding to database")

        db.add(notification)
        logger.info("🔔 NOTIFICATION: Added notification to session, committing")

        db.commit()
        logger.info("🔔 NOTIFICATION: Successfully committed notification to database")

        logger.info("🔔 NOTIFICATION: Scan completion notification created successfully!",
                   scan_id=scan.id,
                   user_id=scan.user_id,
                   notification_type=notification_type,
                   vulnerabilities_found=scan.total_vulnerabilities)

    except Exception as e:
        logger.error("🔔 NOTIFICATION: Failed to create scan completion notification",
                    scan_id=scan.id,
                    error=str(e),
                    error_type=type(e).__name__)
        import traceback
        logger.error("🔔 NOTIFICATION: Full traceback", traceback=traceback.format_exc())


async def _execute_scan(task_info: Dict[str, Any], scan_id: int) -> Dict[str, Any]:
    """Execute the actual scan logic."""
    scan = None
    db = None
    try:
        # Create a fresh database session
        db = AsyncSessionLocal()

        # Get scan details
        result = await db.execute(select(Scan).where(Scan.id == scan_id))
        scan = result.scalar_one_or_none()

        if not scan:
            raise ValueError(f"Scan {scan_id} not found")

        # Update scan status to running
        scan.status = ScanStatus.RUNNING
        scan.started_at = datetime.utcnow()
        scan.progress_percentage = 0
        await db.commit()

        logger.info("Starting scan", scan_id=scan_id, scan_type=scan.scan_type, target_url=scan.target_url)

        # Update progress
        await _update_progress(task_info, 10, "Initializing scanner...")

        # Initialize appropriate scanner
        scanner = _get_scanner(scan.scan_type, scan)

        # Update progress
        await _update_progress(task_info, 20, "Running security scan...")

        # Execute the scan
        scan_result = await scanner.scan(
            target_url=scan.target_url,
            max_depth=scan.max_depth,
            timeout=scan.timeout_seconds,
            concurrent_requests=scan.concurrent_requests,
            custom_headers=scan.custom_headers or {}
        )

        # Update progress
        await _update_progress(task_info, 80, "Processing results...")

        # Save scan results
        await _save_scan_results(db, scan, scan_result)

        # Update progress
        await _update_progress(task_info, 90, "Finalizing...")

        # Update scan status to completed
        scan.status = ScanStatus.COMPLETED
        scan.completed_at = datetime.utcnow()
        scan.progress_percentage = 100

        # Update vulnerability counts
        vulnerability_counts = await _calculate_vulnerability_counts(db, scan_id)
        scan.total_vulnerabilities = vulnerability_counts["total"]
        scan.critical_count = vulnerability_counts["critical"]
        scan.high_count = vulnerability_counts["high"]
        scan.medium_count = vulnerability_counts["medium"]
        scan.low_count = vulnerability_counts["low"]

        # Calculate and save security grade
        grade = calculate_security_grade(
            scan.critical_count,
            scan.high_count,
            scan.medium_count,
            scan.low_count
        )
        scan.security_grade = grade

        logger.info("Calculated security grade",
                   scan_id=scan_id,
                   grade=scan.security_grade,
                   critical=scan.critical_count,
                   high=scan.high_count)

        await db.commit()

        logger.info("Scan completed successfully",
                   scan_id=scan_id,
                   vulnerabilities_found=scan.total_vulnerabilities)

        return {
            "scan_id": scan_id,
            "status": "completed",
            "vulnerabilities_found": scan.total_vulnerabilities,
            "scan_duration": scan_result.scan_duration
        }

    except Exception as e:
        logger.error("Scan execution failed", scan_id=scan_id, error=str(e))

        # Try to update scan status to failed
        try:
            if db is not None:
                # Rollback any pending transaction
                await db.rollback()

                if scan is not None:
                    # Use the existing scan object if available
                    scan.status = ScanStatus.FAILED
                    scan.error_message = str(e)
                    scan.completed_at = datetime.utcnow()
                    await db.commit()
                else:
                    # If scan wasn't loaded, try to fetch and update it
                    result = await db.execute(select(Scan).where(Scan.id == scan_id))
                    scan_record = result.scalar_one_or_none()
                    if scan_record:
                        scan_record.status = ScanStatus.FAILED
                        scan_record.error_message = str(e)
                        scan_record.completed_at = datetime.utcnow()
                        await db.commit()
                    else:
                        logger.error("Could not find scan to update status", scan_id=scan_id)
        except Exception as update_error:
            logger.error("Failed to update scan status", scan_id=scan_id, error=str(update_error))
            # Try one more time with a fresh database session
            try:
                async with AsyncSessionLocal() as fresh_db:
                    result = await fresh_db.execute(select(Scan).where(Scan.id == scan_id))
                    scan_record = result.scalar_one_or_none()
                    if scan_record:
                        scan_record.status = ScanStatus.FAILED
                        scan_record.error_message = str(e)
                        scan_record.completed_at = datetime.utcnow()
                        await fresh_db.commit()
            except Exception as final_error:
                logger.error("Final attempt to update scan status failed", scan_id=scan_id, error=str(final_error))

        raise

    finally:
        # Always close the database session
        if db is not None:
            await db.close()


def _get_scanner(scan_type: ScanType, scan: Scan):
    """Get appropriate scanner instance based on scan type."""
    scanner_map = {
        ScanType.API_ENDPOINTS: APIEndpointScanner,
        ScanType.DATABASE_SECURITY: UltimateDatabaseScanner,  # Use the ultimate scanner
        ScanType.SECRETS: UltimateSecretsScanner,  # Use the ultimate secrets scanner
        ScanType.FIREBASE: FirebaseAuditor,
        ScanType.WEBHOOKS: WebhookValidator,
        ScanType.COMPREHENSIVE: ComprehensiveScanner
    }

    scanner_class = scanner_map.get(scan_type)
    if not scanner_class:
        raise ValueError(f"Unknown scan type: {scan_type}")

    return scanner_class(
        timeout=scan.timeout_seconds,
        concurrent_requests=scan.concurrent_requests
    )


async def _save_scan_results(db: AsyncSession, scan: Scan, scan_result):
    """Save scan results to database."""
    if not scan:
        raise ValueError("Scan object is None")

    try:
        # Serialize the scan result data safely
        try:
            raw_data = _serialize_for_json(scan_result.__dict__)
        except Exception as e:
            logger.warning("Failed to serialize raw_data, using fallback", error=str(e))
            raw_data = {
                "scanner_name": scan_result.scanner_name,
                "success": getattr(scan_result, 'success', False),
                "scan_duration": getattr(scan_result, 'scan_duration', 0),
                "vulnerabilities_count": len(getattr(scan_result, 'vulnerabilities', []))
            }

        try:
            scan_metadata = _serialize_for_json(scan_result.metadata)
        except Exception as e:
            logger.warning("Failed to serialize metadata, using fallback", error=str(e))
            scan_metadata = {"error": f"Serialization failed: {str(e)}"}

        # Save raw scan result
        db_scan_result = ScanResult(
            scan_id=scan.id,
            scanner_name=scan_result.scanner_name,
            raw_data=raw_data,
            processed_data={
                "vulnerabilities_count": len(scan_result.vulnerabilities),
                "scan_duration": scan_result.scan_duration,
                "success": scan_result.success
            },
            scan_metadata=scan_metadata
        )
        db.add(db_scan_result)
        
        # Save individual vulnerabilities
        for vuln_data in scan_result.vulnerabilities:
            # Handle business scoring fields
            business_priority = vuln_data.get("business_priority", "medium")
            
            # More robust enum conversion
            from app.models.scan import BusinessPriority
            if isinstance(business_priority, BusinessPriority):
                business_priority = business_priority.value
            elif hasattr(business_priority, 'value') and hasattr(business_priority, 'name'):
                # This is likely an enum object
                business_priority = business_priority.value
            elif isinstance(business_priority, str):
                # Convert any uppercase enum names to lowercase values
                business_priority = business_priority.lower()
            
            # Ensure it's a valid business priority value
            valid_priorities = ['very_low', 'low', 'medium', 'high', 'critical']
            if business_priority not in valid_priorities:
                logger.warning("Invalid business_priority value, defaulting to medium", 
                             invalid_value=business_priority, type=type(business_priority))
                business_priority = "medium"
            
            # Debug logging
            logger.info("Creating vulnerability with business_priority", 
                       business_priority=business_priority, 
                       type=type(business_priority),
                       vuln_title=vuln_data["title"][:50])
                
            db_vulnerability = Vulnerability(
                scan_id=scan.id,
                title=vuln_data["title"],
                description=vuln_data["description"],
                severity=vuln_data["severity"],
                category=vuln_data["category"],
                url=vuln_data.get("url"),
                method=vuln_data.get("method"),
                parameter=vuln_data.get("parameter"),
                payload=vuln_data.get("payload"),
                evidence=vuln_data.get("evidence"),
                cvss_score=vuln_data.get("cvss_score"),
                cwe_id=vuln_data.get("cwe_id"),
                owasp_category=vuln_data.get("owasp_category"),
                remediation=vuln_data.get("remediation"),
                references=vuln_data.get("references", []),
                business_priority=business_priority,
                endpoint_score=vuln_data.get("endpoint_score", 50),
                is_likely_false_positive=vuln_data.get("is_likely_false_positive", False),
                false_positive_confidence=vuln_data.get("false_positive_confidence", 0)
            )
            db.add(db_vulnerability)
        
        await db.commit()
        
    except Exception as e:
        logger.error("Failed to save scan results", scan_id=scan.id, error=str(e))
        raise


async def _calculate_vulnerability_counts(db: AsyncSession, scan_id: int) -> Dict[str, int]:
    """Calculate vulnerability counts by severity."""
    result = await db.execute(
        select(Vulnerability).where(Vulnerability.scan_id == scan_id)
    )
    vulnerabilities = result.scalars().all()
    
    counts = {
        "total": len(vulnerabilities),
        "critical": 0,
        "high": 0,
        "medium": 0,
        "low": 0
    }
    
    for vuln in vulnerabilities:
        if vuln.severity in counts:
            counts[vuln.severity] += 1
    
    return counts


async def _update_progress(task_info: Dict[str, Any], percentage: int, message: str):
    """Update task progress."""
    try:
        if task_info and 'task_instance' in task_info:
            task_instance = task_info['task_instance']
            # Use the original task instance to update state
            task_instance.update_state(
                state="PROGRESS",
                meta={"percentage": percentage, "message": message}
            )
    except Exception as e:
        # If progress update fails, just log it and continue
        logger.debug("Failed to update task progress", error=str(e))


async def _update_scan_status(scan_id: int, status: ScanStatus, error_message: str = None):
    """Update scan status in database."""
    async with AsyncSessionLocal() as db:
        try:
            result = await db.execute(select(Scan).where(Scan.id == scan_id))
            scan = result.scalar_one_or_none()
            
            if scan:
                scan.status = status
                if error_message:
                    scan.error_message = error_message
                if status == ScanStatus.FAILED:
                    scan.completed_at = datetime.utcnow()
                
                await db.commit()
                
        except Exception as e:
            logger.error("Failed to update scan status", scan_id=scan_id, error=str(e))


@celery_app.task(name="cleanup_old_scans")
def cleanup_old_scans():
    """Clean up old completed scans."""
    # Create a new event loop for this task
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        return loop.run_until_complete(_cleanup_old_scans())
    finally:
        loop.close()
        asyncio.set_event_loop(None)


async def _cleanup_old_scans():
    """Clean up scans older than 30 days."""
    async with AsyncSessionLocal() as db:
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            # Delete old completed scans
            await db.execute(
                delete(Scan).where(
                    Scan.status == ScanStatus.COMPLETED,
                    Scan.completed_at < cutoff_date
                )
            )
            
            await db.commit()
            logger.info("Cleaned up old scans", cutoff_date=cutoff_date)
            
        except Exception as e:
            logger.error("Failed to cleanup old scans", error=str(e))