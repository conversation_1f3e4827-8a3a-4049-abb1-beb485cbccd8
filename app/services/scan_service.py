from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc
from sqlalchemy.orm import selectinload
from datetime import datetime

from app.models.scan import Scan, ScanResult, Vulnerability, ScanStatus, ScanType
from app.models.user import User
from app.schemas.scan import ScanCreate, ScanUpdate, VulnerabilitySummary, ScanSummary
from app.services.tasks import run_scan_task
import structlog

logger = structlog.get_logger()


class ScanService:
    """Service class for managing security scans."""
    
    @staticmethod
    async def create_scan(
        db: AsyncSession,
        scan_data: ScanCreate,
        user: User
    ) -> Scan:
        """Create a new security scan."""
        
        # Create scan record
        db_scan = Scan(
            user_id=user.id,
            target_url=str(scan_data.target_url),
            scan_type=scan_data.scan_type,
            scan_name=scan_data.scan_name,
            scan_description=scan_data.scan_description,
            max_depth=scan_data.max_depth,
            timeout_seconds=scan_data.timeout_seconds,
            concurrent_requests=scan_data.concurrent_requests,
            custom_headers=scan_data.custom_headers,
            status=ScanStatus.PENDING
        )
        
        db.add(db_scan)
        await db.commit()
        await db.refresh(db_scan)
        
        # Start scan task asynchronously
        task = run_scan_task.delay(db_scan.id)
        
        logger.info("Scan created and queued", 
                   scan_id=db_scan.id, 
                   user_id=user.id,
                   task_id=task.id,
                   target_url=db_scan.target_url)
        
        return db_scan
    
    @staticmethod
    async def get_scan(
        db: AsyncSession,
        scan_id: int,
        user: User
    ) -> Optional[Scan]:
        """Get a specific scan by ID."""
        result = await db.execute(
            select(Scan)
            .options(
                selectinload(Scan.vulnerabilities),
                selectinload(Scan.results)
            )
            .where(
                and_(
                    Scan.id == scan_id,
                    Scan.user_id == user.id
                )
            )
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_user_scans(
        db: AsyncSession,
        user: User,
        skip: int = 0,
        limit: int = 100,
        status: Optional[ScanStatus] = None,
        scan_type: Optional[ScanType] = None
    ) -> List[Scan]:
        """Get scans for a specific user with optional filtering."""
        query = select(Scan).where(Scan.user_id == user.id)
        
        if status:
            query = query.where(Scan.status == status)
        
        if scan_type:
            query = query.where(Scan.scan_type == scan_type)
        
        query = query.order_by(desc(Scan.created_at)).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    @staticmethod
    async def update_scan(
        db: AsyncSession,
        scan_id: int,
        scan_update: ScanUpdate,
        user: User
    ) -> Optional[Scan]:
        """Update scan details."""
        scan = await ScanService.get_scan(db, scan_id, user)
        
        if not scan:
            return None
        
        # Only allow updates to certain fields
        update_data = scan_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            if field in ["scan_name", "scan_description"]:  # Only allow these fields to be updated
                setattr(scan, field, value)
        
        await db.commit()
        await db.refresh(scan)
        
        logger.info("Scan updated", scan_id=scan_id, user_id=user.id, updated_fields=list(update_data.keys()))
        
        return scan
    
    @staticmethod
    async def cancel_scan(
        db: AsyncSession,
        scan_id: int,
        user: User
    ) -> Optional[Scan]:
        """Cancel a running scan."""
        scan = await ScanService.get_scan(db, scan_id, user)
        
        if not scan:
            return None
        
        if scan.status not in [ScanStatus.PENDING, ScanStatus.RUNNING]:
            return scan  # Can't cancel completed/failed scans
        
        scan.status = ScanStatus.CANCELLED
        scan.completed_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(scan)
        
        logger.info("Scan cancelled", scan_id=scan_id, user_id=user.id)
        
        return scan
    
    @staticmethod
    async def delete_scan(
        db: AsyncSession,
        scan_id: int,
        user: User
    ) -> bool:
        """Delete a scan and all its results."""
        scan = await ScanService.get_scan(db, scan_id, user)
        
        if not scan:
            return False
        
        await db.delete(scan)
        await db.commit()
        
        logger.info("Scan deleted", scan_id=scan_id, user_id=user.id)
        
        return True
    
    @staticmethod
    async def get_scan_vulnerabilities(
        db: AsyncSession,
        scan_id: int,
        user: User,
        severity: Optional[str] = None,
        category: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Vulnerability]:
        """Get vulnerabilities for a specific scan."""
        # First verify user owns the scan
        scan = await ScanService.get_scan(db, scan_id, user)
        if not scan:
            return []
        
        query = select(Vulnerability).where(Vulnerability.scan_id == scan_id)
        
        if severity:
            query = query.where(Vulnerability.severity == severity)
        
        if category:
            query = query.where(Vulnerability.category == category)
        
        query = query.order_by(desc(Vulnerability.created_at)).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    @staticmethod
    async def get_vulnerability(
        db: AsyncSession,
        vulnerability_id: int,
        user: User
    ) -> Optional[Vulnerability]:
        """Get a specific vulnerability by ID."""
        result = await db.execute(
            select(Vulnerability)
            .join(Scan)
            .where(
                and_(
                    Vulnerability.id == vulnerability_id,
                    Scan.user_id == user.id
                )
            )
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def update_vulnerability(
        db: AsyncSession,
        vulnerability_id: int,
        vulnerability_update: Dict[str, Any],
        user: User
    ) -> Optional[Vulnerability]:
        """Update vulnerability status (mark as false positive, resolved, etc.)."""
        vulnerability = await ScanService.get_vulnerability(db, vulnerability_id, user)
        
        if not vulnerability:
            return None
        
        # Update allowed fields
        for field, value in vulnerability_update.items():
            if field in ["is_false_positive", "is_resolved", "resolution_notes"]:
                setattr(vulnerability, field, value)
        
        await db.commit()
        await db.refresh(vulnerability)
        
        logger.info("Vulnerability updated", 
                   vulnerability_id=vulnerability_id, 
                   user_id=user.id,
                   updates=vulnerability_update)
        
        return vulnerability
    
    @staticmethod
    async def get_user_scan_summary(
        db: AsyncSession,
        user: User
    ) -> ScanSummary:
        """Get scan summary for a user."""
        result = await db.execute(
            select(Scan).where(Scan.user_id == user.id)
        )
        scans = result.scalars().all()
        
        summary = ScanSummary(
            total_scans=len(scans),
            pending_scans=len([s for s in scans if s.status == ScanStatus.PENDING]),
            running_scans=len([s for s in scans if s.status == ScanStatus.RUNNING]),
            completed_scans=len([s for s in scans if s.status == ScanStatus.COMPLETED]),
            failed_scans=len([s for s in scans if s.status == ScanStatus.FAILED])
        )
        
        return summary
    
    @staticmethod
    async def get_user_vulnerability_summary(
        db: AsyncSession,
        user: User
    ) -> VulnerabilitySummary:
        """Get vulnerability summary for a user."""
        result = await db.execute(
            select(Vulnerability)
            .join(Scan)
            .where(Scan.user_id == user.id)
        )
        vulnerabilities = result.scalars().all()
        
        # Count by severity
        severity_counts = {
            "critical": len([v for v in vulnerabilities if v.severity == "critical"]),
            "high": len([v for v in vulnerabilities if v.severity == "high"]),
            "medium": len([v for v in vulnerabilities if v.severity == "medium"]),
            "low": len([v for v in vulnerabilities if v.severity == "low"])
        }
        
        # Count by category
        category_counts = {}
        for vuln in vulnerabilities:
            category = vuln.category
            category_counts[category] = category_counts.get(category, 0) + 1
        
        summary = VulnerabilitySummary(
            total_vulnerabilities=len(vulnerabilities),
            critical_count=severity_counts["critical"],
            high_count=severity_counts["high"],
            medium_count=severity_counts["medium"],
            low_count=severity_counts["low"],
            by_category=category_counts
        )
        
        return summary