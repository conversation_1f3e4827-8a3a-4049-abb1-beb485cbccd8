from celery import Celery
from app.core.config import settings

celery_app = Celery(
    "viberush_scanner",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=["app.services.tasks"]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_send_sent_event=True,
    task_publish_retry=True,
    task_publish_retry_policy={
        'max_retries': 3,
        'interval_start': 0,
        'interval_step': 0.2,
        'interval_max': 0.2,
    },
    task_time_limit=settings.SCAN_TIMEOUT_SECONDS,
    task_soft_time_limit=settings.SCAN_TIMEOUT_SECONDS - 30,
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    worker_send_task_events=True,
    worker_enable_remote_control=True,
    result_expires=3600,  # Results expire after 1 hour
    worker_pool="threads",  # Use thread pool for better async support
    worker_concurrency=4,   # Number of concurrent threads
    task_routes={
        "run_scan_task": {"queue": "scans"},
        "cleanup_old_scans": {"queue": "maintenance"},
    }
)