from __future__ import annotations
from sqlalchemy import <PERSON>umn, Inte<PERSON>, <PERSON>, <PERSON>olean, DateTime, Text, ForeignKey, func, Enum
from sqlalchemy.orm import relationship
from app.core.database import Base
import enum


class NotificationType(str, enum.Enum):
    SCAN_COMPLETED = "scan_completed"
    SCAN_FAILED = "scan_failed"
    VULNERABILITY_FOUND = "vulnerability_found"
    HIGH_PRIORITY_VULNERABILITY = "high_priority_vulnerability"
    WEEKLY_REPORT = "weekly_report"
    SYSTEM_ALERT = "system_alert"


class Notification(Base):
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Notification Details
    type = Column(Enum(NotificationType), nullable=False)
    title = Column(String, nullable=False)
    message = Column(Text, nullable=False)
    
    # Optional Reference Data
    scan_id = Column(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("scans.id"), nullable=True)
    vulnerability_id = Column(Integer, ForeignKey("vulnerabilities.id"), nullable=True)
    
    # Status
    is_read = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    read_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="notifications")
    scan = relationship("Scan", back_populates="notifications")
    vulnerability = relationship("Vulnerability", back_populates="notifications")