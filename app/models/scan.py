from sqlalchemy import Column, Integer, String, DateTime, Text, Foreign<PERSON>ey, Boolean, JSON, Enum, func
from sqlalchemy.orm import relationship
from app.core.database import Base
import enum


class ScanStatus(str, enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ScanType(str, enum.Enum):
    API_ENDPOINTS = "api_endpoints"
    DATABASE_SECURITY = "database_security"
    SECRETS = "secrets"
    FIREBASE = "firebase"
    WEBHOOKS = "webhooks"
    COMPREHENSIVE = "comprehensive"


class VulnerabilitySeverity(str, enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class BusinessPriority(str, enum.Enum):
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"



class Scan(Base):
    __tablename__ = "scans"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Scan Configuration
    target_url = Column(String, nullable=False)
    scan_type = Column(Enum(ScanType), nullable=False)
    scan_name = Column(String)
    scan_description = Column(Text)
    
    # Scan Status
    status = Column(Enum(ScanStatus), default=ScanStatus.PENDING)
    progress_percentage = Column(Integer, default=0)
    error_message = Column(Text)
    
    # Scan Settings
    max_depth = Column(Integer, default=3)
    timeout_seconds = Column(Integer, default=300)
    concurrent_requests = Column(Integer, default=10)
    custom_headers = Column(JSON)  # {"User-Agent": "Custom", "Authorization": "Bearer token"}
    
    # Results Summary
    total_vulnerabilities = Column(Integer, default=0)
    critical_count = Column(Integer, default=0)
    high_count = Column(Integer, default=0)
    medium_count = Column(Integer, default=0)
    low_count = Column(Integer, default=0)
    security_grade = Column(String)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User", back_populates="scans")
    results = relationship("ScanResult", back_populates="scan", cascade="all, delete-orphan")
    vulnerabilities = relationship("Vulnerability", back_populates="scan", cascade="all, delete-orphan")
    notifications = relationship("Notification", back_populates="scan", cascade="all, delete-orphan")


class ScanResult(Base):
    __tablename__ = "scan_results"

    id = Column(Integer, primary_key=True, index=True)
    scan_id = Column(Integer, ForeignKey("scans.id"), nullable=False)
    
    # Result Data
    scanner_name = Column(String, nullable=False)  # "api_endpoint_scanner", "secrets_scanner", etc.
    raw_data = Column(JSON)  # Full scanner output
    processed_data = Column(JSON)  # Cleaned/formatted data
    scan_metadata = Column(JSON)  # Scanner-specific metadata
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    scan = relationship("Scan", back_populates="results")


class Vulnerability(Base):
    __tablename__ = "vulnerabilities"

    id = Column(Integer, primary_key=True, index=True)
    scan_id = Column(Integer, ForeignKey("scans.id"), nullable=False)
    
    # Vulnerability Details
    title = Column(String, nullable=False)
    description = Column(Text, nullable=False)
    severity = Column(Enum(VulnerabilitySeverity), nullable=False)
    category = Column(String, nullable=False)  # "Authentication", "SQL Injection", "XSS", etc.
    
    # Location Information
    url = Column(String)
    method = Column(String)  # HTTP method if applicable
    parameter = Column(String)  # Vulnerable parameter
    payload = Column(Text)  # Exploit payload used
    evidence = Column(Text)  # Evidence of the vulnerability
    
    # Risk Assessment
    cvss_score = Column(String)  # CVSS 3.1 score
    cwe_id = Column(String)  # CWE identifier
    owasp_category = Column(String)  # OWASP Top 10 category
    
    # Remediation
    remediation = Column(Text)  # How to fix
    references = Column(JSON)  # List of reference URLs
    
    # Business Importance & Scoring
    business_priority = Column(Enum(BusinessPriority), default=BusinessPriority.MEDIUM)
    endpoint_score = Column(Integer, default=50)  # 0-100 score based on business importance
    is_likely_false_positive = Column(Boolean, default=False)  # ML-based prediction
    false_positive_confidence = Column(Integer, default=0)  # 0-100 confidence score
    
    # Status
    is_false_positive = Column(Boolean, default=False)
    is_resolved = Column(Boolean, default=False)
    resolution_notes = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    scan = relationship("Scan", back_populates="vulnerabilities")
    notifications = relationship("Notification", back_populates="vulnerability", cascade="all, delete-orphan")