from fastapi import APIRouter

from app.api.v1.endpoints import auth, scans, users, health, notifications

api_router = APIRouter()

api_router.include_router(health.router, prefix="/health", tags=["health"])
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(scans.router, prefix="/scans", tags=["scans"])
api_router.include_router(notifications.router, prefix="/notifications", tags=["notifications"])