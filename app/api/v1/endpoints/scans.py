from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.deps import get_current_user, check_rate_limit, check_scan_limit
from app.models.user import User
from app.models.scan import Scan<PERSON>tat<PERSON>, ScanType
from app.schemas.scan import (
    Scan, ScanCreate, ScanUpdate, ScanSummary,
    Vulnerability, VulnerabilityUpdate, VulnerabilitySummary
)
from app.services.scan_service import ScanService
import structlog

router = APIRouter()
logger = structlog.get_logger()


@router.post("/", response_model=Scan, dependencies=[Depends(check_rate_limit), Depends(check_scan_limit)])
async def create_scan(
    scan_data: ScanCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new security scan."""
    try:
        scan = await ScanService.create_scan(db, scan_data, current_user)
        logger.info("Scan created via API", scan_id=scan.id, user_id=current_user.id)
        return scan
    except Exception as e:
        logger.error("Failed to create scan", user_id=current_user.id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create scan"
        )


@router.get("/", response_model=List[Scan])
async def get_scans(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    status_filter: Optional[ScanStatus] = Query(None, alias="status"),
    scan_type: Optional[ScanType] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user's scans with optional filtering."""
    scans = await ScanService.get_user_scans(
        db, current_user, skip, limit, status_filter, scan_type
    )
    return scans


@router.get("/summary", response_model=ScanSummary)
async def get_scan_summary(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get scan summary for current user."""
    summary = await ScanService.get_user_scan_summary(db, current_user)
    return summary


@router.get("/vulnerabilities/summary", response_model=VulnerabilitySummary)
async def get_vulnerability_summary(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get vulnerability summary for current user."""
    summary = await ScanService.get_user_vulnerability_summary(db, current_user)
    return summary


@router.get("/{scan_id}", response_model=Scan)
async def get_scan(
    scan_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific scan by ID."""
    scan = await ScanService.get_scan(db, scan_id, current_user)
    
    if not scan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scan not found"
        )
    
    return scan


@router.patch("/{scan_id}", response_model=Scan)
async def update_scan(
    scan_id: int,
    scan_update: ScanUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update scan details."""
    scan = await ScanService.update_scan(db, scan_id, scan_update, current_user)
    
    if not scan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scan not found"
        )
    
    return scan


@router.post("/{scan_id}/cancel", response_model=Scan)
async def cancel_scan(
    scan_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Cancel a running scan."""
    scan = await ScanService.cancel_scan(db, scan_id, current_user)
    
    if not scan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scan not found"
        )
    
    return scan


@router.delete("/{scan_id}")
async def delete_scan(
    scan_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a scan and all its results."""
    success = await ScanService.delete_scan(db, scan_id, current_user)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scan not found"
        )
    
    return {"message": "Scan deleted successfully"}


@router.get("/{scan_id}/vulnerabilities", response_model=List[Vulnerability])
async def get_scan_vulnerabilities(
    scan_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    severity: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get vulnerabilities for a specific scan."""
    vulnerabilities = await ScanService.get_scan_vulnerabilities(
        db, scan_id, current_user, severity, category, skip, limit
    )
    return vulnerabilities


@router.get("/vulnerabilities/{vulnerability_id}", response_model=Vulnerability)
async def get_vulnerability(
    vulnerability_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific vulnerability by ID."""
    vulnerability = await ScanService.get_vulnerability(db, vulnerability_id, current_user)
    
    if not vulnerability:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vulnerability not found"
        )
    
    return vulnerability


@router.patch("/vulnerabilities/{vulnerability_id}", response_model=Vulnerability)
async def update_vulnerability(
    vulnerability_id: int,
    vulnerability_update: VulnerabilityUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update vulnerability status (mark as false positive, resolved, etc.)."""
    update_data = vulnerability_update.model_dump(exclude_unset=True)
    
    vulnerability = await ScanService.update_vulnerability(
        db, vulnerability_id, update_data, current_user
    )
    
    if not vulnerability:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vulnerability not found"
        )
    
    return vulnerability


@router.get("/{scan_id}/status")
async def get_scan_status(
    scan_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get current scan status and progress."""
    scan = await ScanService.get_scan(db, scan_id, current_user)
    
    if not scan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scan not found"
        )
    
    return {
        "scan_id": scan.id,
        "status": scan.status,
        "progress_percentage": scan.progress_percentage,
        "error_message": scan.error_message,
        "created_at": scan.created_at,
        "started_at": scan.started_at,
        "completed_at": scan.completed_at
    }


@router.get("/{scan_id}/report")
async def get_scan_report(
    scan_id: int,
    format: str = Query("json", regex="^(json|pdf|html)$"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get scan report in various formats."""
    scan = await ScanService.get_scan(db, scan_id, current_user)
    
    if not scan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scan not found"
        )
    
    if scan.status != ScanStatus.COMPLETED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Scan is not completed yet"
        )
    
    # Get vulnerabilities for the report
    vulnerabilities = await ScanService.get_scan_vulnerabilities(
        db, scan_id, current_user, limit=1000  # Get all vulnerabilities
    )
    
    # Get scan results for metadata
    scan_results = []
    metadata = {}
    if scan.results:
        for result in scan.results:
            scan_results.append({
                "scanner_name": result.scanner_name,
                "metadata": result.scan_metadata or {},
                "raw_data": result.raw_data or {},
                "processed_data": result.processed_data or {}
            })
            # Use the first result's metadata as the main metadata
            if not metadata and result.scan_metadata:
                metadata = result.scan_metadata

    # Determine if this is a clean report (no vulnerabilities)
    is_clean_report = len(vulnerabilities) == 0

    report_data = {
        "scan": {
            "id": scan.id,
            "name": scan.scan_name,
            "description": scan.scan_description,
            "target_url": scan.target_url,
            "scan_type": scan.scan_type,
            "status": scan.status,
            "created_at": scan.created_at.isoformat(),
            "started_at": scan.started_at.isoformat() if scan.started_at else None,
            "completed_at": scan.completed_at.isoformat() if scan.completed_at else None,
            "total_vulnerabilities": scan.total_vulnerabilities,
            "vulnerability_counts": {
                "critical": scan.critical_count,
                "high": scan.high_count,
                "medium": scan.medium_count,
                "low": scan.low_count
            },
            "max_depth": scan.max_depth,
            "concurrent_requests": scan.concurrent_requests,
            "timeout_seconds": scan.timeout_seconds
        },
        "metadata": metadata,
        "scan_results": scan_results,
        "is_clean_report": is_clean_report,
        "security_summary": {
            "endpoints_tested": metadata.get("total_endpoints_tested", metadata.get("discovered_endpoints", 0)),
            "scan_methods": metadata.get("scan_methods", []),
            "cloud_services_detected": metadata.get("cloud_services_found", len(metadata.get("detected_services", []))),
            "detected_services": metadata.get("detected_services", []),
            "filtering_accuracy": metadata.get("filtering_accuracy", 0),
            "valid_endpoints": metadata.get("valid_endpoints", 0),
            "tests_performed": metadata.get("tests_performed", [])
        } if is_clean_report else None,
        "vulnerabilities": [
            {
                "id": vuln.id,
                "title": vuln.title,
                "description": vuln.description,
                "severity": vuln.severity,
                "category": vuln.category,
                "url": vuln.url,
                "method": vuln.method,
                "parameter": vuln.parameter,
                "evidence": vuln.evidence,
                "remediation": vuln.remediation,
                "cwe_id": vuln.cwe_id,
                "owasp_category": vuln.owasp_category,
                "references": vuln.references,
                "is_false_positive": vuln.is_false_positive,
                "is_resolved": vuln.is_resolved
            }
            for vuln in vulnerabilities
        ]
    }
    
    if format == "json":
        return report_data
    elif format == "pdf":
        # TODO: Implement PDF generation
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="PDF format not yet implemented"
        )
    elif format == "html":
        # TODO: Implement HTML generation
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="HTML format not yet implemented"
        )


@router.post("/{scan_id}/retry", response_model=Scan, dependencies=[Depends(check_rate_limit), Depends(check_scan_limit)])
async def retry_scan(
    scan_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Retry a failed scan."""
    scan = await ScanService.get_scan(db, scan_id, current_user)
    
    if not scan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scan not found"
        )
    
    if scan.status not in [ScanStatus.FAILED, ScanStatus.CANCELLED]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only retry failed or cancelled scans"
        )
    
    # Create a new scan with the same parameters
    from app.schemas.scan import ScanCreate
    
    retry_scan_data = ScanCreate(
        target_url=scan.target_url,
        scan_type=scan.scan_type,
        scan_name=f"{scan.scan_name} (Retry)",
        scan_description=scan.scan_description,
        max_depth=scan.max_depth,
        timeout_seconds=scan.timeout_seconds,
        concurrent_requests=scan.concurrent_requests,
        custom_headers=scan.custom_headers
    )
    
    new_scan = await ScanService.create_scan(db, retry_scan_data, current_user)
    logger.info("Scan retried", original_scan_id=scan_id, new_scan_id=new_scan.id, user_id=current_user.id)
    
    return new_scan