from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, desc, func
from sqlalchemy.orm import selectinload
from app.core.database import get_db
from app.core.deps import get_current_user
from app.models.user import User
from app.models.notification import Notification, NotificationType
from app.schemas.notification import (
    Notification as NotificationSchema, 
    NotificationCreate, 
    NotificationUpdate,
    NotificationMarkRead
)
import structlog
from datetime import datetime

router = APIRouter()
logger = structlog.get_logger()


@router.get("/", response_model=List[NotificationSchema])
async def get_notifications(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    unread_only: bool = Query(False),
    notification_type: NotificationType = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user notifications."""
    query = select(Notification).where(Notification.user_id == current_user.id)
    
    if unread_only:
        query = query.where(Notification.is_read == False)
    
    if notification_type:
        query = query.where(Notification.type == notification_type)
    
    query = query.order_by(desc(Notification.created_at)).offset(skip).limit(limit)
    
    result = await db.execute(query)
    notifications = result.scalars().all()
    
    return notifications


@router.get("/unread-count")
async def get_unread_count(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get count of unread notifications."""
    result = await db.execute(
        select(func.count(Notification.id)).where(
            and_(
                Notification.user_id == current_user.id,
                Notification.is_read == False
            )
        )
    )
    unread_count = result.scalar()
    
    return {"unread_count": unread_count}


@router.patch("/{notification_id}/read")
async def mark_notification_read(
    notification_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Mark a notification as read."""
    result = await db.execute(
        select(Notification).where(
            and_(
                Notification.id == notification_id,
                Notification.user_id == current_user.id
            )
        )
    )
    notification = result.scalar_one_or_none()
    
    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notification not found"
        )
    
    notification.is_read = True
    notification.read_at = datetime.utcnow()
    await db.commit()
    
    logger.info("Notification marked as read", notification_id=notification_id, user_id=current_user.id)
    return {"message": "Notification marked as read"}


@router.patch("/mark-read")
async def mark_notifications_read(
    mark_read_data: NotificationMarkRead,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Mark multiple notifications as read."""
    await db.execute(
        update(Notification).where(
            and_(
                Notification.id.in_(mark_read_data.notification_ids),
                Notification.user_id == current_user.id
            )
        ).values(
            is_read=True,
            read_at=datetime.utcnow()
        )
    )
    await db.commit()
    
    logger.info("Multiple notifications marked as read", 
               notification_ids=mark_read_data.notification_ids, 
               user_id=current_user.id)
    return {"message": f"Marked {len(mark_read_data.notification_ids)} notifications as read"}


@router.patch("/mark-all-read")
async def mark_all_read(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Mark all notifications as read."""
    result = await db.execute(
        update(Notification).where(
            and_(
                Notification.user_id == current_user.id,
                Notification.is_read == False
            )
        ).values(
            is_read=True,
            read_at=datetime.utcnow()
        )
    )
    await db.commit()
    
    marked_count = result.rowcount
    logger.info("All notifications marked as read", marked_count=marked_count, user_id=current_user.id)
    return {"message": f"Marked {marked_count} notifications as read"}


@router.delete("/{notification_id}")
async def delete_notification(
    notification_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a notification."""
    result = await db.execute(
        select(Notification).where(
            and_(
                Notification.id == notification_id,
                Notification.user_id == current_user.id
            )
        )
    )
    notification = result.scalar_one_or_none()
    
    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notification not found"
        )
    
    await db.delete(notification)
    await db.commit()
    
    logger.info("Notification deleted", notification_id=notification_id, user_id=current_user.id)
    return {"message": "Notification deleted"}


# Helper function to create notifications (to be used by other services)
async def create_notification(
    db: AsyncSession,
    user_id: int,
    notification_type: NotificationType,
    title: str,
    message: str,
    scan_id: int = None,
    vulnerability_id: int = None
) -> Notification:
    """Create a new notification."""
    notification = Notification(
        user_id=user_id,
        type=notification_type,
        title=title,
        message=message,
        scan_id=scan_id,
        vulnerability_id=vulnerability_id
    )
    
    db.add(notification)
    await db.commit()
    await db.refresh(notification)
    
    logger.info("Notification created", 
               notification_id=notification.id,
               user_id=user_id, 
               type=notification_type)
    
    return notification