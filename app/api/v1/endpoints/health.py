from fastapi import APIRouter
from app.core.monitoring import system_monitor, metrics_collector
import structlog

router = APIRouter()
logger = structlog.get_logger()


@router.get("/")
async def health_check():
    """Basic health check endpoint."""
    return {"status": "healthy", "service": "MostlySecure Security Scanner"}


@router.get("/detailed")
async def detailed_health_check():
    """Detailed health check with comprehensive system monitoring."""
    return await system_monitor.get_system_health()


@router.get("/metrics")
async def get_metrics(hours: int = 24):
    """Get application metrics for the specified time period."""
    return await metrics_collector.get_metrics_summary(hours=hours)