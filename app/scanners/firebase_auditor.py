import asyncio
import re
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
import time
import httpx
from urllib.parse import urljoin, urlparse
from .base_scanner import BaseScanner, ScannerResult


class FirebaseAuditor(BaseScanner):
    """Scanner for Firebase security misconfigurations."""
    
    def __init__(self, timeout: int = 300, concurrent_requests: int = 10):
        super().__init__(timeout, concurrent_requests)
        
        # Firebase project patterns
        self.firebase_patterns = {
            'config_object': r'firebase\.initializeApp\s*\(\s*({[^}]+})\s*\)',
            'project_id': r'["\']projectId["\']\s*:\s*["\']([^"\']+)["\']',
            'api_key': r'["\']apiKey["\']\s*:\s*["\']([^"\']+)["\']',
            'database_url': r'["\']databaseURL["\']\s*:\s*["\']([^"\']+)["\']',
            'storage_bucket': r'["\']storageBucket["\']\s*:\s*["\']([^"\']+)["\']',
            'messaging_sender_id': r'["\']messagingSenderId["\']\s*:\s*["\']([^"\']+)["\']',
            'app_id': r'["\']appId["\']\s*:\s*["\']([^"\']+)["\']'
        }
        
        # Dangerous Firestore rules patterns
        self.dangerous_rules_patterns = [
            r'allow\s+read,?\s*write\s*:\s*if\s+true',  # Allow all access
            r'allow\s+read\s*:\s*if\s+true',  # Allow read to all
            r'allow\s+write\s*:\s*if\s+true',  # Allow write to all
            r'allow\s+read,?\s*write\s*;\s*$',  # No conditions
            r'allow\s+read\s*;\s*$',  # No conditions for read
            r'allow\s+write\s*;\s*$'  # No conditions for write
        ]

    async def scan(self, target_url: str, **kwargs) -> ScannerResult:
        """Main Firebase security audit method."""
        start_time = time.time()
        vulnerabilities = []
        
        try:
            async with httpx.AsyncClient(
                timeout=self.timeout,
                limits=httpx.Limits(max_connections=self.concurrent_requests),
                verify=False
            ) as client:
                
                # Step 1: Discover Firebase configuration
                firebase_config = await self._discover_firebase_config(client, target_url)
                
                if not firebase_config:
                    return ScannerResult(
                        vulnerabilities=[],
                        metadata={"error": "No Firebase configuration found"},
                        scan_duration=time.time() - start_time,
                        timestamp=datetime.utcnow(),
                        scanner_name="firebase_auditor",
                        target_url=target_url,
                        success=False,
                        error_message="No Firebase configuration detected"
                    )
                
                # Step 2: Test Firestore database security
                firestore_vulns = await self._test_firestore_security(client, firebase_config)
                vulnerabilities.extend(firestore_vulns)
                
                # Step 3: Test Realtime Database security
                realtime_vulns = await self._test_realtime_database_security(client, firebase_config)
                vulnerabilities.extend(realtime_vulns)
                
                # Step 4: Test Firebase Storage security
                storage_vulns = await self._test_storage_security(client, firebase_config)
                vulnerabilities.extend(storage_vulns)
                
                # Step 5: Test Firebase Authentication security
                auth_vulns = await self._test_auth_security(client, firebase_config)
                vulnerabilities.extend(auth_vulns)
                
                # Step 6: Test Cloud Functions security
                functions_vulns = await self._test_cloud_functions_security(client, firebase_config)
                vulnerabilities.extend(functions_vulns)
                
                # Step 7: Check for exposed Firebase configuration
                config_vulns = await self._check_exposed_config(firebase_config, target_url)
                vulnerabilities.extend(config_vulns)
            
            scan_duration = time.time() - start_time
            
            return ScannerResult(
                vulnerabilities=vulnerabilities,
                metadata={
                    "firebase_config": firebase_config,
                    "tests_performed": [
                        "firestore_security",
                        "realtime_database_security", 
                        "storage_security",
                        "auth_security",
                        "cloud_functions_security",
                        "config_exposure"
                    ]
                },
                scan_duration=scan_duration,
                timestamp=datetime.utcnow(),
                scanner_name="firebase_auditor",
                target_url=target_url,
                success=True
            )
            
        except Exception as e:
            scan_duration = time.time() - start_time
            self.logger.error("Firebase audit failed", error=str(e))
            
            return ScannerResult(
                vulnerabilities=[],
                metadata={},
                scan_duration=scan_duration,
                timestamp=datetime.utcnow(),
                scanner_name="firebase_auditor",
                target_url=target_url,
                success=False,
                error_message=str(e)
            )

    async def _discover_firebase_config(self, client: httpx.AsyncClient, target_url: str) -> Optional[Dict[str, Any]]:
        """Discover Firebase configuration from web application."""
        config = {}
        
        try:
            # Get main page
            response = await client.get(target_url)
            
            if response.status_code == 200:
                page_content = response.text
                
                # Extract Firebase configuration
                for pattern_name, pattern in self.firebase_patterns.items():
                    matches = re.search(pattern, page_content, re.IGNORECASE)
                    if matches:
                        if pattern_name == 'config_object':
                            # Try to parse the entire config object
                            try:
                                config_str = matches.group(1)
                                # Clean up the config string for JSON parsing
                                config_str = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', config_str)
                                firebase_config = json.loads(config_str)
                                config.update(firebase_config)
                            except Exception:
                                pass
                        else:
                            config[pattern_name] = matches.group(1)
                
                # Also check for Firebase scripts
                firebase_scripts = re.findall(r'<script[^>]*src=[\'"]([^\'"]*firebase[^\'"]*)[\'"]', page_content)
                if firebase_scripts:
                    config['firebase_scripts'] = firebase_scripts
                
                # Look for __FIREBASE_DEFAULTS__ or similar
                firebase_defaults = re.search(r'__FIREBASE_DEFAULTS__\s*=\s*({[^}]+})', page_content)
                if firebase_defaults:
                    try:
                        defaults = json.loads(firebase_defaults.group(1))
                        config.update(defaults)
                    except Exception:
                        pass
        
        except Exception as e:
            self.logger.debug("Firebase config discovery failed", error=str(e))
        
        return config if config else None

    async def _test_firestore_security(self, client: httpx.AsyncClient, firebase_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Test Firestore database security rules."""
        vulnerabilities = []
        
        project_id = firebase_config.get('projectId') or firebase_config.get('project_id')
        if not project_id:
            return vulnerabilities
        
        try:
            # Test if Firestore is publicly readable
            firestore_url = f"https://firestore.googleapis.com/v1/projects/{project_id}/databases/(default)/documents"
            
            response = await client.get(firestore_url)
            
            if response.status_code == 200:
                # Firestore is publicly readable
                vuln = self.create_vulnerability(
                    title="Firestore Database Publicly Readable",
                    description="Firestore database allows public read access without authentication",
                    severity="critical",
                    category="Access Control",
                    url=firestore_url,
                    evidence=f"HTTP {response.status_code}: {response.text[:200]}",
                    cwe_id="CWE-284",
                    owasp_category="A01:2021 – Broken Access Control",
                    remediation="Configure proper Firestore security rules to restrict access",
                    references=["https://firebase.google.com/docs/firestore/security/get-started"]
                )
                vulnerabilities.append(vuln)
                self.log_vulnerability(vuln)
                
                # Try to read specific collections
                collections_to_test = ['users', 'admin', 'private', 'config', 'settings']
                for collection in collections_to_test:
                    collection_url = f"{firestore_url}/{collection}"
                    coll_response = await client.get(collection_url)
                    
                    if coll_response.status_code == 200:
                        vuln = self.create_vulnerability(
                            title=f"Sensitive Firestore Collection Exposed: {collection}",
                            description=f"Firestore collection '{collection}' is publicly accessible",
                            severity="high" if collection in ['admin', 'private', 'config'] else "medium",
                            category="Access Control",
                            url=collection_url,
                            evidence=f"Collection data: {coll_response.text[:200]}",
                            cwe_id="CWE-284",
                            remediation=f"Restrict access to sensitive collection '{collection}'"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)
            
            # Test if Firestore is publicly writable
            test_doc_url = f"{firestore_url}/test_security_scan"
            test_data = {"fields": {"test": {"stringValue": "security_scan"}}}
            
            write_response = await client.patch(test_doc_url, json=test_data)
            
            if write_response.status_code in [200, 201]:
                vuln = self.create_vulnerability(
                    title="Firestore Database Publicly Writable",
                    description="Firestore database allows public write access without authentication",
                    severity="critical",
                    category="Access Control",
                    url=firestore_url,
                    evidence=f"Successfully wrote test document: {test_doc_url}",
                    cwe_id="CWE-284",
                    owasp_category="A01:2021 – Broken Access Control",
                    remediation="Configure proper Firestore security rules to restrict write access"
                )
                vulnerabilities.append(vuln)
                self.log_vulnerability(vuln)
                
                # Clean up test document
                try:
                    await client.delete(test_doc_url)
                except Exception:
                    pass
        
        except Exception as e:
            self.logger.debug("Firestore security test failed", error=str(e))
        
        return vulnerabilities

    async def _test_realtime_database_security(self, client: httpx.AsyncClient, firebase_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Test Firebase Realtime Database security."""
        vulnerabilities = []
        
        database_url = firebase_config.get('databaseURL') or firebase_config.get('database_url')
        if not database_url:
            return vulnerabilities
        
        try:
            # Test if database is publicly readable
            read_url = f"{database_url.rstrip('/')}.json"
            response = await client.get(read_url)
            
            if response.status_code == 200:
                vuln = self.create_vulnerability(
                    title="Firebase Realtime Database Publicly Readable",
                    description="Firebase Realtime Database allows public read access",
                    severity="critical",
                    category="Access Control",
                    url=read_url,
                    evidence=f"Database content: {response.text[:200]}",
                    cwe_id="CWE-284",
                    owasp_category="A01:2021 – Broken Access Control",
                    remediation="Configure proper database security rules",
                    references=["https://firebase.google.com/docs/database/security"]
                )
                vulnerabilities.append(vuln)
                self.log_vulnerability(vuln)
                
                # Test sensitive paths
                sensitive_paths = ['users', 'admin', 'private', 'config', 'keys', 'secrets']
                for path in sensitive_paths:
                    path_url = f"{database_url.rstrip('/')}/{path}.json"
                    path_response = await client.get(path_url)
                    
                    if path_response.status_code == 200 and path_response.text not in ['null', '']:
                        vuln = self.create_vulnerability(
                            title=f"Sensitive Realtime Database Path Exposed: {path}",
                            description=f"Sensitive database path '/{path}' is publicly accessible",
                            severity="high",
                            category="Access Control", 
                            url=path_url,
                            evidence=f"Path data: {path_response.text[:200]}",
                            cwe_id="CWE-284",
                            remediation=f"Restrict access to sensitive path '/{path}'"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)
            
            # Test if database is publicly writable
            test_path = f"{database_url.rstrip('/')}/security_test.json"
            test_data = {"test": "security_scan", "timestamp": int(time.time())}
            
            write_response = await client.put(test_path, json=test_data)
            
            if write_response.status_code == 200:
                vuln = self.create_vulnerability(
                    title="Firebase Realtime Database Publicly Writable",
                    description="Firebase Realtime Database allows public write access",
                    severity="critical",
                    category="Access Control",
                    url=database_url,
                    evidence=f"Successfully wrote to: {test_path}",
                    cwe_id="CWE-284",
                    remediation="Configure proper database security rules to restrict write access"
                )
                vulnerabilities.append(vuln)
                self.log_vulnerability(vuln)
                
                # Clean up test data
                try:
                    await client.delete(test_path)
                except Exception:
                    pass
        
        except Exception as e:
            self.logger.debug("Realtime database test failed", error=str(e))
        
        return vulnerabilities

    async def _test_storage_security(self, client: httpx.AsyncClient, firebase_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Test Firebase Storage security."""
        vulnerabilities = []
        
        storage_bucket = firebase_config.get('storageBucket') or firebase_config.get('storage_bucket')
        if not storage_bucket:
            return vulnerabilities
        
        try:
            # Test if storage bucket is publicly listable
            storage_url = f"https://firebasestorage.googleapis.com/v0/b/{storage_bucket}/o"
            
            response = await client.get(storage_url)
            
            if response.status_code == 200:
                try:
                    storage_data = response.json()
                    if 'items' in storage_data and storage_data['items']:
                        vuln = self.create_vulnerability(
                            title="Firebase Storage Bucket Publicly Listable",
                            description="Firebase Storage bucket allows public listing of files",
                            severity="medium",
                            category="Information Disclosure",
                            url=storage_url,
                            evidence=f"Found {len(storage_data['items'])} items in bucket",
                            cwe_id="CWE-200",
                            remediation="Configure proper Storage security rules to restrict listing"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)
                        
                        # Check individual files for public access
                        for item in storage_data['items'][:5]:  # Check first 5 items
                            file_name = item.get('name', '')
                            if file_name:
                                file_url = f"https://firebasestorage.googleapis.com/v0/b/{storage_bucket}/o/{file_name}?alt=media"
                                file_response = await client.head(file_url)
                                
                                if file_response.status_code == 200:
                                    vuln = self.create_vulnerability(
                                        title=f"Firebase Storage File Publicly Accessible: {file_name}",
                                        description=f"Storage file '{file_name}' is publicly downloadable",
                                        severity="medium",
                                        category="Access Control",
                                        url=file_url,
                                        evidence=f"File accessible without authentication",
                                        cwe_id="CWE-284",
                                        remediation="Configure proper Storage security rules"
                                    )
                                    vulnerabilities.append(vuln)
                                    self.log_vulnerability(vuln)
                except Exception:
                    pass
        
        except Exception as e:
            self.logger.debug("Storage security test failed", error=str(e))
        
        return vulnerabilities

    async def _test_auth_security(self, client: httpx.AsyncClient, firebase_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Test Firebase Authentication security."""
        vulnerabilities = []
        
        api_key = firebase_config.get('apiKey') or firebase_config.get('api_key')
        project_id = firebase_config.get('projectId') or firebase_config.get('project_id')
        
        if not api_key or not project_id:
            return vulnerabilities
        
        try:
            # Test if anonymous authentication is enabled
            auth_url = f"https://identitytoolkit.googleapis.com/v1/accounts:signUp?key={api_key}"
            
            # Try anonymous sign up
            anonymous_data = {"returnSecureToken": True}
            response = await client.post(auth_url, json=anonymous_data)
            
            if response.status_code == 200:
                try:
                    auth_data = response.json()
                    if 'idToken' in auth_data:
                        vuln = self.create_vulnerability(
                            title="Firebase Anonymous Authentication Enabled",
                            description="Firebase allows anonymous user creation without any verification",
                            severity="medium",
                            category="Authentication",
                            url=auth_url,
                            evidence="Successfully created anonymous user account",
                            cwe_id="CWE-287",
                            remediation="Disable anonymous authentication if not required"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)
                except Exception:
                    pass
            
            # Test user enumeration
            enum_url = f"https://identitytoolkit.googleapis.com/v1/accounts:createAuthUri?key={api_key}"
            
            test_emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
            for email in test_emails:
                enum_data = {"identifier": email, "continueUri": "http://localhost"}
                enum_response = await client.post(enum_url, json=enum_data)
                
                if enum_response.status_code == 200:
                    try:
                        enum_result = enum_response.json()
                        if 'registered' in enum_result:
                            vuln = self.create_vulnerability(
                                title="Firebase User Enumeration Possible",
                                description="Firebase API allows enumeration of registered email addresses",
                                severity="low",
                                category="Information Disclosure",
                                url=enum_url,
                                evidence=f"Can determine if {email} is registered: {enum_result.get('registered')}",
                                cwe_id="CWE-200",
                                remediation="Consider implementing rate limiting on authentication endpoints"
                            )
                            vulnerabilities.append(vuln)
                            self.log_vulnerability(vuln)
                            break  # Only report once
                    except Exception:
                        pass
        
        except Exception as e:
            self.logger.debug("Auth security test failed", error=str(e))
        
        return vulnerabilities

    async def _test_cloud_functions_security(self, client: httpx.AsyncClient, firebase_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Test Firebase Cloud Functions security."""
        vulnerabilities = []
        
        project_id = firebase_config.get('projectId') or firebase_config.get('project_id')
        if not project_id:
            return vulnerabilities
        
        try:
            # Common Cloud Function endpoints to test
            function_endpoints = [
                f"https://us-central1-{project_id}.cloudfunctions.net/api",
                f"https://us-central1-{project_id}.cloudfunctions.net/webhook",
                f"https://us-central1-{project_id}.cloudfunctions.net/admin",
                f"https://us-central1-{project_id}.cloudfunctions.net/test",
                f"https://us-central1-{project_id}.cloudfunctions.net/dev"
            ]
            
            for endpoint in function_endpoints:
                try:
                    response = await client.get(endpoint)
                    
                    if response.status_code == 200:
                        vuln = self.create_vulnerability(
                            title=f"Exposed Firebase Cloud Function: {endpoint.split('/')[-1]}",
                            description=f"Cloud Function endpoint is publicly accessible",
                            severity="medium",
                            category="Access Control",
                            url=endpoint,
                            evidence=f"HTTP {response.status_code}: {response.text[:200]}",
                            cwe_id="CWE-284",
                            remediation="Implement proper authentication for Cloud Functions"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)
                        
                except Exception:
                    pass  # Function doesn't exist, which is expected
        
        except Exception as e:
            self.logger.debug("Cloud Functions test failed", error=str(e))
        
        return vulnerabilities

    async def _check_exposed_config(self, firebase_config: Dict[str, Any], target_url: str) -> List[Dict[str, Any]]:
        """Check for exposed Firebase configuration."""
        vulnerabilities = []
        
        try:
            # Check if API key is exposed in client-side code
            api_key = firebase_config.get('apiKey') or firebase_config.get('api_key')
            if api_key:
                vuln = self.create_vulnerability(
                    title="Firebase API Key Exposed in Client-Side Code",
                    description="Firebase API key is visible in client-side JavaScript",
                    severity="low",
                    category="Information Disclosure",
                    url=target_url,
                    evidence=f"API Key: {api_key}",
                    cwe_id="CWE-200",
                    remediation="Firebase API keys in client-side code is normal, but ensure proper security rules",
                    references=["https://firebase.google.com/docs/projects/api-keys"]
                )
                vulnerabilities.append(vuln)
                self.log_vulnerability(vuln)
            
            # Check for sensitive configuration
            sensitive_keys = ['privateKey', 'clientSecret', 'serverKey']
            for key in sensitive_keys:
                if key in firebase_config:
                    vuln = self.create_vulnerability(
                        title=f"Sensitive Firebase Configuration Exposed: {key}",
                        description=f"Sensitive configuration key '{key}' found in client-side code",
                        severity="high",
                        category="Sensitive Data Exposure",
                        url=target_url,
                        evidence=f"Found {key} in configuration",
                        cwe_id="CWE-200",
                        remediation=f"Remove {key} from client-side configuration"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
        
        except Exception as e:
            self.logger.debug("Config exposure check failed", error=str(e))
        
        return vulnerabilities