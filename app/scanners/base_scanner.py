from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import structlog

logger = structlog.get_logger()


@dataclass
class ScannerResult:
    """Base result structure for all scanners."""
    vulnerabilities: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    scan_duration: float
    timestamp: datetime
    scanner_name: str
    target_url: str
    success: bool
    error_message: Optional[str] = None


class BaseScanner(ABC):
    """Base class for all vulnerability scanners."""
    
    def __init__(self, timeout: int = 300, concurrent_requests: int = 10):
        self.timeout = timeout
        self.concurrent_requests = concurrent_requests
        self.logger = structlog.get_logger()
    
    @abstractmethod
    async def scan(self, target_url: str, **kwargs) -> ScannerResult:
        """Main scan method that each scanner must implement."""
        pass
    
    def create_vulnerability(
        self,
        title: str,
        description: str,
        severity: str,
        category: str,
        url: Optional[str] = None,
        method: Optional[str] = None,
        parameter: Optional[str] = None,
        payload: Optional[str] = None,
        evidence: Optional[str] = None,
        cvss_score: Optional[str] = None,
        cwe_id: Optional[str] = None,
        owasp_category: Optional[str] = None,
        remediation: Optional[str] = None,
        references: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Create a standardized vulnerability dictionary."""
        return {
            "title": title,
            "description": description,
            "severity": severity,
            "category": category,
            "url": url,
            "method": method,
            "parameter": parameter,
            "payload": payload,
            "evidence": evidence,
            "cvss_score": cvss_score,
            "cwe_id": cwe_id,
            "owasp_category": owasp_category,
            "remediation": remediation,
            "references": references or []
        }
    
    def log_vulnerability(self, vulnerability: Dict[str, Any]):
        """Log discovered vulnerability."""
        self.logger.info(
            "Vulnerability discovered",
            title=vulnerability["title"],
            severity=vulnerability["severity"],
            category=vulnerability["category"],
            url=vulnerability.get("url")
        )