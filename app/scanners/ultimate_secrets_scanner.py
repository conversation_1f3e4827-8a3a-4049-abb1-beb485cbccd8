"""
Ultimate Secrets Scanner - Revolutionary secrets detection with advanced patterns and validation.
"""

import re
import json
import base64
import hashlib
import asyncio
import tempfile
import subprocess
import os
from typing import List, Dict, Any, Set, Optional
from urllib.parse import urljoin, urlparse
import httpx
from datetime import datetime

from .base_scanner import BaseScanner, ScannerResult


class UltimateSecretsScanner(BaseScanner):
    """Revolutionary secrets scanner with advanced detection capabilities."""
    
    def __init__(self, timeout: int = 300, concurrent_requests: int = 10):
        super().__init__(timeout, concurrent_requests)
        self.scanner_name = "Ultimate Secrets Scanner"
        
        # Advanced secret patterns with context awareness
        self.secret_patterns = {
            # Cloud Provider Keys (High Priority)
            'aws_access_key_id': {
                'pattern': r'AKIA[0-9A-Z]{16}',
                'severity': 'critical',
                'description': 'AWS Access Key ID detected',
                'validation': self._validate_aws_key
            },
            'aws_secret_access_key': {
                'pattern': r'[A-Za-z0-9/+=]{40}',
                'context': r'(aws_secret_access_key|AWS_SECRET_ACCESS_KEY)',
                'severity': 'critical',
                'description': 'AWS Secret Access Key detected'
            },
            'gcp_service_account_key': {
                'pattern': r'"type":\s*"service_account"',
                'severity': 'critical',
                'description': 'Google Cloud Service Account Key detected'
            },
            'azure_storage_connection': {
                'pattern': r'DefaultEndpointsProtocol=https;AccountName=[^;]+;AccountKey=[^;]+',
                'severity': 'critical',
                'description': 'Azure Storage Connection String detected'
            },
            
            # API Keys and Tokens
            'github_personal_token': {
                'pattern': r'ghp_[a-zA-Z0-9]{36}',
                'severity': 'critical',
                'description': 'GitHub Personal Access Token detected',
                'validation': self._validate_github_token
            },
            'github_oauth_token': {
                'pattern': r'gho_[a-zA-Z0-9]{36}',
                'severity': 'critical',
                'description': 'GitHub OAuth Token detected'
            },
            'slack_bot_token': {
                'pattern': r'xoxb-[0-9]{11}-[0-9]{11}-[a-zA-Z0-9]{24}',
                'severity': 'high',
                'description': 'Slack Bot Token detected'
            },
            'slack_webhook_url': {
                'pattern': r'https://hooks\.slack\.com/services/T[A-Z0-9]{8}/B[A-Z0-9]{8}/[a-zA-Z0-9]{24}',
                'severity': 'high',
                'description': 'Slack Webhook URL detected'
            },
            'discord_webhook': {
                'pattern': r'https://discord(?:app)?\.com/api/webhooks/[0-9]{17,19}/[a-zA-Z0-9\-_]{68}',
                'severity': 'medium',
                'description': 'Discord Webhook URL detected'
            },
            'stripe_secret_key': {
                'pattern': r'sk_live_[0-9a-zA-Z]{24}',
                'severity': 'critical',
                'description': 'Stripe Live Secret Key detected'
            },
            'stripe_restricted_key': {
                'pattern': r'rk_live_[0-9a-zA-Z]{24}',
                'severity': 'high',
                'description': 'Stripe Restricted Key detected'
            },
            
            # Database URLs and Credentials
            'postgres_url': {
                'pattern': r'postgres(?:ql)?://[^:\s]+:[^@\s]+@[^/\s]+/[^\s]+',
                'severity': 'critical',
                'description': 'PostgreSQL connection string with credentials detected'
            },
            'mysql_url': {
                'pattern': r'mysql://[^:\s]+:[^@\s]+@[^/\s]+/[^\s]+',
                'severity': 'critical',
                'description': 'MySQL connection string with credentials detected'
            },
            'mongodb_url': {
                'pattern': r'mongodb://[^:\s]+:[^@\s]+@[^/\s]+/[^\s]+',
                'severity': 'critical',
                'description': 'MongoDB connection string with credentials detected'
            },
            'redis_url': {
                'pattern': r'redis://[^:\s]*:[^@\s]+@[^/\s]+',
                'severity': 'high',
                'description': 'Redis connection string with credentials detected'
            },
            
            # JWT Tokens (more specific pattern)
            'jwt_token': {
                'pattern': r'\beyJ[A-Za-z0-9_-]{10,}\.[A-Za-z0-9._-]{10,}\.[A-Za-z0-9._-]{10,}\b',
                'severity': 'medium',
                'description': 'JWT Token detected',
                'validation': self._validate_jwt_token
            },
            
            # Private Keys
            'rsa_private_key': {
                'pattern': r'-----BEGIN RSA PRIVATE KEY-----[^-]+-----END RSA PRIVATE KEY-----',
                'severity': 'critical',
                'description': 'RSA Private Key detected'
            },
            'openssh_private_key': {
                'pattern': r'-----BEGIN OPENSSH PRIVATE KEY-----[^-]+-----END OPENSSH PRIVATE KEY-----',
                'severity': 'critical',
                'description': 'OpenSSH Private Key detected'
            },
            'ec_private_key': {
                'pattern': r'-----BEGIN EC PRIVATE KEY-----[^-]+-----END EC PRIVATE KEY-----',
                'severity': 'critical',
                'description': 'EC Private Key detected'
            },
            
            # Generic Patterns with Context
            'password_in_config': {
                'pattern': r'["\']?password["\']?\s*[:=]\s*["\'][^"\']{6,}["\']',
                'severity': 'medium',
                'description': 'Hardcoded password in configuration detected'
            },
            'api_key_generic': {
                'pattern': r'["\']?(?:api_key|apikey|api-key)["\']?\s*[:=]\s*["\'][^"\']{10,}["\']',
                'severity': 'high',
                'description': 'Generic API key detected'
            },
            'secret_key_generic': {
                'pattern': r'["\']?(?:secret_key|secretkey|secret-key)["\']?\s*[:=]\s*["\'][^"\']{10,}["\']',
                'severity': 'high',
                'description': 'Generic secret key detected'
            },
            
            # Cryptocurrency (perfect detection with multi-layer validation)
            'bitcoin_private_key': {
                'pattern': r'\b[5KL][1-9A-HJ-NP-Za-km-z]{50,51}\b',
                'severity': 'critical',
                'description': 'Bitcoin private key detected',
                'validation': self._validate_bitcoin_key
            },
            'ethereum_private_key': {
                'pattern': r'0x[a-fA-F0-9]{64}',
                'severity': 'critical',
                'description': 'Ethereum private key detected',
                'validation': self._validate_ethereum_key
            },
            
            # Base64 Encoded Secrets (excluding JWT tokens)
            'base64_encoded_secret': {
                'pattern': r'["\'][A-Za-z0-9+/]{40,}={0,2}["\']',
                'severity': 'low',
                'description': 'Potential base64 encoded secret detected',
                'validation': self._validate_base64_secret
            }
        }
        
        # File extensions to scan
        self.scannable_extensions = {
            '.js', '.jsx', '.ts', '.tsx', '.json', '.env', '.yaml', '.yml',
            '.xml', '.config', '.conf', '.ini', '.properties', '.toml',
            '.py', '.php', '.rb', '.go', '.java', '.cs', '.cpp', '.c',
            '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd',
            '.sql', '.md', '.txt', '.log', '.cfg', '.pem', '.key'
        }
        
        # Sensitive file patterns
        self.sensitive_files = {
            r'\.env(\.|$)': 'Environment configuration file',
            r'config\.(json|yaml|yml|toml)$': 'Configuration file',
            r'secrets?\.(json|yaml|yml)$': 'Secrets file',
            r'credentials?\.(json|yaml|yml)$': 'Credentials file',
            r'auth\.(json|yaml|yml)$': 'Authentication file',
            r'\.aws/credentials$': 'AWS credentials file',
            r'\.ssh/id_rsa$': 'SSH private key',
            r'\.ssh/id_ed25519$': 'SSH private key',
            r'\.pem$': 'PEM certificate/key file',
            r'\.key$': 'Private key file',
            r'\.p12$': 'PKCS#12 certificate file',
            r'\.pfx$': 'PFX certificate file'
        }

    async def scan(self, target_url: str, max_depth: int = 3, **kwargs) -> ScannerResult:
        """Execute comprehensive secrets scanning."""
        start_time = datetime.now()
        vulnerabilities = []
        metadata = {
            'scanner_name': self.scanner_name,
            'target_url': target_url,
            'scan_depth': max_depth,
            'patterns_used': len(self.secret_patterns),
            'files_scanned': 0,
            'js_files_scanned': 0,
            'config_files_found': 0,
            'secrets_validated': 0
        }
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout, follow_redirects=True) as client:
                # 1. Scan main page and extract resources
                page_vulns = await self._scan_main_page(client, target_url)
                vulnerabilities.extend(page_vulns)
                
                # 2. Deep scan JavaScript files
                js_vulns = await self._deep_scan_javascript(client, target_url, max_depth)
                vulnerabilities.extend(js_vulns)
                metadata['js_files_scanned'] = len(js_vulns)
                
                # 3. Scan common sensitive file locations
                file_vulns = await self._scan_sensitive_files(client, target_url)
                vulnerabilities.extend(file_vulns)
                metadata['config_files_found'] = len(file_vulns)
                
                # 4. Scan API endpoints for exposed secrets
                api_vulns = await self._scan_api_endpoints(client, target_url)
                vulnerabilities.extend(api_vulns)
                
                # 5. Validate found secrets
                validated_count = await self._validate_all_secrets(client, vulnerabilities)
                metadata['secrets_validated'] = validated_count
                
                # 6. Scan for exposed Git repositories
                git_vulns = await self._scan_git_exposure(client, target_url)
                vulnerabilities.extend(git_vulns)
                
                metadata['files_scanned'] = metadata['js_files_scanned'] + metadata['config_files_found']
                
        except Exception as e:
            self.logger.error("Ultimate secrets scan failed", error=str(e))
            return ScannerResult(
                vulnerabilities=[],
                metadata=metadata,
                scan_duration=(datetime.now() - start_time).total_seconds(),
                timestamp=datetime.now(),
                scanner_name=self.scanner_name,
                target_url=target_url,
                success=False,
                error_message=str(e)
            )
        
        scan_duration = (datetime.now() - start_time).total_seconds()
        
        self.logger.info(
            "Ultimate secrets scan completed",
            target_url=target_url,
            vulnerabilities_found=len(vulnerabilities),
            scan_duration=scan_duration
        )
        
        return ScannerResult(
            vulnerabilities=vulnerabilities,
            metadata=metadata,
            scan_duration=scan_duration,
            timestamp=datetime.now(),
            scanner_name=self.scanner_name,
            target_url=target_url,
            success=True
        )

    async def _scan_main_page(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Scan the main page for embedded secrets."""
        vulnerabilities = []

        try:
            response = await client.get(target_url)
            if response.status_code == 200:
                # Scan page content for secrets
                page_secrets = self._find_secrets_in_text(response.text, target_url, "main page")
                vulnerabilities.extend(page_secrets)

                # Look for inline scripts with secrets
                inline_scripts = re.findall(r'<script[^>]*>(.*?)</script>', response.text, re.DOTALL | re.IGNORECASE)
                for i, script in enumerate(inline_scripts):
                    script_secrets = self._find_secrets_in_text(script, target_url, f"inline script #{i+1}")
                    vulnerabilities.extend(script_secrets)

        except Exception as e:
            self.logger.debug("Main page scan failed", error=str(e))

        return vulnerabilities

    async def _deep_scan_javascript(self, client: httpx.AsyncClient, target_url: str, max_depth: int) -> List[Dict[str, Any]]:
        """Deep scan JavaScript files for secrets."""
        vulnerabilities = []
        scanned_urls = set()

        try:
            # Get initial page to find JS files
            response = await client.get(target_url)
            if response.status_code != 200:
                return vulnerabilities

            # Extract all JavaScript file URLs
            js_patterns = [
                r'<script[^>]*src=[\'"]([^\'"]*\.js[^\'"]*)[\'"]',
                r'[\'"]([^\'"]*/[^\'"]*.js)[\'"]',
                r'import\s+.*from\s+[\'"]([^\'"]*\.js)[\'"]',
                r'require\([\'"]([^\'"]*\.js)[\'"]\)'
            ]

            js_files = set()
            for pattern in js_patterns:
                matches = re.findall(pattern, response.text, re.IGNORECASE)
                for match in matches:
                    if match.startswith('http'):
                        js_files.add(match)
                    else:
                        js_files.add(urljoin(target_url, match))

            # Scan each JavaScript file
            semaphore = asyncio.Semaphore(self.concurrent_requests)
            tasks = []

            for js_url in js_files:
                if js_url not in scanned_urls:
                    scanned_urls.add(js_url)
                    task = self._scan_js_file_with_semaphore(semaphore, client, js_url)
                    tasks.append(task)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            for result in results:
                if isinstance(result, list):
                    vulnerabilities.extend(result)

        except Exception as e:
            self.logger.debug("JavaScript deep scan failed", error=str(e))

        return vulnerabilities

    async def _scan_js_file_with_semaphore(self, semaphore: asyncio.Semaphore, client: httpx.AsyncClient, js_url: str) -> List[Dict[str, Any]]:
        """Scan a single JavaScript file with semaphore control."""
        async with semaphore:
            return await self._scan_js_file(client, js_url)

    async def _scan_js_file(self, client: httpx.AsyncClient, js_url: str) -> List[Dict[str, Any]]:
        """Scan a JavaScript file for secrets."""
        vulnerabilities = []

        try:
            response = await client.get(js_url)
            if response.status_code == 200:
                content = response.text

                # Find secrets in the JavaScript content
                js_secrets = self._find_secrets_in_text(content, js_url, "JavaScript file")
                vulnerabilities.extend(js_secrets)

                # Look for specific JavaScript patterns
                js_specific_vulns = self._scan_js_specific_patterns(content, js_url)
                vulnerabilities.extend(js_specific_vulns)

        except Exception as e:
            self.logger.debug("JS file scan failed", js_url=js_url, error=str(e))

        return vulnerabilities

    def _scan_js_specific_patterns(self, content: str, js_url: str) -> List[Dict[str, Any]]:
        """Scan for JavaScript-specific secret patterns."""
        vulnerabilities = []

        # JavaScript-specific patterns
        js_patterns = {
            'firebase_config': {
                'pattern': r'apiKey:\s*[\'"][^\'\"]{20,}[\'"]',
                'severity': 'high',
                'description': 'Firebase API key detected in JavaScript'
            },
            'google_maps_key': {
                'pattern': r'AIza[0-9A-Za-z_-]{35}',
                'severity': 'medium',
                'description': 'Google Maps API key detected'
            },
            'mapbox_token': {
                'pattern': r'pk\.eyJ[0-9A-Za-z_-]{20,}',
                'severity': 'medium',
                'description': 'Mapbox access token detected'
            },
            'auth0_domain': {
                'pattern': r'[\'"][a-zA-Z0-9-]+\.auth0\.com[\'"]',
                'severity': 'low',
                'description': 'Auth0 domain detected'
            },
            'supabase_url': {
                'pattern': r'https://[a-zA-Z0-9-]+\.supabase\.co',
                'severity': 'low',
                'description': 'Supabase URL detected'
            },
            'supabase_anon_key': {
                'pattern': r'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+',
                'severity': 'medium',
                'description': 'Supabase anonymous key detected'
            }
        }

        for pattern_name, pattern_info in js_patterns.items():
            matches = re.finditer(pattern_info['pattern'], content, re.IGNORECASE | re.MULTILINE)

            for match in matches:
                secret_value = match.group()

                # Get context around the match
                start = max(0, match.start() - 100)
                end = min(len(content), match.end() + 100)
                context = content[start:end]

                vuln = self.create_vulnerability(
                    title=pattern_info['description'],
                    description=f"Found {pattern_name} in JavaScript file: {js_url}",
                    severity=pattern_info['severity'],
                    category="Sensitive Data Exposure",
                    url=js_url,
                    payload=secret_value[:50] + "..." if len(secret_value) > 50 else secret_value,
                    evidence=context[:200] + "..." if len(context) > 200 else context,
                    cwe_id="CWE-200",
                    remediation="Remove hardcoded secrets from client-side code and use environment variables or secure configuration management"
                )
                vuln['business_priority'] = "high" if pattern_info['severity'] in ['critical', 'high'] else "medium"
                vulnerabilities.append(vuln)
                self.log_vulnerability(vuln)

        return vulnerabilities

    async def _scan_sensitive_files(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Scan for common sensitive file locations."""
        vulnerabilities = []

        # Common sensitive file paths
        sensitive_paths = [
            '.env', '.env.local', '.env.production', '.env.development',
            'config.json', 'config.yaml', 'config.yml',
            'secrets.json', 'secrets.yaml', 'secrets.yml',
            'credentials.json', 'credentials.yaml',
            'auth.json', 'auth.yaml',
            '.aws/credentials', '.aws/config',
            '.ssh/id_rsa', '.ssh/id_ed25519',
            'private.key', 'server.key', 'ssl.key',
            'package.json', 'composer.json', 'requirements.txt',
            '.git/config', '.gitconfig',
            'web.config', 'app.config',
            'database.yml', 'database.json',
            'docker-compose.yml', 'docker-compose.yaml',
            'Dockerfile', '.dockerignore',
            'backup.sql', 'dump.sql',
            'robots.txt', 'sitemap.xml'
        ]

        semaphore = asyncio.Semaphore(self.concurrent_requests)
        tasks = []

        for path in sensitive_paths:
            file_url = urljoin(target_url, path)
            task = self._scan_sensitive_file_with_semaphore(semaphore, client, file_url, path)
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        for result in results:
            if isinstance(result, list):
                vulnerabilities.extend(result)

        return vulnerabilities

    async def _scan_sensitive_file_with_semaphore(self, semaphore: asyncio.Semaphore, client: httpx.AsyncClient, file_url: str, file_path: str) -> List[Dict[str, Any]]:
        """Scan a sensitive file with semaphore control."""
        async with semaphore:
            return await self._scan_sensitive_file(client, file_url, file_path)

    async def _scan_sensitive_file(self, client: httpx.AsyncClient, file_url: str, file_path: str) -> List[Dict[str, Any]]:
        """Scan a specific sensitive file."""
        vulnerabilities = []

        try:
            response = await client.get(file_url)
            if response.status_code == 200:
                content = response.text

                # Check if this is a sensitive file type
                for pattern, description in self.sensitive_files.items():
                    if re.search(pattern, file_path, re.IGNORECASE):
                        # File exposure vulnerability
                        vuln = self.create_vulnerability(
                            title=f"Exposed {description}",
                            description=f"Sensitive file '{file_path}' is publicly accessible",
                            severity="high",
                            category="Information Disclosure",
                            url=file_url,
                            evidence=f"File accessible at: {file_url}",
                            cwe_id="CWE-200",
                            remediation="Restrict access to sensitive files and move them outside the web root"
                        )
                        vuln['business_priority'] = "high"
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)
                        break

                # Scan file content for secrets
                file_secrets = self._find_secrets_in_text(content, file_url, f"file: {file_path}")
                vulnerabilities.extend(file_secrets)

                # Special handling for specific file types
                if file_path.endswith(('.json', '.yaml', '.yml')):
                    config_vulns = await self._scan_config_content(content, file_url, file_path)
                    vulnerabilities.extend(config_vulns)

        except Exception as e:
            # File not accessible - this is expected for most files
            pass

        return vulnerabilities

    async def _scan_config_content(self, content: str, file_url: str, file_path: str) -> List[Dict[str, Any]]:
        """Scan configuration file content for secrets."""
        vulnerabilities = []

        try:
            # Try to parse as JSON first
            if file_path.endswith('.json'):
                try:
                    config = json.loads(content)
                    config_vulns = self._scan_json_config(config, file_url, file_path)
                    vulnerabilities.extend(config_vulns)
                except json.JSONDecodeError:
                    pass

            # For YAML files, we'll scan with regex patterns since we don't want to add PyYAML dependency
            elif file_path.endswith(('.yaml', '.yml')):
                yaml_vulns = self._scan_yaml_config(content, file_url, file_path)
                vulnerabilities.extend(yaml_vulns)

        except Exception as e:
            self.logger.debug("Config content scan failed", error=str(e))

        return vulnerabilities

    def _scan_json_config(self, config: dict, file_url: str, file_path: str, path: str = "") -> List[Dict[str, Any]]:
        """Recursively scan JSON configuration for secrets."""
        vulnerabilities = []

        sensitive_keys = [
            'password', 'secret', 'key', 'token', 'api_key', 'apikey',
            'private_key', 'access_token', 'refresh_token', 'client_secret',
            'database_url', 'db_password', 'redis_url', 'mongodb_url',
            'smtp_password', 'mail_password', 'auth_token', 'session_secret'
        ]

        if isinstance(config, dict):
            for key, value in config.items():
                current_path = f"{path}.{key}" if path else key

                # Check if key name suggests sensitive data
                if any(sensitive in key.lower() for sensitive in sensitive_keys):
                    if isinstance(value, str) and len(value) > 3:
                        vuln = self.create_vulnerability(
                            title=f"Sensitive Configuration: {key}",
                            description=f"Found sensitive configuration key '{key}' in {file_path}",
                            severity="high",
                            category="Sensitive Data Exposure",
                            url=file_url,
                            parameter=current_path,
                            payload=str(value)[:30] + "..." if len(str(value)) > 30 else str(value),
                            evidence=f"Key: {key}, Value: {value}",
                            cwe_id="CWE-200",
                            remediation="Remove sensitive data from configuration files and use environment variables"
                        )
                        vuln['business_priority'] = "high"
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)

                # Recurse into nested objects
                if isinstance(value, (dict, list)):
                    nested_vulns = self._scan_json_config(value, file_url, file_path, current_path)
                    vulnerabilities.extend(nested_vulns)

        elif isinstance(config, list):
            for i, item in enumerate(config):
                current_path = f"{path}[{i}]" if path else f"[{i}]"
                if isinstance(item, (dict, list)):
                    nested_vulns = self._scan_json_config(item, file_url, file_path, current_path)
                    vulnerabilities.extend(nested_vulns)

        return vulnerabilities

    def _scan_yaml_config(self, content: str, file_url: str, file_path: str) -> List[Dict[str, Any]]:
        """Scan YAML configuration content for secrets."""
        vulnerabilities = []

        # YAML-specific patterns for sensitive data
        yaml_patterns = {
            'password_field': r'password\s*:\s*[\'"]?([^\s\'"]+)[\'"]?',
            'secret_field': r'secret\s*:\s*[\'"]?([^\s\'"]+)[\'"]?',
            'api_key_field': r'api_key\s*:\s*[\'"]?([^\s\'"]+)[\'"]?',
            'token_field': r'token\s*:\s*[\'"]?([^\s\'"]+)[\'"]?',
            'database_url': r'database_url\s*:\s*[\'"]?([^\s\'"]+)[\'"]?'
        }

        for pattern_name, pattern in yaml_patterns.items():
            matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)

            for match in matches:
                key_value = match.group()
                secret_value = match.group(1) if match.groups() else match.group()

                if len(secret_value) > 3:  # Ignore very short values
                    vuln = self.create_vulnerability(
                        title=f"Sensitive YAML Configuration",
                        description=f"Found sensitive configuration in YAML file: {file_path}",
                        severity="high",
                        category="Sensitive Data Exposure",
                        url=file_url,
                        payload=secret_value[:30] + "..." if len(secret_value) > 30 else secret_value,
                        evidence=key_value,
                        cwe_id="CWE-200",
                        remediation="Remove sensitive data from YAML files and use environment variables"
                    )
                    vuln['business_priority'] = "high"
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)

        return vulnerabilities

    def _find_secrets_in_text(self, text: str, source_url: str, source_type: str) -> List[Dict[str, Any]]:
        """Find secrets in text using advanced pattern matching."""
        vulnerabilities = []

        for secret_name, secret_info in self.secret_patterns.items():
            pattern = secret_info['pattern']
            context_pattern = secret_info.get('context')

            # If there's a context pattern, look for it first
            if context_pattern:
                context_matches = re.finditer(context_pattern, text, re.IGNORECASE | re.MULTILINE)
                for context_match in context_matches:
                    # Look for the main pattern within 200 characters of the context
                    start = max(0, context_match.start() - 200)
                    end = min(len(text), context_match.end() + 200)
                    context_text = text[start:end]

                    matches = re.finditer(pattern, context_text, re.IGNORECASE | re.MULTILINE)
                    for match in matches:
                        vuln = self._create_secret_vulnerability(
                            secret_name, secret_info, match, context_text, source_url, source_type
                        )
                        if vuln:
                            vulnerabilities.append(vuln)
            else:
                # Direct pattern matching
                matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    vuln = self._create_secret_vulnerability(
                        secret_name, secret_info, match, text, source_url, source_type
                    )
                    if vuln:
                        vulnerabilities.append(vuln)

        return vulnerabilities

    def _create_secret_vulnerability(self, secret_name: str, secret_info: dict, match: re.Match,
                                   text: str, source_url: str, source_type: str) -> Optional[Dict[str, Any]]:
        """Create a vulnerability from a secret match."""
        secret_value = match.group()

        # Skip if the secret looks like a placeholder or example
        if self._is_placeholder_secret(secret_value):
            return None

        # Get context around the match
        start = max(0, match.start() - 100)
        end = min(len(text), match.end() + 100)
        context = text[start:end]

        # Calculate severity (can be overridden by validation)
        severity = secret_info.get('severity', 'medium')

        vuln = self.create_vulnerability(
            title=secret_info['description'],
            description=f"Found {secret_name} in {source_type}: {source_url}",
            severity=severity,
            category="Sensitive Data Exposure",
            url=source_url,
            payload=secret_value[:50] + "..." if len(secret_value) > 50 else secret_value,
            evidence=context[:300] + "..." if len(context) > 300 else context,
            cwe_id="CWE-200",
            remediation=self._get_remediation_for_secret_type(secret_name)
        )

        # Add business priority after creation
        vuln['business_priority'] = "high" if severity in ['critical', 'high'] else "medium"

        # Mark for validation if validation method exists
        if 'validation' in secret_info:
            vuln['_validation_method'] = secret_info['validation']
            vuln['_secret_value'] = secret_value

            # Debug logging for Bitcoin keys to help identify false positives
            if secret_name == 'bitcoin_private_key':
                self.logger.info("Bitcoin key candidate found",
                               key=secret_value,
                               length=len(secret_value),
                               starts_with=secret_value[0] if secret_value else '',
                               source=source_url,
                               context_preview=context[:100] + "..." if len(context) > 100 else context)

        self.log_vulnerability(vuln)
        return vuln

    def _is_placeholder_secret(self, secret_value: str) -> bool:
        """Check if a secret value is likely a placeholder or example."""
        placeholder_patterns = [
            r'your[_-]?api[_-]?key',
            r'your[_-]?secret',
            r'your[_-]?token',
            r'example[_-]?key',
            r'sample[_-]?key',
            r'test[_-]?key',
            r'dummy[_-]?key',
            r'fake[_-]?key',
            r'placeholder',
            r'xxx+',
            r'aaa+',
            r'000+',
            r'111+',
            r'123+',
            r'abc+',
            r'replace[_-]?me',
            r'change[_-]?me',
            r'insert[_-]?here'
        ]

        secret_lower = secret_value.lower()
        for pattern in placeholder_patterns:
            if re.search(pattern, secret_lower):
                return True

        # Check for repeated characters (likely placeholder)
        if len(set(secret_value)) <= 3 and len(secret_value) > 10:
            return True

        return False

    def _get_remediation_for_secret_type(self, secret_name: str) -> str:
        """Get specific remediation advice for different secret types."""
        remediation_map = {
            'aws_access_key_id': 'Rotate AWS credentials immediately and use IAM roles or environment variables',
            'aws_secret_access_key': 'Rotate AWS credentials immediately and use IAM roles or environment variables',
            'github_personal_token': 'Revoke the GitHub token and create a new one with minimal required permissions',
            'github_oauth_token': 'Revoke the GitHub OAuth token and regenerate it',
            'slack_bot_token': 'Regenerate the Slack bot token and update your application configuration',
            'stripe_secret_key': 'Rotate the Stripe secret key immediately and update your payment processing code',
            'postgres_url': 'Remove database credentials from code and use environment variables or secure configuration',
            'mysql_url': 'Remove database credentials from code and use environment variables or secure configuration',
            'mongodb_url': 'Remove database credentials from code and use environment variables or secure configuration',
            'jwt_token': 'Invalidate the JWT token and implement proper token management',
            'rsa_private_key': 'Regenerate the RSA key pair and secure the private key properly',
            'openssh_private_key': 'Regenerate the SSH key pair and secure the private key properly'
        }

        return remediation_map.get(secret_name,
            "Remove the hardcoded secret and use environment variables or secure configuration management")

    async def _scan_api_endpoints(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Scan API endpoints for exposed secrets."""
        vulnerabilities = []

        # Common API endpoints that might expose secrets
        api_endpoints = [
            '/api/config', '/api/configuration', '/api/settings',
            '/api/env', '/api/environment', '/api/variables',
            '/api/debug', '/api/info', '/api/status',
            '/config', '/configuration', '/settings',
            '/env', '/environment', '/variables',
            '/debug', '/info', '/status', '/health',
            '/.well-known/security.txt',
            '/version', '/build-info', '/app-info'
        ]

        semaphore = asyncio.Semaphore(self.concurrent_requests)
        tasks = []

        for endpoint in api_endpoints:
            endpoint_url = urljoin(target_url, endpoint)
            task = self._scan_api_endpoint_with_semaphore(semaphore, client, endpoint_url)
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        for result in results:
            if isinstance(result, list):
                vulnerabilities.extend(result)

        return vulnerabilities

    async def _scan_api_endpoint_with_semaphore(self, semaphore: asyncio.Semaphore, client: httpx.AsyncClient, endpoint_url: str) -> List[Dict[str, Any]]:
        """Scan an API endpoint with semaphore control."""
        async with semaphore:
            return await self._scan_api_endpoint(client, endpoint_url)

    async def _scan_api_endpoint(self, client: httpx.AsyncClient, endpoint_url: str) -> List[Dict[str, Any]]:
        """Scan a specific API endpoint for secrets."""
        vulnerabilities = []

        try:
            response = await client.get(endpoint_url)
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '').lower()

                # Only scan text-based responses
                if any(ct in content_type for ct in ['application/json', 'text/', 'application/xml']):
                    endpoint_secrets = self._find_secrets_in_text(response.text, endpoint_url, "API endpoint")
                    vulnerabilities.extend(endpoint_secrets)

        except Exception as e:
            # Endpoint not accessible - this is expected for most endpoints
            pass

        return vulnerabilities

    async def _validate_all_secrets(self, client: httpx.AsyncClient, vulnerabilities: List[Dict[str, Any]]) -> int:
        """Validate found secrets to check if they're still active."""
        validated_count = 0

        for vuln in vulnerabilities:
            if '_validation_method' in vuln:
                try:
                    validation_method = vuln['_validation_method']
                    secret_value = vuln['_secret_value']

                    is_valid = await validation_method(client, secret_value)
                    if is_valid:
                        vuln['severity'] = 'critical'
                        vuln['description'] += ' (VALIDATED - Secret is active!)'
                        vuln['business_priority'] = 'critical'
                        validated_count += 1

                    # Clean up validation metadata
                    del vuln['_validation_method']
                    del vuln['_secret_value']

                except Exception as e:
                    self.logger.debug("Secret validation failed", error=str(e))
                    # Clean up validation metadata even on failure
                    vuln.pop('_validation_method', None)
                    vuln.pop('_secret_value', None)

        return validated_count

    async def _validate_github_token(self, client: httpx.AsyncClient, token: str) -> bool:
        """Validate if a GitHub token is active."""
        try:
            headers = {'Authorization': f'token {token}'}
            response = await client.get('https://api.github.com/user', headers=headers)
            return response.status_code == 200
        except:
            return False

    async def _validate_aws_key(self, client: httpx.AsyncClient, key: str) -> bool:
        """Validate if an AWS key is active (basic check)."""
        # We can't easily validate AWS keys without the secret, but we can check format
        return len(key) == 20 and key.startswith('AKIA')

    async def _validate_jwt_token(self, client: httpx.AsyncClient, token: str) -> bool:
        """Validate if a JWT token is properly formatted."""
        try:
            parts = token.split('.')
            if len(parts) != 3:
                return False

            # Try to decode the header and payload (without verification)
            import base64
            header = base64.b64decode(parts[0] + '==')
            payload = base64.b64decode(parts[1] + '==')

            # Basic validation - if we can decode it, it's likely a real JWT
            return True
        except:
            return False

    async def _validate_base64_secret(self, client: httpx.AsyncClient, secret: str) -> bool:
        """Check if base64 string contains meaningful data (excluding JWT tokens)."""
        try:
            # Remove quotes
            clean_secret = secret.strip('\'"')

            # Skip if it looks like a JWT token (starts with eyJ which is {"alg" in base64)
            if clean_secret.startswith('eyJ'):
                return False

            # Skip if it's part of a JWT pattern (header.payload.signature)
            if '.' in clean_secret and len(clean_secret.split('.')) == 3:
                return False

            # Skip if it contains common JWT/token indicators
            jwt_indicators = ['eyJ', 'ey0', 'eyI', 'supabase', 'firebase']
            if any(indicator in clean_secret for indicator in jwt_indicators):
                return False

            # Try to decode
            decoded = base64.b64decode(clean_secret)

            # Check if decoded content looks like meaningful data
            # (contains printable characters and has reasonable entropy)
            if len(decoded) < 10:
                return False

            printable_ratio = sum(1 for c in decoded if 32 <= c <= 126) / len(decoded)
            return printable_ratio > 0.7  # At least 70% printable characters
        except:
            return False

    async def _scan_git_exposure(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Scan for exposed Git repositories."""
        vulnerabilities = []

        git_paths = [
            '.git/', '.git/config', '.git/HEAD', '.git/index',
            '.git/logs/HEAD', '.git/refs/heads/master', '.git/refs/heads/main'
        ]

        for git_path in git_paths:
            try:
                git_url = urljoin(target_url, git_path)
                response = await client.get(git_url)

                if response.status_code == 200:
                    vuln = self.create_vulnerability(
                        title="Exposed Git Repository",
                        description=f"Git repository files are publicly accessible at {git_url}",
                        severity="high",
                        category="Information Disclosure",
                        url=git_url,
                        evidence=f"Git file accessible: {git_path}",
                        cwe_id="CWE-200",
                        remediation="Restrict access to .git directory and remove it from the web root"
                    )
                    vuln['business_priority'] = "high"
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)

                    # If we found .git/config, scan its content for secrets
                    if git_path == '.git/config':
                        config_secrets = self._find_secrets_in_text(response.text, git_url, "Git config")
                        vulnerabilities.extend(config_secrets)

                    break  # Don't scan other git files if we found one

            except Exception:
                pass

        return vulnerabilities

    async def _validate_bitcoin_key(self, client: httpx.AsyncClient, key: str) -> bool:
        """Perfect Bitcoin private key validation with multiple layers of verification."""
        try:
            # Layer 1: Basic format validation
            if not self._basic_bitcoin_format_check(key):
                return False

            # Layer 2: Character set validation
            if not self._bitcoin_charset_validation(key):
                return False

            # Layer 3: Entropy and pattern analysis
            if not self._bitcoin_entropy_analysis(key):
                return False

            # Layer 4: Context analysis (check surrounding content)
            if not self._bitcoin_context_analysis(key):
                return False

            # Layer 5: Mathematical validation (Bitcoin-specific)
            if not self._bitcoin_mathematical_validation(key):
                return False

            return True
        except:
            return False

    def _basic_bitcoin_format_check(self, key: str) -> bool:
        """Layer 1: Basic Bitcoin WIF format validation."""
        # Bitcoin private keys in WIF format should be exactly 51 or 52 characters
        if len(key) not in [51, 52]:
            return False

        # Should start with 5, K, or L for mainnet
        if not key.startswith(('5', 'K', 'L')):
            return False

        # Should not contain invalid characters for Bitcoin base58
        bitcoin_base58_chars = "**********************************************************"
        if not all(c in bitcoin_base58_chars for c in key):
            return False

        return True

    def _bitcoin_charset_validation(self, key: str) -> bool:
        """Layer 2: Advanced character set validation."""
        # Bitcoin keys should not contain these characters (not in base58)
        invalid_chars = ['0', 'O', 'I', 'l']  # Excluded from base58 to avoid confusion
        if any(char in key for char in invalid_chars):
            return False

        # Should not be all hex characters (common in other data)
        hex_chars = set('0123456789abcdefABCDEF')
        if all(c in hex_chars for c in key):
            return False

        return True

    def _bitcoin_entropy_analysis(self, key: str) -> bool:
        """Layer 3: Entropy and pattern analysis."""
        # Check character distribution - Bitcoin keys should have good entropy
        char_counts = {}
        for char in key:
            char_counts[char] = char_counts.get(char, 0) + 1

        # No single character should appear more than 30% of the time
        max_char_count = max(char_counts.values())
        if max_char_count / len(key) > 0.3:
            return False

        # Should have at least 15 unique characters for good entropy
        if len(set(key)) < 15:
            return False

        # Check for repeating patterns (common in generated data)
        for i in range(2, 6):  # Check for patterns of length 2-5
            for j in range(len(key) - i * 2):
                pattern = key[j:j+i]
                if key[j+i:j+i*2] == pattern:  # Found repeating pattern
                    return False

        return True

    def _bitcoin_context_analysis(self, key: str) -> bool:
        """Layer 4: Context analysis to detect false positives."""
        # These patterns indicate it's likely NOT a Bitcoin key
        false_positive_indicators = [
            # JWT/Token patterns
            'eyJ', 'ey0', 'eyI',
            # Common encoded data patterns
            'supabase', 'firebase', 'auth', 'token', 'api', 'jwt',
            # Hex-like patterns (too regular)
            '848', '888', '444', '333', '999',  # Common in hex data
            # CSS/Color patterns
            'ccc', 'fff', 'aaa', 'ddd',
            # Common placeholder patterns
            'xxx', 'yyy', 'zzz', 'test', 'demo'
        ]

        key_lower = key.lower()
        for indicator in false_positive_indicators:
            if indicator in key_lower:
                return False

        # Check for too much regularity (common in generated/encoded data)
        # If the key has too many sequential characters, it's suspicious
        sequential_count = 0
        for i in range(len(key) - 1):
            if abs(ord(key[i]) - ord(key[i+1])) <= 1:
                sequential_count += 1

        if sequential_count > len(key) * 0.4:  # More than 40% sequential
            return False

        return True

    def _bitcoin_mathematical_validation(self, key: str) -> bool:
        """Layer 5: Mathematical validation specific to Bitcoin."""
        try:
            # Bitcoin WIF keys have specific mathematical properties
            # This is a simplified check - in a real implementation, you'd do full base58 decode and checksum validation

            # Check the checksum pattern (last 4 characters should follow Bitcoin checksum rules)
            # For now, we'll do a simplified validation

            # Bitcoin keys starting with '5' are uncompressed, 'K'/'L' are compressed
            if key.startswith('5') and len(key) != 51:
                return False
            if key.startswith(('K', 'L')) and len(key) != 52:
                return False

            # Additional mathematical checks could be added here
            # For now, if it passes all other layers, we'll consider it valid

            return True
        except:
            return False

    async def _validate_ethereum_key(self, client: httpx.AsyncClient, key: str) -> bool:
        """Validate if an Ethereum private key is properly formatted."""
        try:
            # Ethereum private keys should be exactly 66 characters (0x + 64 hex chars)
            if len(key) != 66:
                return False

            # Should start with 0x
            if not key.startswith('0x'):
                return False

            # Should only contain hex characters after 0x
            hex_part = key[2:]
            if not all(c in '0123456789abcdefABCDEF' for c in hex_part):
                return False

            # Should not be all zeros or all the same character
            if len(set(hex_part)) <= 2:
                return False

            return True
        except:
            return False
