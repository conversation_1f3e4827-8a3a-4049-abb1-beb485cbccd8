import asyncio
import re
import json
import base64
from typing import List, Dict, Any, Optional
from datetime import datetime
import time
import asyncpg
import aiomysql
import aiosqlite
from urllib.parse import urlparse, urljoin
import httpx
from .base_scanner import BaseScanner, ScannerResult


class DatabaseSecurityChecker(BaseScanner):
    """Ultimate Database Security Scanner - Tests modern cloud databases, APIs, and traditional databases."""

    def __init__(self, timeout: int = 300, concurrent_requests: int = 5):
        super().__init__(timeout, concurrent_requests)

        # Modern SQL injection payloads
        self.sqli_payloads = [
            "' OR '1'='1",
            "' UNION SELECT NULL, NULL, NULL--",
            "'; DROP TABLE test; --",
            "' AND (SELECT COUNT(*) FROM information_schema.tables) > 0--",
            "' OR SLEEP(5)--",
            "' OR pg_sleep(5)--",
            "' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT version()), 0x7e))--",
            "' OR (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--",
            "'; WAITFOR DELAY '00:00:05'--",
            "' OR 1=1 AND (SELECT SUBSTRING(@@version,1,1))='5'--"
        ]

        # NoSQL injection payloads
        self.nosql_payloads = [
            '{"$ne": null}',
            '{"$gt": ""}',
            '{"$where": "this.username == this.password"}',
            '{"$regex": ".*"}',
            '{"$exists": true}',
            '{"$or": [{"username": {"$ne": null}}, {"password": {"$ne": null}}]}',
            '{"$and": [{"$where": "this.username.length > 0"}, {"$where": "this.password.length > 0"}]}',
            '{"username": {"$regex": "^admin"}, "password": {"$ne": ""}}',
            '{"$expr": {"$gt": [{"$strLenCP": "$password"}, 0]}}'
        ]

        # Cloud database service patterns
        self.cloud_db_patterns = {
            'supabase': {
                'domains': ['supabase.co', 'supabase.io'],
                'endpoints': ['/rest/v1/', '/auth/v1/', '/storage/v1/', '/realtime/v1/'],
                'headers': ['sb-', 'apikey', 'authorization'],
                'indicators': ['supabase', 'postgrest', 'gotrue']
            },
            'firebase': {
                'domains': ['firebaseio.com', 'googleapis.com', 'firebase.google.com'],
                'endpoints': ['/.well-known/assetlinks.json', '/firebase-config.js', '/__/firebase/'],
                'headers': ['x-firebase-', 'x-goog-'],
                'indicators': ['firebase', 'firestore', 'realtime database']
            },
            'mongodb_atlas': {
                'domains': ['mongodb.net', 'mongodb.com'],
                'endpoints': ['/api/atlas/', '/api/public/'],
                'headers': ['x-mongodb-', 'digest'],
                'indicators': ['mongodb', 'atlas', 'realm']
            },
            'planetscale': {
                'domains': ['planetscale.com', 'psdb.cloud'],
                'endpoints': ['/api/', '/v1/'],
                'headers': ['authorization', 'x-planetscale-'],
                'indicators': ['planetscale', 'vitess']
            },
            'neon': {
                'domains': ['neon.tech', 'neon.build'],
                'endpoints': ['/api/', '/v2/'],
                'headers': ['authorization', 'x-neon-'],
                'indicators': ['neon', 'postgres']
            },
            'railway': {
                'domains': ['railway.app', 'up.railway.app'],
                'endpoints': ['/api/', '/graphql'],
                'headers': ['authorization', 'x-railway-'],
                'indicators': ['railway']
            }
        }
        
        # Database information gathering queries
        self.info_queries = {
            'postgresql': [
                "SELECT version()",
                "SELECT current_database()",
                "SELECT current_user",
                "SELECT table_name FROM information_schema.tables WHERE table_schema='public'",
                "SELECT column_name, data_type FROM information_schema.columns WHERE table_schema='public'"
            ],
            'mysql': [
                "SELECT @@version",
                "SELECT database()",
                "SELECT user()",
                "SELECT table_name FROM information_schema.tables WHERE table_schema=database()",
                "SELECT column_name, data_type FROM information_schema.columns WHERE table_schema=database()"
            ],
            'sqlite': [
                "SELECT sqlite_version()",
                "SELECT name FROM sqlite_master WHERE type='table'",
                "PRAGMA table_info(users)"
            ]
        }

    async def scan(self, target_url: str, **kwargs) -> ScannerResult:
        """Main database security scanning method."""
        start_time = time.time()
        vulnerabilities = []

        # Extract database connection info from URL or kwargs
        db_config = kwargs.get('db_config')
        if not db_config:
            db_config = self._parse_database_url(target_url)

        # If no direct database URL, try to discover database endpoints from web URL
        if not db_config:
            discovered_configs = await self._discover_database_endpoints(target_url)
            if discovered_configs:
                # Test each discovered database configuration
                for config in discovered_configs:
                    config_vulns = await self._test_database_config(config)
                    vulnerabilities.extend(config_vulns)
            else:
                # If no database endpoints found, create a generic vulnerability about database exposure
                web_vulns = await self._test_web_database_exposure(target_url)
                vulnerabilities.extend(web_vulns)

                return ScannerResult(
                    vulnerabilities=vulnerabilities,
                    metadata={"scan_type": "web_database_exposure", "target_type": "web_url"},
                    scan_duration=time.time() - start_time,
                    timestamp=datetime.utcnow(),
                    scanner_name="database_security_checker",
                    target_url=target_url,
                    success=True,
                    error_message=None
                )
        else:
            # Test the provided database configuration
            config_vulns = await self._test_database_config(db_config)
            vulnerabilities.extend(config_vulns)
        
        try:
            # Test database connection
            connection_vulns = await self._test_connection_security(db_config)
            vulnerabilities.extend(connection_vulns)
            
            # Test for SQL injection in connection parameters
            injection_vulns = await self._test_sql_injection_in_config(db_config)
            vulnerabilities.extend(injection_vulns)
            
            # Test Row-Level Security (RLS)
            rls_vulns = await self._test_row_level_security(db_config)
            vulnerabilities.extend(rls_vulns)
            
            # Test privilege escalation
            privilege_vulns = await self._test_privilege_escalation(db_config)
            vulnerabilities.extend(privilege_vulns)
            
            # Test for exposed database information
            info_vulns = await self._test_information_disclosure(db_config)
            vulnerabilities.extend(info_vulns)
            
            scan_duration = time.time() - start_time
            
            return ScannerResult(
                vulnerabilities=vulnerabilities,
                metadata={
                    "database_type": db_config.get("type"),
                    "tests_performed": [
                        "connection_security",
                        "sql_injection", 
                        "row_level_security",
                        "privilege_escalation",
                        "information_disclosure"
                    ]
                },
                scan_duration=scan_duration,
                timestamp=datetime.utcnow(),
                scanner_name="database_security_checker",
                target_url=target_url,
                success=True
            )
            
        except Exception as e:
            scan_duration = time.time() - start_time
            self.logger.error("Database security scan failed", error=str(e))
            
            return ScannerResult(
                vulnerabilities=[],
                metadata={},
                scan_duration=scan_duration,
                timestamp=datetime.utcnow(),
                scanner_name="database_security_checker",
                target_url=target_url,
                success=False,
                error_message=str(e)
            )

    def _parse_database_url(self, url: str) -> Optional[Dict[str, Any]]:
        """Parse database URL to extract connection parameters."""
        try:
            parsed = urlparse(url)
            
            if not parsed.scheme:
                return None
            
            db_type = parsed.scheme.split('+')[0]  # Handle postgresql+asyncpg
            
            return {
                "type": db_type,
                "host": parsed.hostname or "localhost",
                "port": parsed.port or self._get_default_port(db_type),
                "database": parsed.path.lstrip('/') if parsed.path else "",
                "username": parsed.username or "",
                "password": parsed.password or "",
                "url": url
            }
        except Exception as e:
            self.logger.error("Failed to parse database URL", error=str(e))
            return None

    def _get_default_port(self, db_type: str) -> int:
        """Get default port for database type."""
        ports = {
            "postgresql": 5432,
            "mysql": 3306,
            "sqlite": 0,  # File-based
            "mongodb": 27017,
            "redis": 6379
        }
        return ports.get(db_type, 0)

    async def _test_connection_security(self, db_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Test database connection security."""
        vulnerabilities = []
        
        try:
            # Test weak credentials
            weak_passwords = ["", "password", "admin", "root", "123456", db_config["username"]]
            
            for weak_password in weak_passwords:
                if await self._test_connection(db_config, password=weak_password):
                    vuln = self.create_vulnerability(
                        title="Weak Database Credentials",
                        description=f"Database accepts weak password: '{weak_password}'",
                        severity="critical",
                        category="Authentication",
                        evidence=f"Successfully connected with password: '{weak_password}'",
                        cwe_id="CWE-521",
                        owasp_category="A07:2021 – Identification and Authentication Failures",
                        remediation="Use strong, unique passwords for database accounts"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
                    break
            
            # Test default credentials
            default_creds = [
                ("admin", "admin"), ("root", "root"), ("admin", "password"),
                ("postgres", "postgres"), ("mysql", "mysql")
            ]
            
            for username, password in default_creds:
                if await self._test_connection(db_config, username=username, password=password):
                    vuln = self.create_vulnerability(
                        title="Default Database Credentials",
                        description=f"Database uses default credentials: {username}/{password}",
                        severity="critical",
                        category="Authentication",
                        evidence=f"Connected with default credentials: {username}/{password}",
                        cwe_id="CWE-1391",
                        remediation="Change default database credentials"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
                    
        except Exception as e:
            self.logger.debug("Connection security test failed", error=str(e))
            
        return vulnerabilities

    async def _test_connection(self, db_config: Dict[str, Any], username: str = None, password: str = None) -> bool:
        """Test database connection with given credentials."""
        test_config = db_config.copy()
        if username:
            test_config["username"] = username
        if password:
            test_config["password"] = password
            
        try:
            if test_config["type"] == "postgresql":
                conn = await asyncpg.connect(
                    host=test_config["host"],
                    port=test_config["port"],
                    database=test_config["database"],
                    user=test_config["username"],
                    password=test_config["password"],
                    timeout=5
                )
                await conn.close()
                return True
                
            elif test_config["type"] == "mysql":
                conn = await aiomysql.connect(
                    host=test_config["host"],
                    port=test_config["port"],
                    db=test_config["database"],
                    user=test_config["username"],
                    password=test_config["password"],
                    connect_timeout=5
                )
                conn.close()
                return True
                
            elif test_config["type"] == "sqlite":
                async with aiosqlite.connect(test_config["database"]) as conn:
                    await conn.execute("SELECT 1")
                return True
                
        except Exception:
            return False
            
        return False

    async def _test_sql_injection_in_config(self, db_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Test for SQL injection vulnerabilities in configuration."""
        vulnerabilities = []
        
        # This would typically test application endpoints that use database queries
        # For demonstration, we'll check if the database name contains suspicious patterns
        suspicious_patterns = [
            r"[';]", r"--", r"/\*", r"\*/", r"union", r"select", r"drop", r"insert"
        ]
        
        database_name = db_config.get("database", "")
        for pattern in suspicious_patterns:
            if re.search(pattern, database_name, re.IGNORECASE):
                vuln = self.create_vulnerability(
                    title="Suspicious Database Configuration",
                    description=f"Database name contains potentially malicious pattern: {pattern}",
                    severity="medium",
                    category="Configuration",
                    evidence=f"Database name: {database_name}",
                    cwe_id="CWE-89",
                    remediation="Review database configuration for SQL injection attempts"
                )
                vulnerabilities.append(vuln)
                self.log_vulnerability(vuln)
                
        return vulnerabilities

    async def _test_row_level_security(self, db_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Test Row-Level Security (RLS) implementation."""
        vulnerabilities = []
        
        if db_config["type"] != "postgresql":
            return vulnerabilities  # RLS is primarily a PostgreSQL feature
            
        try:
            conn = await asyncpg.connect(
                host=db_config["host"],
                port=db_config["port"],
                database=db_config["database"],
                user=db_config["username"],
                password=db_config["password"],
                timeout=10
            )
            
            try:
                # Check for tables with RLS disabled
                rls_query = """
                SELECT schemaname, tablename, rowsecurity 
                FROM pg_tables 
                WHERE schemaname NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
                """
                
                rows = await conn.fetch(rls_query)
                
                for row in rows:
                    if not row['rowsecurity']:
                        # Check if table contains sensitive data patterns
                        table_name = f"{row['schemaname']}.{row['tablename']}"
                        
                        try:
                            # Get column names to check for sensitive data
                            columns_query = """
                            SELECT column_name 
                            FROM information_schema.columns 
                            WHERE table_schema = $1 AND table_name = $2
                            """
                            
                            columns = await conn.fetch(columns_query, row['schemaname'], row['tablename'])
                            column_names = [col['column_name'].lower() for col in columns]
                            
                            sensitive_patterns = [
                                'password', 'email', 'ssn', 'credit_card', 'phone',
                                'address', 'salary', 'user_id', 'customer_id'
                            ]
                            
                            has_sensitive_data = any(
                                pattern in ' '.join(column_names) 
                                for pattern in sensitive_patterns
                            )
                            
                            if has_sensitive_data:
                                vuln = self.create_vulnerability(
                                    title="Row-Level Security Not Enabled",
                                    description=f"Table {table_name} contains sensitive data but RLS is not enabled",
                                    severity="high",
                                    category="Access Control",
                                    evidence=f"Table: {table_name}, Columns: {column_names}",
                                    cwe_id="CWE-284",
                                    owasp_category="A01:2021 – Broken Access Control",
                                    remediation=f"Enable RLS on table {table_name} with appropriate policies"
                                )
                                vulnerabilities.append(vuln)
                                self.log_vulnerability(vuln)
                                
                        except Exception as e:
                            self.logger.debug("Failed to analyze table", table=table_name, error=str(e))
                            
                # Test for RLS bypass attempts
                bypass_vulns = await self._test_rls_bypass(conn)
                vulnerabilities.extend(bypass_vulns)
                
            finally:
                await conn.close()
                
        except Exception as e:
            self.logger.debug("RLS test failed", error=str(e))
            
        return vulnerabilities

    async def _test_rls_bypass(self, conn) -> List[Dict[str, Any]]:
        """Test for RLS bypass vulnerabilities."""
        vulnerabilities = []
        
        try:
            # Test if current user can disable RLS
            bypass_queries = [
                "SET row_security = off",
                "ALTER TABLE users DISABLE ROW LEVEL SECURITY",
                "SET SESSION AUTHORIZATION DEFAULT"
            ]
            
            for query in bypass_queries:
                try:
                    await conn.execute(query)
                    
                    vuln = self.create_vulnerability(
                        title="RLS Bypass Possible",
                        description=f"User can execute RLS bypass command: {query}",
                        severity="critical",
                        category="Access Control",
                        evidence=f"Successfully executed: {query}",
                        cwe_id="CWE-284",
                        remediation="Restrict permissions to modify RLS settings"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
                    
                except Exception:
                    # Good - command should fail for non-privileged users
                    pass
                    
        except Exception as e:
            self.logger.debug("RLS bypass test failed", error=str(e))
            
        return vulnerabilities

    async def _test_privilege_escalation(self, db_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Test for privilege escalation vulnerabilities."""
        vulnerabilities = []
        
        try:
            conn = await asyncpg.connect(
                host=db_config["host"],
                port=db_config["port"],
                database=db_config["database"],
                user=db_config["username"],
                password=db_config["password"],
                timeout=10
            )
            
            try:
                # Check current user privileges
                privileges_query = """
                SELECT 
                    r.rolname as role_name,
                    r.rolsuper as is_superuser,
                    r.rolcreaterole as can_create_roles,
                    r.rolcreatedb as can_create_db,
                    r.rolcanlogin as can_login
                FROM pg_roles r 
                WHERE r.rolname = current_user
                """
                
                result = await conn.fetch(privileges_query)
                
                if result:
                    role_info = result[0]
                    
                    if role_info['is_superuser']:
                        vuln = self.create_vulnerability(
                            title="Database Superuser Access",
                            description="Application is connecting with database superuser privileges",
                            severity="high",
                            category="Privilege Escalation",
                            evidence=f"Role: {role_info['role_name']} has superuser privileges",
                            cwe_id="CWE-250",
                            remediation="Use least-privilege database accounts for applications"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)
                    
                    if role_info['can_create_roles'] or role_info['can_create_db']:
                        vuln = self.create_vulnerability(
                            title="Excessive Database Privileges",
                            description="Database user has role/database creation privileges",
                            severity="medium",
                            category="Privilege Escalation",
                            evidence=f"Role: {role_info['role_name']} can create roles/databases",
                            cwe_id="CWE-250",
                            remediation="Remove unnecessary privileges from application database account"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)
                
                # Test for function execution privileges
                function_vulns = await self._test_function_execution(conn)
                vulnerabilities.extend(function_vulns)
                
            finally:
                await conn.close()
                
        except Exception as e:
            self.logger.debug("Privilege escalation test failed", error=str(e))
            
        return vulnerabilities

    async def _test_function_execution(self, conn) -> List[Dict[str, Any]]:
        """Test for dangerous function execution capabilities."""
        vulnerabilities = []
        
        dangerous_functions = [
            "pg_read_file('/etc/passwd')",
            "pg_ls_dir('/')",
            "copy (select '') to program 'id'",
            "lo_import('/etc/passwd')"
        ]
        
        for func in dangerous_functions:
            try:
                await conn.fetch(f"SELECT {func}")
                
                vuln = self.create_vulnerability(
                    title="Dangerous Function Execution",
                    description=f"User can execute dangerous function: {func}",
                    severity="critical",
                    category="Privilege Escalation",
                    evidence=f"Successfully executed: {func}",
                    cwe_id="CWE-78",
                    remediation="Restrict access to dangerous database functions"
                )
                vulnerabilities.append(vuln)
                self.log_vulnerability(vuln)
                
            except Exception:
                # Good - function should not be accessible
                pass
                
        return vulnerabilities

    async def _test_information_disclosure(self, db_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Test for information disclosure vulnerabilities."""
        vulnerabilities = []
        
        try:
            conn = await asyncpg.connect(
                host=db_config["host"],
                port=db_config["port"],
                database=db_config["database"],
                user=db_config["username"],
                password=db_config["password"],
                timeout=10
            )
            
            try:
                # Test access to information schema
                info_queries = self.info_queries.get(db_config["type"], [])
                
                for query in info_queries:
                    try:
                        result = await conn.fetch(query)
                        
                        if result:
                            # Check if results contain sensitive information
                            result_str = str(result)
                            
                            if any(sensitive in result_str.lower() for sensitive in 
                                  ['password', 'secret', 'key', 'token', 'credential']):
                                
                                vuln = self.create_vulnerability(
                                    title="Sensitive Information Disclosure",
                                    description=f"Query exposes sensitive information: {query}",
                                    severity="medium",
                                    category="Information Disclosure",
                                    evidence=result_str[:200],
                                    cwe_id="CWE-200",
                                    remediation="Restrict access to sensitive database metadata"
                                )
                                vulnerabilities.append(vuln)
                                self.log_vulnerability(vuln)
                                
                    except Exception:
                        pass  # Query failed, which is often expected
                
            finally:
                await conn.close()
                
        except Exception as e:
            self.logger.debug("Information disclosure test failed", error=str(e))
            
        return vulnerabilities

    async def _discover_database_endpoints(self, target_url: str) -> List[Dict[str, Any]]:
        """Discover potential database endpoints from a web URL."""
        discovered_configs = []

        try:
            import httpx
            # Create headers to bypass bot protection
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            }

            async with httpx.AsyncClient(timeout=10, verify=False, headers=headers, follow_redirects=True) as client:
                # Common database ports to check
                common_db_ports = {
                    5432: 'postgresql',
                    3306: 'mysql',
                    1433: 'mssql',
                    27017: 'mongodb',
                    6379: 'redis'
                }

                # Extract hostname from target URL
                from urllib.parse import urlparse
                parsed = urlparse(target_url)
                hostname = parsed.hostname or parsed.netloc.split(':')[0]

                if hostname:
                    # Test common database ports
                    for port, db_type in common_db_ports.items():
                        try:
                            # Try to connect to see if port is open
                            import socket
                            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                            sock.settimeout(2)
                            result = sock.connect_ex((hostname, port))
                            sock.close()

                            if result == 0:  # Port is open
                                discovered_configs.append({
                                    "type": db_type,
                                    "host": hostname,
                                    "port": port,
                                    "database": "",
                                    "username": "",
                                    "password": "",
                                    "url": f"{db_type}://{hostname}:{port}"
                                })
                        except Exception:
                            pass

        except Exception as e:
            self.logger.debug("Database endpoint discovery failed", error=str(e))

        return discovered_configs

    async def _test_web_database_exposure(self, target_url: str) -> List[Dict[str, Any]]:
        """Test for database-related vulnerabilities in web applications."""
        vulnerabilities = []

        try:
            import httpx
            # Create headers to bypass bot protection
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            }

            async with httpx.AsyncClient(timeout=10, verify=False, headers=headers, follow_redirects=True) as client:
                # Test for common database exposure endpoints
                db_endpoints = [
                    # Database admin interfaces
                    '/phpmyadmin', '/adminer', '/phpMyAdmin', '/pma',
                    '/db', '/database', '/mysql', '/postgres', '/postgresql',
                    '/admin/db', '/admin/database', '/admin/phpmyadmin',
                    '/dbadmin', '/db-admin', '/database-admin',
                    '/pgadmin', '/pgadmin4', '/mongo-express',
                    '/redis-commander', '/redisinsight',

                    # Configuration files
                    '/.env', '/.env.local', '/.env.production', '/.env.development',
                    '/config.php', '/database.yml', '/config/database.yml',
                    '/app/config/database.yml', '/config/config.php',
                    '/wp-config.php', '/wp-config.php.bak',
                    '/settings.php', '/local_settings.py',
                    '/appsettings.json', '/web.config',

                    # Database files
                    '/database.sqlite', '/db.sqlite3', '/app.db',
                    '/data.db', '/users.db', '/backup.sql',
                    '/dump.sql', '/database.sql', '/db.sql',
                    '/backup.zip', '/database.zip',

                    # API endpoints that might expose database info
                    '/api/config', '/api/database', '/api/db',
                    '/api/v1/config', '/api/v1/database',
                    '/graphql', '/graphiql', '/playground',
                    '/swagger', '/swagger-ui', '/api-docs',

                    # Common backup and temp files
                    '/config.bak', '/database.bak', '/db.bak',
                    '/config.old', '/database.old', '/db.old',
                    '/config.txt', '/database.txt', '/credentials.txt'
                ]

                # Test each endpoint
                for endpoint in db_endpoints:
                    try:
                        test_url = target_url.rstrip('/') + endpoint
                        response = await client.get(test_url)

                        if response.status_code == 200:
                            # Check if this is a valid response or SPA fallback
                            if self._is_spa_fallback(response, endpoint):
                                continue

                            content = response.text.lower()

                            # Check for database admin interfaces
                            if any(indicator in content for indicator in [
                                'phpmyadmin', 'adminer', 'database administration',
                                'mysql', 'postgresql', 'database login', 'pgadmin',
                                'mongo-express', 'redis-commander', 'redisinsight'
                            ]):
                                vuln = self.create_vulnerability(
                                    title="Database Administration Interface Exposed",
                                    description=f"Database admin interface found at {test_url}",
                                    severity="high",
                                    category="Information Disclosure",
                                    url=test_url,
                                    evidence=f"Found database admin interface: {endpoint}",
                                    cwe_id="CWE-200",
                                    remediation="Restrict access to database administration interfaces"
                                )
                                vulnerabilities.append(vuln)
                                self.log_vulnerability(vuln)

                            # Check for configuration files with database credentials
                            elif any(indicator in content for indicator in [
                                'database_password', 'db_password', 'mysql_password',
                                'postgres_password', 'connection_string', 'database_url',
                                'db_host', 'db_user', 'db_name', 'mongodb_uri'
                            ]):
                                vuln = self.create_vulnerability(
                                    title="Database Configuration Exposed",
                                    description=f"Database configuration file exposed at {test_url}",
                                    severity="critical",
                                    category="Information Disclosure",
                                    url=test_url,
                                    evidence=f"Found database configuration: {endpoint}",
                                    cwe_id="CWE-200",
                                    remediation="Remove or protect database configuration files"
                                )
                                vulnerabilities.append(vuln)
                                self.log_vulnerability(vuln)

                            # Check for GraphQL introspection
                            elif 'graphql' in endpoint and any(indicator in content for indicator in [
                                '__schema', '__type', 'introspection', 'query', 'mutation'
                            ]):
                                vuln = self.create_vulnerability(
                                    title="GraphQL Introspection Enabled",
                                    description=f"GraphQL introspection is enabled at {test_url}",
                                    severity="medium",
                                    category="Information Disclosure",
                                    url=test_url,
                                    evidence="GraphQL introspection reveals database schema",
                                    cwe_id="CWE-200",
                                    remediation="Disable GraphQL introspection in production"
                                )
                                vulnerabilities.append(vuln)
                                self.log_vulnerability(vuln)

                            # Check for API documentation that might reveal database structure
                            elif any(indicator in content for indicator in [
                                'swagger', 'openapi', 'api documentation', 'endpoints'
                            ]):
                                if any(db_term in content for db_term in [
                                    'database', 'table', 'schema', 'query', 'sql'
                                ]):
                                    vuln = self.create_vulnerability(
                                        title="API Documentation Exposes Database Information",
                                        description=f"API documentation at {test_url} reveals database structure",
                                        severity="low",
                                        category="Information Disclosure",
                                        url=test_url,
                                        evidence="API documentation contains database-related information",
                                        cwe_id="CWE-200",
                                        remediation="Review API documentation for sensitive information"
                                    )
                                    vulnerabilities.append(vuln)
                                    self.log_vulnerability(vuln)

                        # Check for directory listing that might expose database files
                        elif response.status_code == 403:
                            # Try to access parent directory
                            parent_url = '/'.join(test_url.split('/')[:-1]) + '/'
                            try:
                                parent_response = await client.get(parent_url)
                                if parent_response.status_code == 200 and 'index of' in parent_response.text.lower():
                                    if any(db_file in parent_response.text.lower() for db_file in [
                                        '.sql', '.db', '.sqlite', 'database', 'backup'
                                    ]):
                                        vuln = self.create_vulnerability(
                                            title="Directory Listing Exposes Database Files",
                                            description=f"Directory listing at {parent_url} exposes database files",
                                            severity="medium",
                                            category="Information Disclosure",
                                            url=parent_url,
                                            evidence="Directory listing contains database-related files",
                                            cwe_id="CWE-200",
                                            remediation="Disable directory listing and protect database files"
                                        )
                                        vulnerabilities.append(vuln)
                                        self.log_vulnerability(vuln)
                            except Exception:
                                pass

                    except Exception:
                        pass

                # Test for SQL injection in common parameters
                sqli_vulns = await self._test_sql_injection_web(client, target_url)
                vulnerabilities.extend(sqli_vulns)

                # Test for NoSQL injection
                nosql_vulns = await self._test_nosql_injection_web(client, target_url)
                vulnerabilities.extend(nosql_vulns)

                # Test for Supabase-specific vulnerabilities
                supabase_vulns = await self._test_supabase_security(client, target_url)
                vulnerabilities.extend(supabase_vulns)

                # Test for Firebase/Firestore vulnerabilities
                firebase_vulns = await self._test_firebase_security(client, target_url)
                vulnerabilities.extend(firebase_vulns)

                # Test for GraphQL vulnerabilities
                graphql_vulns = await self._test_graphql_security(client, target_url)
                vulnerabilities.extend(graphql_vulns)

        except Exception as e:
            self.logger.debug("Web database exposure test failed", error=str(e))

        return vulnerabilities

    def _is_spa_fallback(self, response, endpoint: str) -> bool:
        """
        Simple SPA detection to prevent false positives.
        Returns True if the response appears to be SPA fallback content.
        """
        content_type = response.headers.get('content-type', '').lower()
        content_disposition = response.headers.get('content-disposition', '').lower()
        content = response.text

        # 1. If we're requesting a specific file but get HTML, it's likely SPA fallback
        if 'text/html' in content_type and any(endpoint.endswith(ext) for ext in [
            '.json', '.env', '.sql', '.config', '.ini', '.cfg', '.xml', '.yml', '.yaml'
        ]):
            return True

        # 2. Check content-disposition for index.html when requesting other files
        if 'filename="index.html"' in content_disposition and not endpoint.endswith('.html'):
            return True

        # 3. Check if content starts with HTML when we expect other formats
        content_start = content.strip()[:200].lower()
        html_indicators = ['<!doctype html', '<html', '<head>', '<body>', '<meta', '<title>']
        if any(indicator in content_start for indicator in html_indicators):
            # If we're requesting a non-HTML endpoint but getting HTML, it's likely SPA
            if any(endpoint.endswith(ext) for ext in ['.json', '.env', '.sql', '.config']):
                return True

        # 4. Check for common SPA framework indicators
        spa_indicators = ['react', 'vue', 'angular', 'next.js', 'nuxt', 'gatsby']
        content_lower = content.lower()
        if any(indicator in content_lower for indicator in spa_indicators):
            # If content contains SPA indicators and we're requesting config files
            if any(endpoint.endswith(ext) for ext in ['.env', '.json', '.config', '.sql']):
                return True

        return False

    async def _test_sql_injection_web(self, client, target_url: str) -> List[Dict[str, Any]]:
        """Test for SQL injection vulnerabilities in web application."""
        vulnerabilities = []

        try:
            # Common SQL injection payloads
            sql_payloads = [
                "' OR '1'='1",
                "' OR 1=1--",
                "' UNION SELECT NULL--",
                "'; DROP TABLE users--",
                "' AND (SELECT COUNT(*) FROM information_schema.tables)>0--"
            ]

            # Common parameters to test
            test_params = ['id', 'user', 'search', 'q', 'query', 'name', 'email']

            for param in test_params:
                for payload in sql_payloads:
                    try:
                        # Test GET request
                        test_url = f"{target_url}?{param}={payload}"
                        response = await client.get(test_url)

                        # Look for SQL error messages
                        if any(error in response.text.lower() for error in [
                            'sql syntax', 'mysql_fetch', 'ora-', 'postgresql',
                            'sqlite_', 'sqlstate', 'syntax error', 'mysql error'
                        ]):
                            vuln = self.create_vulnerability(
                                title="SQL Injection Vulnerability",
                                description=f"SQL injection detected in parameter '{param}'",
                                severity="high",
                                category="Injection",
                                url=test_url,
                                parameter=param,
                                payload=payload,
                                evidence="SQL error message detected in response",
                                cwe_id="CWE-89",
                                owasp_category="A03:2021 – Injection",
                                remediation="Use parameterized queries and input validation"
                            )
                            vulnerabilities.append(vuln)
                            self.log_vulnerability(vuln)
                            break  # Don't test more payloads for this parameter

                    except Exception:
                        pass

        except Exception as e:
            self.logger.debug("SQL injection test failed", error=str(e))

        return vulnerabilities

    async def _test_nosql_injection_web(self, client, target_url: str) -> List[Dict[str, Any]]:
        """Test for NoSQL injection vulnerabilities."""
        vulnerabilities = []

        try:
            # MongoDB injection payloads
            nosql_payloads = [
                '{"$ne": null}',
                '{"$gt": ""}',
                '{"$where": "this.username == this.password"}',
                '{"$regex": ".*"}',
                '{"$exists": true}'
            ]

            test_params = ['id', 'user', 'search', 'filter', 'query']

            for param in test_params:
                for payload in nosql_payloads:
                    try:
                        # Test with JSON payload
                        test_url = f"{target_url}?{param}={payload}"
                        response = await client.get(test_url)

                        # Look for MongoDB error messages or unexpected behavior
                        if any(error in response.text.lower() for error in [
                            'mongodb', 'bson', 'objectid', 'mongoose',
                            'invalid bson', 'cast to objectid failed'
                        ]):
                            vuln = self.create_vulnerability(
                                title="NoSQL Injection Vulnerability",
                                description=f"NoSQL injection detected in parameter '{param}'",
                                severity="high",
                                category="Injection",
                                url=test_url,
                                parameter=param,
                                payload=payload,
                                evidence="NoSQL error message detected in response",
                                cwe_id="CWE-943",
                                remediation="Use proper input validation and sanitization for NoSQL queries"
                            )
                            vulnerabilities.append(vuln)
                            self.log_vulnerability(vuln)
                            break

                    except Exception:
                        pass

        except Exception as e:
            self.logger.debug("NoSQL injection test failed", error=str(e))

        return vulnerabilities

    async def _test_supabase_security(self, client, target_url: str) -> List[Dict[str, Any]]:
        """Test for Supabase-specific security vulnerabilities."""
        vulnerabilities = []

        try:
            # Common Supabase endpoints to test
            supabase_endpoints = [
                '/rest/v1/',
                '/auth/v1/',
                '/storage/v1/',
                '/realtime/v1/',
                '/functions/v1/',
                '/rest/v1/rpc/',
                '/rest/v1/users',
                '/rest/v1/profiles',
                '/auth/v1/signup',
                '/auth/v1/token',
                '/auth/v1/user',
                '/auth/v1/logout'
            ]

            # Test each Supabase endpoint
            for endpoint in supabase_endpoints:
                try:
                    test_url = target_url.rstrip('/') + endpoint

                    # Test without authentication
                    response = await client.get(test_url)

                    if response.status_code == 200:
                        content = response.text.lower()

                        # Check if it's actually Supabase
                        if any(indicator in content for indicator in [
                            'supabase', 'postgrest', 'gotrue', 'realtime'
                        ]) or any(header.startswith('sb-') for header in response.headers.keys()):

                            # Check for unauthenticated access to data
                            if endpoint.startswith('/rest/v1/') and endpoint != '/rest/v1/':
                                if 'select' in response.headers.get('allow', '').lower() or \
                                   any(data_indicator in content for data_indicator in [
                                       '"id":', '"email":', '"user_id":', '"created_at":',
                                       '"updated_at":', '[{', 'rows'
                                   ]):
                                    vuln = self.create_vulnerability(
                                        title="Supabase Table Accessible Without Authentication",
                                        description=f"Supabase table endpoint {endpoint} is accessible without authentication",
                                        severity="high",
                                        category="Access Control",
                                        url=test_url,
                                        evidence=f"Unauthenticated access to: {endpoint}",
                                        cwe_id="CWE-284",
                                        owasp_category="A01:2021 – Broken Access Control",
                                        remediation="Enable Row Level Security (RLS) and proper authentication"
                                    )
                                    vulnerabilities.append(vuln)
                                    self.log_vulnerability(vuln)

                            # Check for exposed auth endpoints
                            elif endpoint.startswith('/auth/v1/'):
                                if endpoint in ['/auth/v1/signup', '/auth/v1/token'] and response.status_code == 200:
                                    vuln = self.create_vulnerability(
                                        title="Supabase Auth Endpoint Exposed",
                                        description=f"Supabase auth endpoint {endpoint} is publicly accessible",
                                        severity="medium",
                                        category="Information Disclosure",
                                        url=test_url,
                                        evidence=f"Auth endpoint accessible: {endpoint}",
                                        cwe_id="CWE-200",
                                        remediation="Review auth endpoint configuration and access controls"
                                    )
                                    vulnerabilities.append(vuln)
                                    self.log_vulnerability(vuln)

                    # Test for RLS bypass attempts
                    elif response.status_code == 401 or response.status_code == 403:
                        # Try with common bypass techniques
                        bypass_headers = {
                            'Authorization': 'Bearer invalid',
                            'apikey': 'invalid',
                            'X-Client-Info': 'supabase-js/1.0.0',
                            'Prefer': 'return=minimal'
                        }

                        bypass_response = await client.get(test_url, headers=bypass_headers)
                        if bypass_response.status_code == 200:
                            vuln = self.create_vulnerability(
                                title="Supabase Authentication Bypass",
                                description=f"Authentication bypass possible on {endpoint}",
                                severity="critical",
                                category="Authentication",
                                url=test_url,
                                evidence="Authentication bypass with invalid credentials",
                                cwe_id="CWE-287",
                                remediation="Fix authentication validation logic"
                            )
                            vulnerabilities.append(vuln)
                            self.log_vulnerability(vuln)

                except Exception:
                    pass

            # Test for Supabase introspection
            introspection_endpoints = [
                '/rest/v1/',
                '/rest/v1/?select=*',
                '/rest/v1/rpc/'
            ]

            for endpoint in introspection_endpoints:
                try:
                    test_url = target_url.rstrip('/') + endpoint
                    response = await client.get(test_url)

                    if response.status_code == 200:
                        content = response.text.lower()

                        # Check for schema information disclosure
                        if any(schema_indicator in content for schema_indicator in [
                            'table_name', 'column_name', 'data_type', 'is_nullable',
                            'table_schema', 'information_schema', 'pg_catalog'
                        ]):
                            vuln = self.create_vulnerability(
                                title="Supabase Schema Information Disclosure",
                                description=f"Database schema information exposed at {endpoint}",
                                severity="medium",
                                category="Information Disclosure",
                                url=test_url,
                                evidence="Database schema information accessible",
                                cwe_id="CWE-200",
                                remediation="Restrict access to schema introspection endpoints"
                            )
                            vulnerabilities.append(vuln)
                            self.log_vulnerability(vuln)

                except Exception:
                    pass

        except Exception as e:
            self.logger.debug("Supabase security test failed", error=str(e))

        return vulnerabilities

    async def _test_firebase_security(self, client, target_url: str) -> List[Dict[str, Any]]:
        """Test for Firebase/Firestore security vulnerabilities."""
        vulnerabilities = []

        try:
            # Firebase/Firestore endpoints to test
            firebase_endpoints = [
                '/.well-known/assetlinks.json',
                '/firebase-config.js',
                '/firebase-messaging-sw.js',
                '/__/firebase/init.js',
                '/__/firebase/init.json'
            ]

            for endpoint in firebase_endpoints:
                try:
                    test_url = target_url.rstrip('/') + endpoint
                    response = await client.get(test_url)

                    if response.status_code == 200:
                        content = response.text.lower()

                        # Check for Firebase configuration exposure
                        if any(firebase_indicator in content for firebase_indicator in [
                            'firebase', 'firestore', 'apikey', 'authdomain',
                            'projectid', 'storagebucket', 'messagingsenderid'
                        ]):
                            # Check if API keys are exposed
                            if 'apikey' in content and any(key_pattern in content for key_pattern in [
                                'aiza', 'firebase', 'google'
                            ]):
                                vuln = self.create_vulnerability(
                                    title="Firebase API Key Exposed",
                                    description=f"Firebase API key exposed in {endpoint}",
                                    severity="medium",
                                    category="Information Disclosure",
                                    url=test_url,
                                    evidence="Firebase API key found in client-side code",
                                    cwe_id="CWE-200",
                                    remediation="Use environment variables and restrict API key usage"
                                )
                                vulnerabilities.append(vuln)
                                self.log_vulnerability(vuln)

                except Exception:
                    pass

        except Exception as e:
            self.logger.debug("Firebase security test failed", error=str(e))

        return vulnerabilities

    async def _test_graphql_security(self, client, target_url: str) -> List[Dict[str, Any]]:
        """Test for GraphQL security vulnerabilities."""
        vulnerabilities = []

        try:
            # Common GraphQL endpoints
            graphql_endpoints = [
                '/graphql',
                '/graphiql',
                '/playground',
                '/api/graphql',
                '/v1/graphql',
                '/query',
                '/gql'
            ]

            for endpoint in graphql_endpoints:
                try:
                    test_url = target_url.rstrip('/') + endpoint

                    # Test for introspection
                    introspection_query = {
                        "query": "query IntrospectionQuery { __schema { queryType { name } } }"
                    }

                    response = await client.post(test_url, json=introspection_query)

                    if response.status_code == 200:
                        content = response.text.lower()

                        # Check if introspection is enabled
                        if any(introspection_indicator in content for introspection_indicator in [
                            '__schema', '__type', 'querytype', 'mutationtype'
                        ]):
                            vuln = self.create_vulnerability(
                                title="GraphQL Introspection Enabled",
                                description=f"GraphQL introspection is enabled at {endpoint}",
                                severity="medium",
                                category="Information Disclosure",
                                url=test_url,
                                evidence="GraphQL introspection reveals database schema",
                                cwe_id="CWE-200",
                                remediation="Disable GraphQL introspection in production"
                            )
                            vulnerabilities.append(vuln)
                            self.log_vulnerability(vuln)

                        # Test for query depth/complexity attacks
                        deep_query = {
                            "query": "query { " + "user { posts { comments { user { posts { comments { id } } } } } } " * 5 + "}"
                        }

                        deep_response = await client.post(test_url, json=deep_query)
                        if deep_response.status_code == 200 and len(deep_response.text) > 1000:
                            vuln = self.create_vulnerability(
                                title="GraphQL Query Depth Attack Possible",
                                description=f"GraphQL endpoint {endpoint} vulnerable to deep query attacks",
                                severity="medium",
                                category="Denial of Service",
                                url=test_url,
                                evidence="Deep nested queries not properly limited",
                                cwe_id="CWE-400",
                                remediation="Implement query depth and complexity limits"
                            )
                            vulnerabilities.append(vuln)
                            self.log_vulnerability(vuln)

                except Exception:
                    pass

        except Exception as e:
            self.logger.debug("GraphQL security test failed", error=str(e))

        return vulnerabilities

    async def _test_database_config(self, db_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Test a specific database configuration for vulnerabilities."""
        vulnerabilities = []

        try:
            # Test database connection
            connection_vulns = await self._test_connection_security(db_config)
            vulnerabilities.extend(connection_vulns)

            # Test for SQL injection in connection parameters
            injection_vulns = await self._test_sql_injection_in_config(db_config)
            vulnerabilities.extend(injection_vulns)

            # Test Row-Level Security (RLS)
            rls_vulns = await self._test_row_level_security(db_config)
            vulnerabilities.extend(rls_vulns)

            # Test privilege escalation
            privilege_vulns = await self._test_privilege_escalation(db_config)
            vulnerabilities.extend(privilege_vulns)

            # Test for exposed database information
            info_vulns = await self._test_information_disclosure(db_config)
            vulnerabilities.extend(info_vulns)

        except Exception as e:
            self.logger.debug("Database config test failed", error=str(e))

        return vulnerabilities