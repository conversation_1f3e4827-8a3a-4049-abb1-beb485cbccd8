import asyncio
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from .base_scanner import Base<PERSON>canner, ScannerResult
from .api_endpoint_scanner import APIEndpointScanner
from .database_security_checker import <PERSON><PERSON>ec<PERSON><PERSON>he<PERSON>
from .secrets_scanner import Secrets<PERSON>canner
from .firebase_auditor import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .webhook_validator import WebhookValidator


@dataclass
class ComprehensiveResult:
    """Result from comprehensive scanner containing all sub-scanner results."""
    all_vulnerabilities: List[Dict[str, Any]]
    scanner_results: Dict[str, ScannerResult]
    total_scan_duration: float
    success: bool
    errors: List[str]


class ComprehensiveScanner(BaseScanner):
    """Comprehensive scanner that runs multiple specialized scanners."""
    
    def __init__(self, timeout: int = 300, concurrent_requests: int = 10):
        super().__init__(timeout, concurrent_requests)
        self.scanner_name = "Comprehensive Scanner"
        
        # Initialize all scanners
        self.scanners = {
            'api_endpoints': APIEndpointScanner(timeout, concurrent_requests),
            'database_security': <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(timeout, concurrent_requests),
            'secrets': SecretsScanner(timeout, concurrent_requests),
            'firebase': FirebaseAuditor(timeout, concurrent_requests),
            'webhooks': WebhookValidator(timeout, concurrent_requests)
        }
    
    async def scan(self, target_url: str, **kwargs) -> ScannerResult:
        """Run all scanners and aggregate results."""
        start_time = time.time()
        all_vulnerabilities = []
        scanner_results = {}
        errors = []
        
        self.logger.info("Starting comprehensive scan", 
                        target_url=target_url, 
                        scanners=list(self.scanners.keys()))
        
        # Run all scanners concurrently
        tasks = []
        for scanner_name, scanner in self.scanners.items():
            task = self._run_scanner_safely(scanner_name, scanner, target_url, **kwargs)
            tasks.append(task)
        
        # Wait for all scanners to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for i, result in enumerate(results):
            scanner_name = list(self.scanners.keys())[i]
            
            if isinstance(result, Exception):
                error_msg = f"Scanner '{scanner_name}' failed: {str(result)}"
                errors.append(error_msg)
                self.logger.error("Scanner failed", 
                                scanner=scanner_name, 
                                error=str(result))
            elif result:
                scanner_results[scanner_name] = result
                all_vulnerabilities.extend(result.vulnerabilities)
                self.logger.info("Scanner completed", 
                               scanner=scanner_name,
                               vulnerabilities_found=len(result.vulnerabilities))
        
        total_duration = time.time() - start_time
        
        # Create comprehensive metadata
        metadata = {
            'scanners_run': list(self.scanners.keys()),
            'successful_scanners': list(scanner_results.keys()),
            'failed_scanners': [scanner for scanner in self.scanners.keys() 
                              if scanner not in scanner_results],
            'total_scanners': len(self.scanners),
            'success_rate': len(scanner_results) / len(self.scanners),
            'individual_durations': {name: result.scan_duration 
                                   for name, result in scanner_results.items()},
            'errors': errors,
            'scan_summary': {
                'total_vulnerabilities': len(all_vulnerabilities),
                'vulnerabilities_by_scanner': {
                    name: len(result.vulnerabilities) 
                    for name, result in scanner_results.items()
                },
                'severity_distribution': self._get_severity_distribution(all_vulnerabilities)
            }
        }
        
        success = len(scanner_results) > 0  # At least one scanner succeeded
        
        self.logger.info("Comprehensive scan completed",
                        target_url=target_url,
                        total_vulnerabilities=len(all_vulnerabilities),
                        successful_scanners=len(scanner_results),
                        failed_scanners=len(errors),
                        duration=total_duration)
        
        return ScannerResult(
            vulnerabilities=all_vulnerabilities,
            metadata=metadata,
            scan_duration=total_duration,
            timestamp=datetime.utcnow(),
            scanner_name=self.scanner_name,
            target_url=target_url,
            success=success,
            error_message='; '.join(errors) if errors else None
        )
    
    async def _run_scanner_safely(self, scanner_name: str, scanner: BaseScanner, 
                                target_url: str, **kwargs) -> Optional[ScannerResult]:
        """Run a single scanner with error handling."""
        try:
            self.logger.info("Starting scanner", scanner=scanner_name, target_url=target_url)
            result = await scanner.scan(target_url, **kwargs)
            return result
        except Exception as e:
            self.logger.error("Scanner failed", scanner=scanner_name, error=str(e))
            raise e
    
    def _get_severity_distribution(self, vulnerabilities: List[Dict[str, Any]]) -> Dict[str, int]:
        """Calculate severity distribution of vulnerabilities."""
        distribution = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0}
        
        for vuln in vulnerabilities:
            severity = vuln.get('severity', '').lower()
            if severity in distribution:
                distribution[severity] += 1
        
        return distribution