import asyncio
import re
import base64
import json
from typing import List, Dict, Any, Optional, Set
from datetime import datetime
import time
import httpx
import os
import tempfile
import subprocess
from urllib.parse import urlparse
from .base_scanner import BaseScanner, ScannerResult


class SecretsScanner(BaseScanner):
    """Scanner for detecting exposed secrets and credentials."""
    
    def __init__(self, timeout: int = 300, concurrent_requests: int = 10):
        super().__init__(timeout, concurrent_requests)
        
        # Secret patterns with regex
        self.secret_patterns = {
            # API Keys
            'aws_access_key': r'AKIA[0-9A-Z]{16}',
            'aws_secret_key': r'aws_secret_access_key\s*=\s*[\'"][0-9a-zA-Z/+]{40}[\'"]',
            'github_token': r'ghp_[a-zA-Z0-9]{36}',
            'github_oauth': r'gho_[a-zA-Z0-9]{36}',
            'github_app_token': r'(ghu|ghs)_[a-zA-Z0-9]{36}',
            'slack_token': r'xox[baprs]-([0-9a-zA-Z]{10,48})',
            'slack_webhook': r'https://hooks\.slack\.com/services/T[a-zA-Z0-9_]{8}/B[a-zA-Z0-9_]{8}/[a-zA-Z0-9_]{24}',
            'discord_webhook': r'https://discord(app)?\.com/api/webhooks/[0-9]{17,19}/[a-zA-Z0-9\-_]{68}',
            'stripe_key': r'sk_live_[0-9a-zA-Z]{24}',
            'stripe_restricted_key': r'rk_live_[0-9a-zA-Z]{24}',
            'paypal_braintree_access_token': r'access_token\$production\$[0-9a-z]{16}\$[0-9a-f]{32}',
            'square_oauth_secret': r'sq0csp-[0-9A-Za-z\-_]{43}',
            'square_access_token': r'sq0atp-[0-9A-Za-z\-_]{22}',
            'twilio_api_key': r'SK[a-z0-9]{32}',
            'mailgun_api_key': r'key-[0-9a-zA-Z]{32}',
            'mailchimp_api_key': r'[0-9a-f]{32}-us[0-9]{1,2}',
            'google_api_key': r'AIza[0-9A-Za-z\-_]{35}',
            'google_oauth': r'ya29\.[0-9A-Za-z\-_]+',
            'facebook_oauth': r'EAA[0-9A-Za-z]+',
            'twitter_oauth': r'[tT][wW][iI][tT][tT][eE][rR].*[\'|\"][0-9a-zA-Z]{35,44}[\'|\"]',
            'linkedin_oauth': r'linkedin(.{0,20})?[\'\"\\s][0-9a-z]{16}[\'\"\\s]',
            
            # Database Connection Strings
            'postgres_url': r'postgres://[a-zA-Z0-9_\-\.]+:[a-zA-Z0-9_\-\.]+@[a-zA-Z0-9_\-\.]+:\d+/[a-zA-Z0-9_\-\.]+',
            'mysql_url': r'mysql://[a-zA-Z0-9_\-\.]+:[a-zA-Z0-9_\-\.]+@[a-zA-Z0-9_\-\.]+:\d+/[a-zA-Z0-9_\-\.]+',
            'mongodb_url': r'mongodb://[a-zA-Z0-9_\-\.]+:[a-zA-Z0-9_\-\.]+@[a-zA-Z0-9_\-\.]+:\d+/[a-zA-Z0-9_\-\.]+',
            'redis_url': r'redis://[a-zA-Z0-9_\-\.]+:[a-zA-Z0-9_\-\.]+@[a-zA-Z0-9_\-\.]+:\d+',
            
            # Generic Patterns
            'private_key': r'-----BEGIN (RSA |EC |DSA |OPENSSH |PGP )?PRIVATE KEY-----',
            'jwt_token': r'eyJ[A-Za-z0-9_-]*\.[A-Za-z0-9._-]*\.[A-Za-z0-9._-]*',
            'password_in_url': r'[a-zA-Z]{3,10}://[^/\\s:@]{3,20}:[^/\\s:@]{3,20}@.{1,100}["\'\\s]',
            'generic_api_key': r'[\'\"](api_key|apikey|secret|password|passwd|pwd)[\'\"]\s*[:=]\s*[\'\"]{1}[^\s\'\"]{8,}[\'\"]{1}',
            'generic_secret': r'[\'\"](secret|password|passwd|pwd|token)[\'\"]\s*[:=]\s*[\'\"]{1}[^\s\'\"]{8,}[\'\"]{1}',
            
            # Cloud Services
            'azure_storage_key': r'DefaultEndpointsProtocol=https;AccountName=[a-zA-Z0-9]+;AccountKey=[a-zA-Z0-9+/=]+;',
            'gcp_service_account': r'"type": "service_account"',
            'heroku_api_key': r'[h|H][e|E][r|R][o|O][k|K][u|U].*[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}',
            
            # Crypto
            'bitcoin_address': r'[13][a-km-zA-HJ-NP-Z1-9]{25,34}',
            'ethereum_address': r'0x[a-fA-F0-9]{40}',
            
            # Common passwords and keys
            'hardcoded_password': r'password\s*=\s*[\'"][^\'"\s]{4,}[\'"]',
            'base64_encoded': r'[\'"][A-Za-z0-9+/]{20,}={0,2}[\'"]'  # Potential base64 encoded secrets
        }
        
        # High entropy indicators
        self.entropy_threshold = 4.5
        
        # File extensions to scan
        self.scannable_extensions = {
            '.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.php', '.rb', '.go',
            '.c', '.cpp', '.cs', '.swift', '.kt', '.scala', '.r', '.m', '.h',
            '.json', '.yaml', '.yml', '.xml', '.ini', '.cfg', '.conf', '.config',
            '.env', '.properties', '.toml', '.sql', '.sh', '.bat', '.ps1',
            '.html', '.htm', '.css', '.scss', '.sass', '.less', '.vue',
            '.dockerfile', '.docker-compose.yml', '.k8s.yml', '.terraform'
        }

    async def scan(self, target_url: str, **kwargs) -> ScannerResult:
        """Main secrets scanning method."""
        start_time = time.time()
        vulnerabilities = []
        
        try:
            # Create headers to bypass bot protection
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0'
            }

            async with httpx.AsyncClient(
                timeout=self.timeout,
                limits=httpx.Limits(max_connections=self.concurrent_requests),
                headers=headers,
                follow_redirects=True,
                verify=False
            ) as client:
                
                # Method 1: Scan web responses
                web_vulns = await self._scan_web_responses(client, target_url)
                vulnerabilities.extend(web_vulns)
                
                # Method 2: Scan source code if repository URL provided
                repo_url = kwargs.get('repository_url')
                if repo_url:
                    repo_vulns = await self._scan_repository(repo_url)
                    vulnerabilities.extend(repo_vulns)
                
                # Method 3: Scan common files
                file_vulns = await self._scan_common_files(client, target_url)
                vulnerabilities.extend(file_vulns)
                
                # Method 4: Scan JavaScript files
                js_vulns = await self._scan_javascript_files(client, target_url)
                vulnerabilities.extend(js_vulns)

                # Method 5: Discover and scan additional files
                discovered_vulns = await self._discover_and_scan_files(client, target_url)
                vulnerabilities.extend(discovered_vulns)

                # Method 6: Scan for exposed source maps
                sourcemap_vulns = await self._scan_source_maps(client, target_url)
                vulnerabilities.extend(sourcemap_vulns)

                # Method 7: Test found secrets for validity
                await self._validate_secrets(client, vulnerabilities)
            
            scan_duration = time.time() - start_time
            
            return ScannerResult(
                vulnerabilities=vulnerabilities,
                metadata={
                    "patterns_tested": len(self.secret_patterns),
                    "scan_methods": ["web_responses", "source_code", "common_files", "javascript_analysis"],
                    "entropy_threshold": self.entropy_threshold
                },
                scan_duration=scan_duration,
                timestamp=datetime.utcnow(),
                scanner_name="secrets_scanner",
                target_url=target_url,
                success=True
            )
            
        except Exception as e:
            scan_duration = time.time() - start_time
            self.logger.error("Secrets scan failed", error=str(e))
            
            return ScannerResult(
                vulnerabilities=[],
                metadata={},
                scan_duration=scan_duration,
                timestamp=datetime.utcnow(),
                scanner_name="secrets_scanner",
                target_url=target_url,
                success=False,
                error_message=str(e)
            )

    async def _scan_web_responses(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Scan web responses for exposed secrets."""
        vulnerabilities = []
        
        try:
            response = await client.get(target_url)
            
            if response.status_code == 200:
                secrets = self._find_secrets_in_text(response.text, target_url)
                vulnerabilities.extend(secrets)
                
                # Also check response headers
                header_secrets = self._find_secrets_in_headers(response.headers, target_url)
                vulnerabilities.extend(header_secrets)
                
        except Exception as e:
            self.logger.debug("Web response scan failed", error=str(e))
            
        return vulnerabilities

    async def _scan_repository(self, repo_url: str) -> List[Dict[str, Any]]:
        """Scan Git repository for secrets."""
        vulnerabilities = []
        
        try:
            # Clone repository to temporary directory
            with tempfile.TemporaryDirectory() as temp_dir:
                # Clone repository
                result = subprocess.run([
                    'git', 'clone', '--depth', '50', repo_url, temp_dir
                ], capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    # Scan files in repository
                    repo_vulns = await self._scan_directory(temp_dir, repo_url)
                    vulnerabilities.extend(repo_vulns)
                    
                    # Scan git history
                    history_vulns = await self._scan_git_history(temp_dir, repo_url)
                    vulnerabilities.extend(history_vulns)
                    
        except Exception as e:
            self.logger.debug("Repository scan failed", error=str(e))
            
        return vulnerabilities

    async def _scan_directory(self, directory: str, source_url: str) -> List[Dict[str, Any]]:
        """Scan directory for secret files."""
        vulnerabilities = []
        
        try:
            for root, dirs, files in os.walk(directory):
                # Skip hidden directories and common ignore patterns
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in {
                    'node_modules', '__pycache__', 'venv', 'env', '.git', 'build', 'dist'
                }]
                
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    # Check file extension
                    _, ext = os.path.splitext(file.lower())
                    if ext in self.scannable_extensions or file.lower() in {
                        '.env', '.env.local', '.env.production', 'config.json',
                        'secrets.json', 'credentials.json', 'auth.json'
                    }:
                        file_vulns = await self._scan_file(file_path, source_url)
                        vulnerabilities.extend(file_vulns)
                        
        except Exception as e:
            self.logger.debug("Directory scan failed", error=str(e))
            
        return vulnerabilities

    async def _scan_file(self, file_path: str, source_url: str) -> List[Dict[str, Any]]:
        """Scan individual file for secrets."""
        vulnerabilities = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            relative_path = os.path.relpath(file_path)
            file_url = f"{source_url}#{relative_path}"
            
            secrets = self._find_secrets_in_text(content, file_url, file_path=relative_path)
            vulnerabilities.extend(secrets)
            
        except Exception as e:
            self.logger.debug("File scan failed", file=file_path, error=str(e))
            
        return vulnerabilities

    async def _scan_git_history(self, repo_dir: str, repo_url: str) -> List[Dict[str, Any]]:
        """Scan Git commit history for secrets."""
        vulnerabilities = []
        
        try:
            # Get commit diffs
            result = subprocess.run([
                'git', 'log', '--pretty=format:%H|%s', '--name-only', '-p', '--all'
            ], cwd=repo_dir, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                commits_data = result.stdout
                secrets = self._find_secrets_in_text(commits_data, f"{repo_url}#git-history")
                
                # Mark as git history secrets
                for secret in secrets:
                    secret['evidence'] = f"Found in Git history: {secret.get('evidence', '')}"
                    secret['category'] = 'Git History Exposure'
                    
                vulnerabilities.extend(secrets)
                
        except Exception as e:
            self.logger.debug("Git history scan failed", error=str(e))
            
        return vulnerabilities

    async def _scan_common_files(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Scan common files that might contain secrets."""
        vulnerabilities = []
        
        common_files = [
            '.env', '.env.local', '.env.production', '.env.development',
            'config.json', 'secrets.json', 'credentials.json', 'auth.json',
            'settings.json', 'appsettings.json', 'web.config', 'app.config',
            'database.yml', 'secrets.yml', '.aws/credentials', '.ssh/id_rsa',
            'id_rsa', 'id_dsa', 'id_ecdsa', 'id_ed25519', 'known_hosts',
            'backup.sql', 'dump.sql', 'database.sql', 'db.sql'
        ]
        
        semaphore = asyncio.Semaphore(5)  # Limit concurrent requests
        
        tasks = []
        for file_path in common_files:
            task = self._scan_common_file_with_semaphore(semaphore, client, target_url, file_path)
            tasks.append(task)
            
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for result in results:
            if isinstance(result, list):
                vulnerabilities.extend(result)
                
        return vulnerabilities

    async def _scan_common_file_with_semaphore(self, semaphore: asyncio.Semaphore, client: httpx.AsyncClient, target_url: str, file_path: str) -> List[Dict[str, Any]]:
        """Scan common file with semaphore control."""
        async with semaphore:
            return await self._scan_common_file(client, target_url, file_path)

    async def _scan_common_file(self, client: httpx.AsyncClient, target_url: str, file_path: str) -> List[Dict[str, Any]]:
        """Scan a common file for secrets."""
        vulnerabilities = []

        try:
            file_url = f"{target_url.rstrip('/')}/{file_path}"
            response = await client.get(file_url)

            if response.status_code == 200:
                # Check if this is actually the requested file or just SPA fallback
                if self._is_valid_file_response(response, file_path, target_url):
                    secrets = self._find_secrets_in_text(response.text, file_url)
                    vulnerabilities.extend(secrets)

        except Exception:
            pass  # File not found is expected

        return vulnerabilities

    async def _scan_javascript_files(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Scan JavaScript files for embedded secrets."""
        vulnerabilities = []
        
        try:
            # First get the main page to find JS files
            response = await client.get(target_url)
            
            if response.status_code == 200:
                # Extract JavaScript file URLs
                js_files = re.findall(r'<script[^>]*src=[\'"]([^\'"]*)[\'"]', response.text)
                
                semaphore = asyncio.Semaphore(10)
                tasks = []
                
                for js_file in js_files:
                    if not js_file.startswith('http'):
                        js_file = f"{target_url.rstrip('/')}/{js_file.lstrip('/')}"
                    
                    task = self._scan_js_file_with_semaphore(semaphore, client, js_file)
                    tasks.append(task)
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for result in results:
                    if isinstance(result, list):
                        vulnerabilities.extend(result)
                        
        except Exception as e:
            self.logger.debug("JavaScript scan failed", error=str(e))
            
        return vulnerabilities

    async def _scan_js_file_with_semaphore(self, semaphore: asyncio.Semaphore, client: httpx.AsyncClient, js_url: str) -> List[Dict[str, Any]]:
        """Scan JS file with semaphore control."""
        async with semaphore:
            return await self._scan_js_file(client, js_url)

    async def _scan_js_file(self, client: httpx.AsyncClient, js_url: str) -> List[Dict[str, Any]]:
        """Scan individual JavaScript file."""
        vulnerabilities = []

        try:
            response = await client.get(js_url)

            if response.status_code == 200:
                # Extract filename from URL for validation
                file_path = js_url.split('/')[-1] if '/' in js_url else js_url

                # Validate this is actually a JS file and not SPA fallback
                if self._is_valid_file_response(response, file_path, js_url):
                    secrets = self._find_secrets_in_text(response.text, js_url)
                    vulnerabilities.extend(secrets)

        except Exception:
            pass  # JS file not accessible

        return vulnerabilities

    def _find_secrets_in_text(self, text: str, source_url: str, file_path: str = None) -> List[Dict[str, Any]]:
        """Find secrets in text using regex patterns."""
        vulnerabilities = []

        # Check if this is a minified JavaScript file
        is_minified_js = self._is_minified_javascript(text, source_url)

        for secret_type, pattern in self.secret_patterns.items():
            matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)

            for match in matches:
                secret_value = match.group()

                # Get context around the match
                start = max(0, match.start() - 50)
                end = min(len(text), match.end() + 50)
                context = text[start:end]

                # Enhanced false positive detection for minified JS
                if is_minified_js and self._is_likely_false_positive_in_js(secret_type, secret_value, context):
                    continue

                # Additional context-based validation
                if not self._validate_secret_context(secret_type, secret_value, context, source_url):
                    continue

                # Calculate severity based on secret type
                severity = self._calculate_secret_severity(secret_type, secret_value)

                vuln = self.create_vulnerability(
                    title=f"Exposed {secret_type.replace('_', ' ').title()}",
                    description=f"Found exposed {secret_type} in {source_url}",
                    severity=severity,
                    category="Sensitive Data Exposure",
                    url=source_url,
                    parameter=file_path,
                    payload=secret_value[:20] + "..." if len(secret_value) > 20 else secret_value,
                    evidence=f"Context: {context}",
                    cwe_id="CWE-200",
                    owasp_category="A02:2021 – Cryptographic Failures",
                    remediation=f"Remove {secret_type} from source code and rotate credentials",
                    references=[
                        "https://owasp.org/www-project-top-ten/2017/A3_2017-Sensitive_Data_Exposure",
                        "https://docs.github.com/en/code-security/secret-scanning"
                    ]
                )
                vulnerabilities.append(vuln)
                self.log_vulnerability(vuln)

        # Also check for high entropy strings (but be more careful with minified JS)
        if not is_minified_js:
            entropy_secrets = self._find_high_entropy_secrets(text, source_url, file_path)
            vulnerabilities.extend(entropy_secrets)

        return vulnerabilities

    def _find_secrets_in_headers(self, headers: Dict[str, str], source_url: str) -> List[Dict[str, Any]]:
        """Find secrets in HTTP headers."""
        vulnerabilities = []
        
        sensitive_headers = ['authorization', 'x-api-key', 'x-auth-token', 'x-access-token']
        
        for header_name, header_value in headers.items():
            if header_name.lower() in sensitive_headers:
                # Check if header value looks like a secret
                if len(header_value) > 10 and not header_value.lower().startswith('basic'):
                    vuln = self.create_vulnerability(
                        title="Exposed API Key in HTTP Header",
                        description=f"Sensitive header '{header_name}' exposed in response",
                        severity="high",
                        category="Sensitive Data Exposure",
                        url=source_url,
                        evidence=f"Header: {header_name}: {header_value[:20]}...",
                        cwe_id="CWE-200",
                        remediation="Remove sensitive headers from HTTP responses"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
        
        return vulnerabilities

    def _find_high_entropy_secrets(self, text: str, source_url: str, file_path: str = None) -> List[Dict[str, Any]]:
        """Find high entropy strings that might be secrets."""
        vulnerabilities = []
        
        # Look for quoted strings with high entropy
        quoted_strings = re.findall(r'[\'"][A-Za-z0-9+/=]{16,}[\'"]', text)
        
        for string in quoted_strings:
            clean_string = string.strip('\'"')
            
            if len(clean_string) >= 16:
                entropy = self._calculate_entropy(clean_string)
                
                if entropy > self.entropy_threshold:
                    # Additional checks to reduce false positives
                    if self._looks_like_secret(clean_string):
                        vuln = self.create_vulnerability(
                            title="High Entropy String (Potential Secret)",
                            description=f"Found high entropy string that may be a secret",
                            severity="medium",
                            category="Sensitive Data Exposure",
                            url=source_url,
                            parameter=file_path,
                            payload=clean_string[:20] + "...",
                            evidence=f"Entropy: {entropy:.2f}, String: {string}",
                            cwe_id="CWE-200",
                            remediation="Review string to determine if it's a secret that should be protected"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)
        
        return vulnerabilities

    def _calculate_entropy(self, string: str) -> float:
        """Calculate Shannon entropy of a string."""
        if not string:
            return 0
            
        entropy = 0
        for x in range(256):
            p_x = float(string.count(chr(x))) / len(string)
            if p_x > 0:
                entropy += - p_x * (p_x.bit_length() - 1)
        
        return entropy

    def _looks_like_secret(self, string: str) -> bool:
        """Additional heuristics to determine if a string looks like a secret."""
        # Skip common false positives
        false_positives = [
            'lorem', 'ipsum', 'test', 'sample', 'example', 'demo',
            'placeholder', 'abcd', '1234', 'qwer', 'asdf'
        ]
        
        string_lower = string.lower()
        if any(fp in string_lower for fp in false_positives):
            return False
            
        # Look for patterns that suggest it's a secret
        secret_indicators = [
            len(string) >= 20,  # Long strings are more likely secrets
            re.search(r'[A-Z]{2,}', string),  # Has uppercase letters
            re.search(r'[0-9]{2,}', string),  # Has numbers
            '+' in string or '/' in string,  # Base64 indicators
            '=' in string  # Base64 padding
        ]
        
        return sum(secret_indicators) >= 2

    def _is_valid_file_response(self, response: httpx.Response, file_path: str, target_url: str) -> bool:
        """
        Validate if the response is actually the requested file or just SPA fallback.
        This is critical for preventing false positives in Single Page Applications.
        """
        content_type = response.headers.get('content-type', '').lower()
        content_disposition = response.headers.get('content-disposition', '').lower()
        content = response.text

        # 1. Check Content-Type validation
        expected_content_types = {
            '.json': ['application/json', 'text/json'],
            '.env': ['text/plain', 'application/octet-stream'],
            '.js': ['application/javascript', 'text/javascript'],
            '.css': ['text/css'],
            '.xml': ['application/xml', 'text/xml'],
            '.yml': ['text/yaml', 'application/yaml', 'text/plain'],
            '.yaml': ['text/yaml', 'application/yaml', 'text/plain'],
            '.sql': ['text/plain', 'application/sql'],
            '.txt': ['text/plain'],
            '.config': ['text/plain', 'application/xml'],
            '.ini': ['text/plain'],
            '.cfg': ['text/plain'],
            '.conf': ['text/plain'],
            '.properties': ['text/plain']
        }

        # Get file extension
        file_ext = None
        for ext in expected_content_types.keys():
            if file_path.endswith(ext):
                file_ext = ext
                break

        # 2. SPA Detection - Check if content looks like HTML when we expect other formats
        if file_ext and file_ext != '.html':
            # If we expect a non-HTML file but get HTML content, it's likely SPA fallback
            if 'text/html' in content_type:
                return False

            # Check if content starts with HTML doctype or tags
            content_start = content.strip()[:100].lower()
            html_indicators = ['<!doctype html', '<html', '<head>', '<body>', '<meta', '<title>']
            if any(indicator in content_start for indicator in html_indicators):
                return False

        # 3. Content-Disposition check - if filename doesn't match, it's likely fallback
        if content_disposition and 'filename=' in content_disposition:
            if 'filename="index.html"' in content_disposition and not file_path.endswith('.html'):
                return False

        # 4. Content validation for specific file types
        if file_ext == '.json':
            # JSON files should start with { or [
            content_trimmed = content.strip()
            if not (content_trimmed.startswith('{') or content_trimmed.startswith('[')):
                return False

            # Try to parse as JSON
            try:
                import json
                json.loads(content)
            except (json.JSONDecodeError, ValueError):
                return False

        elif file_ext == '.env':
            # .env files should contain KEY=VALUE pairs or comments
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            if lines:
                env_pattern = re.compile(r'^[A-Z_][A-Z0-9_]*=.*$|^#.*$')
                valid_lines = sum(1 for line in lines if env_pattern.match(line))
                # At least 50% of non-empty lines should be valid env format
                if valid_lines / len(lines) < 0.5:
                    return False

        elif file_ext in ['.yml', '.yaml']:
            # YAML files should not contain HTML
            if any(tag in content.lower() for tag in ['<html', '<head>', '<body>', '<script>']):
                return False

        elif file_ext == '.sql':
            # SQL files should contain SQL keywords
            sql_keywords = ['select', 'insert', 'update', 'delete', 'create', 'drop', 'alter', 'grant']
            content_lower = content.lower()
            if not any(keyword in content_lower for keyword in sql_keywords):
                return False

        # 5. Size validation - SPA fallbacks are usually consistent in size
        # If multiple different "files" return the same size, it's likely SPA fallback
        if hasattr(self, '_spa_response_sizes'):
            if len(content) in self._spa_response_sizes:
                self._spa_response_sizes[len(content)] += 1
                # If we've seen this exact size 3+ times, it's likely SPA fallback
                if self._spa_response_sizes[len(content)] >= 3:
                    return False
            else:
                self._spa_response_sizes[len(content)] = 1
        else:
            self._spa_response_sizes = {len(content): 1}

        # 6. Content hash validation - if multiple files return identical content
        import hashlib
        content_hash = hashlib.md5(content.encode()).hexdigest()
        if hasattr(self, '_spa_content_hashes'):
            if content_hash in self._spa_content_hashes:
                self._spa_content_hashes[content_hash] += 1
                # If we've seen this exact content 2+ times for different files, it's SPA fallback
                if self._spa_content_hashes[content_hash] >= 2:
                    return False
            else:
                self._spa_content_hashes[content_hash] = 1
        else:
            self._spa_content_hashes = {content_hash: 1}

        # 7. Framework-specific SPA detection
        spa_indicators = [
            'react', 'vue', 'angular', 'next.js', 'nuxt', 'gatsby',
            'create-react-app', 'vite', 'webpack', 'parcel'
        ]
        content_lower = content.lower()
        if any(indicator in content_lower for indicator in spa_indicators):
            # If content contains SPA framework indicators and we're requesting a config file
            if file_ext in ['.env', '.json', '.config', '.ini', '.cfg']:
                return False

        return True

    def _is_minified_javascript(self, text: str, source_url: str) -> bool:
        """Detect if the content is minified JavaScript."""
        # Check file extension
        if source_url.endswith('.min.js') or '/static/js/' in source_url:
            return True

        # Check content characteristics of minified JS
        if len(text) > 1000:  # Only check large files
            lines = text.split('\n')
            if len(lines) < 10:  # Very few lines suggests minification
                return True

            # Check for typical minified JS patterns
            minified_indicators = [
                len([line for line in lines if len(line) > 200]) > len(lines) * 0.5,  # Long lines
                '!function(' in text or '(function(' in text,  # IIFE patterns
                text.count(';') > len(text) / 50,  # High semicolon density
                text.count('var ') > 20 and text.count('\n') < 20,  # Many vars, few lines
            ]

            return sum(minified_indicators) >= 2

        return False

    def _is_likely_false_positive_in_js(self, secret_type: str, secret_value: str, context: str) -> bool:
        """Check if a detected secret is likely a false positive in JavaScript."""

        # AWS Access Key specific checks
        if secret_type == 'aws_access_key':
            # In minified JS, AKIA patterns are often part of compiled code
            # Check if it's surrounded by non-alphanumeric characters (typical in minified JS)
            if re.search(r'[^a-zA-Z0-9]AKIA[0-9A-Z]{16}[^a-zA-Z0-9]', context):
                # Additional checks for minified JS context
                js_indicators = [
                    'function(' in context,
                    'return' in context,
                    '&&' in context or '||' in context,
                    context.count(',') > 3,
                    context.count('(') > 2,
                    len(context.replace(' ', '')) > len(context) * 0.8,  # Low space ratio
                ]

                if sum(js_indicators) >= 3:
                    return True

        # JWT Token checks
        elif secret_type == 'jwt_token':
            # Real JWTs should have proper structure
            parts = secret_value.split('.')
            if len(parts) != 3:
                return True

            # Check if it's in a typical JS assignment context
            if not any(indicator in context.lower() for indicator in [
                'token', 'jwt', 'auth', 'bearer', 'authorization'
            ]):
                return True

        # Generic checks for all secret types
        # Check if it's part of a larger hex/base64 string (common in minified JS)
        extended_context = context.replace(' ', '').replace('\n', '')
        if len(extended_context) > len(secret_value) * 3:
            # If the secret is a small part of a much larger string, likely false positive
            return True

        return False

    def _validate_secret_context(self, secret_type: str, secret_value: str, context: str, source_url: str) -> bool:
        """Validate if the secret appears in a legitimate context."""

        # AWS Access Key validation
        if secret_type == 'aws_access_key':
            # Should be in a configuration context
            valid_contexts = [
                'aws_access_key', 'access_key', 'accesskey', 'key_id',
                'AWS_ACCESS_KEY', 'ACCESS_KEY', 'ACCESSKEY', 'KEY_ID'
            ]

            # Check if it appears near configuration keywords
            context_lower = context.lower()
            if any(ctx in context_lower for ctx in valid_contexts):
                return True

            # Check if it's in quotes (more likely to be a real key)
            if f'"{secret_value}"' in context or f"'{secret_value}'" in context:
                return True

            # If none of the above, likely false positive
            return False

        # GitHub token validation
        elif secret_type.startswith('github_'):
            # Should be in authentication context
            valid_contexts = ['token', 'github', 'auth', 'authorization', 'bearer']
            context_lower = context.lower()
            return any(ctx in context_lower for ctx in valid_contexts)

        # JWT validation
        elif secret_type == 'jwt_token':
            # Should be in authentication context
            valid_contexts = ['token', 'jwt', 'auth', 'authorization', 'bearer']
            context_lower = context.lower()
            return any(ctx in context_lower for ctx in valid_contexts)

        # For other types, be more permissive but still check for obvious false positives
        return True

    def _calculate_secret_severity(self, secret_type: str, secret_value: str) -> str:
        """Calculate severity based on secret type."""
        critical_types = [
            'aws_secret_key', 'private_key', 'postgres_url', 'mysql_url',
            'mongodb_url', 'password_in_url'
        ]
        
        high_types = [
            'aws_access_key', 'github_token', 'stripe_key', 'google_api_key',
            'jwt_token', 'hardcoded_password'
        ]
        
        if secret_type in critical_types:
            return "critical"
        elif secret_type in high_types:
            return "high"
        else:
            return "medium"

    async def _validate_secrets(self, client: httpx.AsyncClient, vulnerabilities: List[Dict[str, Any]]):
        """Validate if found secrets are still active."""
        for vuln in vulnerabilities:
            try:
                secret_type = vuln.get('title', '').lower()
                secret_value = vuln.get('payload', '')
                
                # Only validate certain types to avoid making malicious requests
                if 'github' in secret_type and secret_value:
                    is_valid = await self._validate_github_token(client, secret_value)
                    if is_valid:
                        vuln['severity'] = 'critical'
                        vuln['description'] += ' (VALIDATED - Token is active!)'
                        
            except Exception as e:
                self.logger.debug("Token validation failed", error=str(e))

    async def _validate_github_token(self, client: httpx.AsyncClient, token: str) -> bool:
        """Validate GitHub token."""
        try:
            headers = {'Authorization': f'token {token}'}
            response = await client.get('https://api.github.com/user', headers=headers)
            return response.status_code == 200
        except Exception:
            return False

    async def _discover_and_scan_files(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Discover and scan additional files that might contain secrets."""
        vulnerabilities = []

        try:
            # Common file patterns to discover
            file_patterns = [
                # React/Next.js files
                '_next/static/chunks/*.js',
                'static/js/*.js',
                'assets/js/*.js',
                'js/*.js',
                'scripts/*.js',

                # Config files
                'config/*.json',
                'config/*.js',
                'settings/*.json',

                # Build files
                'build/static/js/*.js',
                'dist/*.js',
                'public/*.js',

                # Common exposed files
                'robots.txt',
                'sitemap.xml',
                '.well-known/security.txt',
                'humans.txt',
                'manifest.json',
                'package.json',
                'composer.json',
                'yarn.lock',
                'package-lock.json',

                # Backup and temp files
                'backup.zip',
                'backup.tar.gz',
                'dump.sql',
                'database.sql',
                'config.bak',
                'settings.bak',

                # Common directories
                'admin/',
                'api/',
                'docs/',
                'documentation/',
                'swagger/',
                'graphql/',
            ]

            # Try to discover files
            discovered_files = []

            # First, try to get directory listings
            for pattern in file_patterns:
                if pattern.endswith('/'):
                    # Try directory listing
                    dir_files = await self._try_directory_listing(client, target_url, pattern)
                    discovered_files.extend(dir_files)
                else:
                    # Try direct file access
                    file_url = f"{target_url.rstrip('/')}/{pattern}"
                    discovered_files.append(file_url)

            # Also try to discover files from main page
            main_page_files = await self._discover_files_from_main_page(client, target_url)
            discovered_files.extend(main_page_files)

            # Scan discovered files
            semaphore = asyncio.Semaphore(10)
            tasks = []

            for file_url in discovered_files[:50]:  # Limit to first 50 files
                task = self._scan_discovered_file_with_semaphore(semaphore, client, file_url)
                tasks.append(task)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            for result in results:
                if isinstance(result, list):
                    vulnerabilities.extend(result)

        except Exception as e:
            self.logger.debug("File discovery failed", error=str(e))

        return vulnerabilities

    async def _try_directory_listing(self, client: httpx.AsyncClient, target_url: str, directory: str) -> List[str]:
        """Try to get directory listing."""
        discovered_files = []

        try:
            dir_url = f"{target_url.rstrip('/')}/{directory}"
            response = await client.get(dir_url)

            if response.status_code == 200:
                # Look for file links in directory listing
                file_links = re.findall(r'href=[\'"]([^\'">]+\.(js|json|txt|xml|sql|bak|zip|tar\.gz))[\'"]', response.text, re.IGNORECASE)

                for link, _ in file_links:
                    if not link.startswith('http'):
                        file_url = f"{dir_url.rstrip('/')}/{link.lstrip('/')}"
                        discovered_files.append(file_url)

        except Exception:
            pass

        return discovered_files

    async def _discover_files_from_main_page(self, client: httpx.AsyncClient, target_url: str) -> List[str]:
        """Discover files referenced in the main page."""
        discovered_files = []

        try:
            response = await client.get(target_url)

            if response.status_code == 200:
                content = response.text

                # Find script sources
                scripts = re.findall(r'<script[^>]*src=[\'"]([^\'"]*)[\'"]', content)

                # Find link hrefs (CSS, manifests, etc.)
                links = re.findall(r'<link[^>]*href=[\'"]([^\'"]*)[\'"]', content)

                # Find image sources (might contain interesting paths)
                images = re.findall(r'<img[^>]*src=[\'"]([^\'"]*)[\'"]', content)

                # Find fetch/ajax calls in inline scripts
                api_calls = re.findall(r'fetch\([\'"]([^\'"]*)[\'"]', content)
                api_calls.extend(re.findall(r'\.get\([\'"]([^\'"]*)[\'"]', content))
                api_calls.extend(re.findall(r'\.post\([\'"]([^\'"]*)[\'"]', content))

                all_urls = scripts + links + images + api_calls

                for url in all_urls:
                    if url and not url.startswith('http') and not url.startswith('//'):
                        full_url = f"{target_url.rstrip('/')}/{url.lstrip('/')}"
                        discovered_files.append(full_url)

        except Exception:
            pass

        return discovered_files

    async def _scan_discovered_file_with_semaphore(self, semaphore: asyncio.Semaphore, client: httpx.AsyncClient, file_url: str) -> List[Dict[str, Any]]:
        """Scan discovered file with semaphore control."""
        async with semaphore:
            return await self._scan_discovered_file(client, file_url)

    async def _scan_discovered_file(self, client: httpx.AsyncClient, file_url: str) -> List[Dict[str, Any]]:
        """Scan a discovered file for secrets."""
        vulnerabilities = []

        try:
            response = await client.get(file_url)

            if response.status_code == 200:
                # Extract filename from URL for validation
                file_path = file_url.split('/')[-1] if '/' in file_url else file_url

                # Validate this is actually the requested file and not SPA fallback
                if self._is_valid_file_response(response, file_path, file_url):
                    # Check content type
                    content_type = response.headers.get('content-type', '').lower()

                    # Only scan text-based files
                    if any(ct in content_type for ct in ['text/', 'application/json', 'application/javascript']):
                        secrets = self._find_secrets_in_text(response.text, file_url)
                        vulnerabilities.extend(secrets)

                        # Special handling for package.json and similar files
                        if file_url.endswith(('.json', 'package.json', 'composer.json')):
                            config_vulns = await self._scan_config_file(response.text, file_url)
                            vulnerabilities.extend(config_vulns)

        except Exception:
            pass  # File not accessible

        return vulnerabilities

    async def _scan_config_file(self, content: str, file_url: str) -> List[Dict[str, Any]]:
        """Scan configuration files for sensitive information."""
        vulnerabilities = []

        try:
            import json
            config = json.loads(content)

            # Look for sensitive keys in JSON
            sensitive_keys = [
                'password', 'secret', 'key', 'token', 'api_key', 'apikey',
                'private_key', 'access_token', 'refresh_token', 'client_secret',
                'database_url', 'db_password', 'redis_url', 'mongodb_url'
            ]

            def scan_json_recursive(obj, path=""):
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        current_path = f"{path}.{key}" if path else key

                        # Check if key name suggests sensitive data
                        if any(sensitive in key.lower() for sensitive in sensitive_keys):
                            if isinstance(value, str) and len(value) > 5:
                                vuln = self.create_vulnerability(
                                    title=f"Sensitive Configuration Key: {key}",
                                    description=f"Found sensitive configuration key '{key}' in {file_url}",
                                    severity="high",
                                    category="Sensitive Data Exposure",
                                    url=file_url,
                                    parameter=current_path,
                                    payload=str(value)[:20] + "..." if len(str(value)) > 20 else str(value),
                                    evidence=f"Key: {key}, Value: {value}",
                                    cwe_id="CWE-200",
                                    remediation="Remove sensitive data from configuration files"
                                )
                                vulnerabilities.append(vuln)
                                self.log_vulnerability(vuln)

                        # Recurse into nested objects
                        scan_json_recursive(value, current_path)

                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        scan_json_recursive(item, f"{path}[{i}]")

            scan_json_recursive(config)

        except Exception:
            pass  # Not valid JSON or other error

        return vulnerabilities

    async def _scan_source_maps(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Scan for exposed source maps that might contain sensitive information."""
        vulnerabilities = []

        try:
            # First get JS files and look for source map references
            response = await client.get(target_url)

            if response.status_code == 200:
                js_files = re.findall(r'<script[^>]*src=[\'"]([^\'"]*\.js)[\'"]', response.text)

                for js_file in js_files:
                    if not js_file.startswith('http'):
                        js_file = f"{target_url.rstrip('/')}/{js_file.lstrip('/')}"

                    # Check if JS file has source map
                    sourcemap_vulns = await self._check_source_map(client, js_file)
                    vulnerabilities.extend(sourcemap_vulns)

        except Exception as e:
            self.logger.debug("Source map scan failed", error=str(e))

        return vulnerabilities

    async def _check_source_map(self, client: httpx.AsyncClient, js_url: str) -> List[Dict[str, Any]]:
        """Check if a JS file has an exposed source map."""
        vulnerabilities = []

        try:
            # Try to get the JS file
            response = await client.get(js_url)

            if response.status_code == 200:
                content = response.text

                # Look for source map reference
                sourcemap_match = re.search(r'//# sourceMappingURL=([^\s]+)', content)

                if sourcemap_match:
                    sourcemap_url = sourcemap_match.group(1)

                    if not sourcemap_url.startswith('http'):
                        # Construct full URL
                        base_url = '/'.join(js_url.split('/')[:-1])
                        sourcemap_url = f"{base_url}/{sourcemap_url}"

                    # Try to access the source map
                    try:
                        sm_response = await client.get(sourcemap_url)

                        if sm_response.status_code == 200:
                            vuln = self.create_vulnerability(
                                title="Exposed Source Map",
                                description=f"Source map file is publicly accessible at {sourcemap_url}",
                                severity="medium",
                                category="Information Disclosure",
                                url=sourcemap_url,
                                evidence=f"Source map for {js_url} is accessible",
                                cwe_id="CWE-200",
                                remediation="Remove source maps from production or restrict access"
                            )
                            vulnerabilities.append(vuln)
                            self.log_vulnerability(vuln)

                            # Scan the source map content for secrets
                            sm_secrets = self._find_secrets_in_text(sm_response.text, sourcemap_url)
                            vulnerabilities.extend(sm_secrets)

                    except Exception:
                        pass

        except Exception:
            pass

        return vulnerabilities