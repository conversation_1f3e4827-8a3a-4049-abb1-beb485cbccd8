"""
Intelligent endpoint validation and business context scoring.
"""
import re
import urllib.parse
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from app.models.scan import BusinessPriority


@dataclass
class EndpointAnalysis:
    """Analysis result for an endpoint."""
    is_valid_endpoint: bool
    business_priority: BusinessPriority
    endpoint_score: int  # 0-100
    is_likely_false_positive: bool
    false_positive_confidence: int  # 0-100
    reasoning: str
    endpoint_type: str  # "api", "admin", "auth", "static", "framework", "invalid"


class EndpointIntelligence:
    """Intelligent endpoint analysis and scoring."""
    
    def __init__(self):
        # Patterns that indicate false positives (JS code fragments)
        self.false_positive_patterns = [
            r'[a-zA-Z]+\(\w+\)',  # Function calls like "parseInt(v)"
            r'[a-zA-Z]+\.[a-zA-Z]+\(',  # Method calls like "Object.assign("
            r'[a-zA-Z]+\[\d+\]',  # Array access like "arr[0]"
            r'{\w+:\w+}',  # Object literals like "{data:users}"
            r'[a-zA-Z]+=[a-zA-Z]+',  # Variable assignments like "f=g"
            r';\w+\(\)',  # Statements like ";throw"
            r'catch\(\w+\)',  # Exception handling
            r'return\s+\w+',  # Return statements
            r'async\s+\w+',  # Async functions
            r'=>\s*{',  # Arrow functions
            r'\.map\(|\.filter\(|\.reduce\(',  # Array methods
            r'console\.(log|error|warn)',  # Console calls
            r'document\.|window\.',  # DOM access
            r'typeof\s+\w+',  # Type checks
            r'instanceof\s+\w+',  # Instance checks
        ]
        
        # Patterns for valid API endpoints
        self.api_patterns = [
            r'^/api/',
            r'^/v\d+/',
            r'^/rest/',
            r'^/graphql',
            r'^/webhook',
        ]
        
        # Patterns for admin/sensitive endpoints
        self.admin_patterns = [
            r'^/admin/',
            r'^/management/',
            r'^/internal/',
            r'^/debug/',
            r'^/console/',
            r'^/dashboard/',
        ]
        
        # Patterns for authentication endpoints
        self.auth_patterns = [
            r'^/auth/',
            r'^/login',
            r'^/logout',
            r'^/register',
            r'^/oauth',
            r'^/token',
            r'^/sso/',
            r'^/saml/',
        ]
        
        # Patterns for development/testing endpoints
        self.dev_patterns = [
            r'^/test/',
            r'^/dev/',
            r'^/staging/',
            r'^/health',
            r'^/status',
            r'^/metrics',
            r'^/info',
            r'^/ping',
        ]
        
        # Patterns for static/framework content (low priority)
        self.static_patterns = [
            r'\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$',
            r'^/_next/',
            r'^/static/',
            r'^/assets/',
            r'^/public/',
            r'^/images/',
            r'^/css/',
            r'^/js/',
            r'^/fonts/',
            r'^/media/',
            r'^/uploads/',
            r'^/downloads/',
        ]
        
        # Patterns for common framework routes (usually low priority)
        self.framework_patterns = [
            r'^/_next/',  # Next.js
            r'^/__next/',
            r'^/webpack/',
            r'^/hot-update',
            r'^/sockjs-node/',
            r'^/manifest\.json$',
            r'^/service-worker\.js$',
            r'^/sw\.js$',
            r'^/robots\.txt$',
            r'^/sitemap\.xml$',
            r'^/favicon\.ico$',
        ]
        
        # Technology-specific patterns for better scoring
        self.tech_patterns = {
            'supabase': r'\.supabase\.co/',
            'vercel': r'\.vercel\.app/',
            'firebase': r'\.firebaseapp\.com/|\.googleapis\.com/',
            'aws': r'\.amazonaws\.com/',
            'azure': r'\.azure\.com/|\.azurewebsites\.net/',
            'heroku': r'\.herokuapp\.com/',
            'railway': r'\.railway\.app/',
            'render': r'\.render\.com/',
            'netlify': r'\.netlify\.app/',
        }
        
        # High-value endpoint indicators
        self.high_value_indicators = [
            'admin', 'management', 'console', 'dashboard',
            'internal', 'private', 'secure',
            'api', 'webhook', 'callback',
            'auth', 'login', 'oauth', 'token',
            'user', 'account', 'profile',
            'payment', 'billing', 'order',
            'upload', 'download', 'file',
            'config', 'setting', 'preference',
            'debug', 'test', 'dev', 'staging',
        ]
        
        # Low-value endpoint indicators
        self.low_value_indicators = [
            'static', 'asset', 'public', 'css', 'js',
            'image', 'img', 'photo', 'pic',
            'font', 'icon', 'logo',
            'help', 'about', 'contact', 'faq',
            'legal', 'privacy', 'terms',
            'blog', 'news', 'article',
        ]

    def analyze_endpoint(self, url: str, target_domain: str = None) -> EndpointAnalysis:
        """Analyze an endpoint for validity and business importance."""
        
        # Parse the URL
        parsed = urllib.parse.urlparse(url)
        path = parsed.path.lower()
        domain = parsed.netloc.lower()
        query = parsed.query
        
        # Step 1: Check if it's likely a false positive
        is_fp, fp_confidence, fp_reason = self._check_false_positive(url, path)
        if is_fp:
            return EndpointAnalysis(
                is_valid_endpoint=False,
                business_priority=BusinessPriority.VERY_LOW,
                endpoint_score=0,
                is_likely_false_positive=True,
                false_positive_confidence=fp_confidence,
                reasoning=fp_reason,
                endpoint_type="invalid"
            )
        
        # Step 2: Determine endpoint type and base score
        endpoint_type, base_score = self._classify_endpoint(path, domain)
        
        # Step 3: Calculate domain authority score
        domain_score = self._calculate_domain_score(domain, target_domain)
        
        # Step 4: Calculate content/path score
        path_score = self._calculate_path_score(path, query)
        
        # Step 5: Calculate technology score
        tech_score = self._calculate_tech_score(domain, path)
        
        # Step 6: Combine scores
        final_score = self._combine_scores(base_score, domain_score, path_score, tech_score)
        
        # Step 7: Determine business priority
        business_priority = self._score_to_priority(final_score, endpoint_type)
        
        # Step 8: Generate reasoning
        reasoning = self._generate_reasoning(endpoint_type, domain, path, final_score)
        
        return EndpointAnalysis(
            is_valid_endpoint=True,
            business_priority=business_priority,
            endpoint_score=final_score,
            is_likely_false_positive=False,
            false_positive_confidence=0,
            reasoning=reasoning,
            endpoint_type=endpoint_type
        )

    def _check_false_positive(self, url: str, path: str) -> Tuple[bool, int, str]:
        """Check if the URL is likely a false positive from JS parsing."""
        
        # Check for JS code patterns
        for pattern in self.false_positive_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return True, 85, f"Contains JS code pattern: {pattern}"
        
        # Check for malformed URLs
        if len(url) > 500:
            return True, 90, "URL too long (likely JS string)"
        
        # Check for non-ASCII characters in inappropriate places
        try:
            url.encode('ascii')
        except UnicodeEncodeError:
            return True, 75, "Contains non-ASCII characters"
        
        # Check for obvious JS fragments
        js_indicators = [
            'async function', 'await ', 'return ', 'throw ',
            '=>', 'console.', 'document.', 'window.',
            'Object.assign', 'JSON.parse', 'parseInt',
            'catch(', 'try{', 'if(', 'for(',
            '&&', '||', '===', '!==',
        ]
        
        for indicator in js_indicators:
            if indicator in url:
                return True, 80, f"Contains JS indicator: {indicator}"
        
        # Check for excessive query parameters (might be JS object)
        parsed = urllib.parse.urlparse(url)
        if len(parsed.query) > 200:
            return True, 70, "Excessive query parameters"
        
        # Check for paths that don't look like URLs
        if not path.startswith('/'):
            return True, 95, "Invalid path format"
        
        # Check for multiple consecutive special characters
        if re.search(r'[{}()\[\]]{2,}', url):
            return True, 85, "Multiple consecutive special characters"
        
        return False, 0, ""

    def _classify_endpoint(self, path: str, domain: str) -> Tuple[str, int]:
        """Classify endpoint type and assign base score."""
        
        # Admin endpoints (highest priority)
        for pattern in self.admin_patterns:
            if re.search(pattern, path):
                return "admin", 90
        
        # Authentication endpoints (high priority)
        for pattern in self.auth_patterns:
            if re.search(pattern, path):
                return "auth", 85
        
        # API endpoints (high priority)
        for pattern in self.api_patterns:
            if re.search(pattern, path):
                return "api", 80
        
        # Development endpoints (medium-high priority)
        for pattern in self.dev_patterns:
            if re.search(pattern, path):
                return "dev", 70
        
        # Static content (low priority)
        for pattern in self.static_patterns:
            if re.search(pattern, path):
                return "static", 20
        
        # Framework routes (low priority)
        for pattern in self.framework_patterns:
            if re.search(pattern, path):
                return "framework", 25
        
        # Default: unknown endpoint (medium priority)
        return "unknown", 50

    def _calculate_domain_score(self, domain: str, target_domain: str) -> int:
        """Calculate score based on domain authority."""
        if not domain:
            return 50
        
        # Primary domain gets highest score
        if target_domain and domain == target_domain:
            return 100
        
        # Subdomains of primary domain
        if target_domain and domain.endswith('.' + target_domain):
            return 85
        
        # Known high-value services
        high_value_domains = [
            'supabase.co', 'firebase.com', 'googleapis.com',
            'auth0.com', 'okta.com', 'aws.com', 'azure.com'
        ]
        
        for hv_domain in high_value_domains:
            if hv_domain in domain:
                return 75
        
        # CDNs and static services (lower priority)
        low_value_domains = [
            'cloudflare.com', 'cloudfront.net', 'fastly.com',
            'jsdelivr.net', 'unpkg.com', 'cdnjs.com'
        ]
        
        for lv_domain in low_value_domains:
            if lv_domain in domain:
                return 30
        
        # External domain (medium priority)
        return 60

    def _calculate_path_score(self, path: str, query: str) -> int:
        """Calculate score based on path content."""
        score = 50
        
        # High-value indicators
        for indicator in self.high_value_indicators:
            if indicator in path.lower():
                score += 10
        
        # Low-value indicators
        for indicator in self.low_value_indicators:
            if indicator in path.lower():
                score -= 10
        
        # Path depth (deeper paths often more important for APIs)
        depth = len([p for p in path.split('/') if p])
        if depth >= 3:
            score += 5
        elif depth <= 1:
            score -= 5
        
        # Parameters indicate dynamic endpoints
        if query:
            score += 10
        
        # Numeric IDs in path (common in REST APIs)
        if re.search(r'/\d+/', path):
            score += 15
        
        return max(0, min(100, score))

    def _calculate_tech_score(self, domain: str, path: str) -> int:
        """Calculate score based on technology stack."""
        score = 50
        
        # Detect known technologies
        for tech, pattern in self.tech_patterns.items():
            if re.search(pattern, domain + path):
                if tech in ['supabase', 'firebase']:
                    score += 20  # Backend-as-a-Service platforms
                elif tech in ['aws', 'azure']:
                    score += 15  # Cloud platforms
                else:
                    score += 10  # Other platforms
        
        return max(0, min(100, score))

    def _combine_scores(self, base_score: int, domain_score: int, 
                       path_score: int, tech_score: int) -> int:
        """Combine all scores into final endpoint score."""
        
        # Weighted combination
        weights = {
            'base': 0.4,      # Endpoint type is most important
            'domain': 0.3,    # Domain authority matters
            'path': 0.2,      # Path content is relevant
            'tech': 0.1       # Technology is minor factor
        }
        
        final_score = (
            base_score * weights['base'] +
            domain_score * weights['domain'] +
            path_score * weights['path'] +
            tech_score * weights['tech']
        )
        
        return int(max(0, min(100, final_score)))

    def _score_to_priority(self, score: int, endpoint_type: str) -> BusinessPriority:
        """Convert score to business priority."""
        
        # Admin endpoints are always high priority regardless of score
        if endpoint_type == "admin" and score >= 60:
            return BusinessPriority.CRITICAL
        
        # Authentication endpoints are important
        if endpoint_type == "auth" and score >= 70:
            return BusinessPriority.HIGH
        
        # Score-based mapping
        if score >= 85:
            return BusinessPriority.CRITICAL
        elif score >= 70:
            return BusinessPriority.HIGH
        elif score >= 50:
            return BusinessPriority.MEDIUM
        elif score >= 30:
            return BusinessPriority.LOW
        else:
            return BusinessPriority.VERY_LOW

    def _generate_reasoning(self, endpoint_type: str, domain: str, 
                          path: str, score: int) -> str:
        """Generate human-readable reasoning for the score."""
        
        reasons = []
        
        # Endpoint type reasoning
        type_reasons = {
            "admin": "Administrative endpoint - high security risk",
            "auth": "Authentication endpoint - credential exposure risk", 
            "api": "API endpoint - potential data exposure",
            "dev": "Development/testing endpoint - may expose debug info",
            "static": "Static content - low security risk",
            "framework": "Framework route - usually low risk",
            "unknown": "Unknown endpoint type"
        }
        
        if endpoint_type in type_reasons:
            reasons.append(type_reasons[endpoint_type])
        
        # Domain reasoning
        if "supabase.co" in domain:
            reasons.append("Supabase backend service - business critical")
        elif "vercel.app" in domain:
            reasons.append("Vercel deployment - application infrastructure")
        
        # Path reasoning
        if any(indicator in path.lower() for indicator in self.high_value_indicators):
            reasons.append("Contains high-value keywords")
        
        # Score reasoning
        if score >= 80:
            reasons.append("High business impact potential")
        elif score <= 30:
            reasons.append("Low business impact potential")
        
        return "; ".join(reasons) if reasons else f"Endpoint scored {score}/100"


def filter_and_score_endpoints(endpoints: List[str], target_domain: str = None) -> List[Tuple[str, EndpointAnalysis]]:
    """Filter and score a list of endpoints."""
    
    intelligence = EndpointIntelligence()
    results = []
    
    for endpoint in endpoints:
        analysis = intelligence.analyze_endpoint(endpoint, target_domain)
        
        # Only include valid endpoints
        if analysis.is_valid_endpoint:
            results.append((endpoint, analysis))
    
    # Sort by endpoint score (highest first)
    results.sort(key=lambda x: x[1].endpoint_score, reverse=True)
    
    return results