import asyncio
import re
from typing import List, Dict, Any, Optional, Set
from urllib.parse import urljoin, urlparse, parse_qs
from datetime import datetime
import httpx
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup
import json
import time
from .base_scanner import BaseScanner, <PERSON>annerResult
from .endpoint_intelligence import EndpointIntelligence, filter_and_score_endpoints
from app.models.scan import BusinessPriority


class APIEndpointScanner(BaseScanner):
    """Scanner for detecting API endpoint vulnerabilities."""
    
    def __init__(self, timeout: int = 300, concurrent_requests: int = 10):
        super().__init__(timeout, concurrent_requests)
        self.discovered_endpoints: Set[str] = set()
        self.tested_endpoints: Set[str] = set()
        self.endpoint_intelligence = EndpointIntelligence()
        self.target_domain: Optional[str] = None
        self.spa_detection = {
            'base_content_hash': None,
            'base_content_length': None,
            'is_spa': False,
            'spa_indicators': set()
        }
        
        # Common API paths to test
        self.common_api_paths = [
            "/api", "/v1", "/v2", "/v3", "/rest", "/graphql",
            "/admin", "/dashboard", "/management", "/internal",
            "/debug", "/test", "/dev", "/staging"
        ]
        
        # SQL injection payloads
        self.sqli_payloads = [
            "' OR '1'='1",
            "' UNION SELECT NULL--",
            "'; DROP TABLE users--",
            "' AND 1=CONVERT(int, (SELECT @@version))--"
        ]
        
        # XSS payloads
        self.xss_payloads = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "'\"><script>alert('xss')</script>"
        ]
        
        # Command injection payloads
        self.command_payloads = [
            "; cat /etc/passwd",
            "| whoami",
            "&& dir",
            "`id`"
        ]

    async def scan(self, target_url: str, **kwargs) -> ScannerResult:
        """Main API endpoint scanning method."""
        start_time = time.time()
        vulnerabilities = []
        
        # Set target domain for business scoring
        self.target_domain = urlparse(target_url).netloc
        
        try:
            async with httpx.AsyncClient(
                timeout=self.timeout,
                limits=httpx.Limits(max_connections=self.concurrent_requests),
                verify=False  # Allow self-signed certificates for testing
            ) as client:
                
                # Step 1: SPA Detection
                await self._detect_spa(client, target_url)

                # Step 2: Discovery
                await self._discover_endpoints(client, target_url)
                
                # Step 2: Filter and score endpoints using intelligence
                filtered_endpoints = filter_and_score_endpoints(
                    list(self.discovered_endpoints), 
                    self.target_domain
                )
                
                self.logger.info(
                    "Endpoint filtering results",
                    total_discovered=len(self.discovered_endpoints),
                    valid_endpoints=len(filtered_endpoints),
                    filtered_out=len(self.discovered_endpoints) - len(filtered_endpoints)
                )
                
                # Log some examples of filtered endpoints by priority
                high_priority = [ep for ep, analysis in filtered_endpoints if analysis.business_priority in [BusinessPriority.HIGH, BusinessPriority.CRITICAL]]
                self.logger.info(
                    "High priority endpoints found",
                    count=len(high_priority),
                    examples=high_priority[:5]
                )
                
                # Step 3: Test filtered endpoints (prioritize by score)
                semaphore = asyncio.Semaphore(self.concurrent_requests)
                tasks = []
                
                for endpoint, analysis in filtered_endpoints:
                    if endpoint not in self.tested_endpoints:
                        task = self._test_endpoint_with_semaphore(
                            semaphore, client, endpoint, analysis
                        )
                        tasks.append(task)
                
                # Execute tests concurrently
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Collect vulnerabilities
                for result in results:
                    if isinstance(result, list):
                        vulnerabilities.extend(result)
                    elif isinstance(result, Exception):
                        self.logger.error("Endpoint test failed", error=str(result))
            
            scan_duration = time.time() - start_time
            
            # Calculate filtering and priority statistics
            total_discovered = len(self.discovered_endpoints)
            valid_endpoints = len([ep for ep, analysis in filtered_endpoints])
            filtered_out = total_discovered - valid_endpoints
            
            high_priority_count = len([ep for ep, analysis in filtered_endpoints 
                                     if analysis.business_priority in [BusinessPriority.HIGH, BusinessPriority.CRITICAL]])
            
            priority_distribution = {}
            for ep, analysis in filtered_endpoints:
                priority = analysis.business_priority.value
                priority_distribution[priority] = priority_distribution.get(priority, 0) + 1
            
            return ScannerResult(
                vulnerabilities=vulnerabilities,
                metadata={
                    "discovered_endpoints": total_discovered,
                    "valid_endpoints": valid_endpoints,
                    "filtered_out": filtered_out,
                    "filtering_accuracy": round((filtered_out / total_discovered * 100), 2) if total_discovered > 0 else 0,
                    "tested_endpoints": len(self.tested_endpoints),
                    "high_priority_endpoints": high_priority_count,
                    "priority_distribution": priority_distribution,
                    "scan_methods": [
                        "intelligent_endpoint_discovery",
                        "business_context_scoring",
                        "false_positive_filtering",
                        "authentication_bypass",
                        "parameter_fuzzing",
                        "http_method_testing",
                        "cors_misconfiguration"
                    ]
                },
                scan_duration=scan_duration,
                timestamp=datetime.utcnow(),
                scanner_name="api_endpoint_scanner",
                target_url=target_url,
                success=True
            )
            
        except Exception as e:
            scan_duration = time.time() - start_time
            self.logger.error("API endpoint scan failed", error=str(e))
            
            return ScannerResult(
                vulnerabilities=[],
                metadata={},
                scan_duration=scan_duration,
                timestamp=datetime.utcnow(),
                scanner_name="api_endpoint_scanner",
                target_url=target_url,
                success=False,
                error_message=str(e)
            )

    async def _detect_spa(self, client: httpx.AsyncClient, target_url: str):
        """Detect if the target is a Single Page Application."""
        try:
            # Get the base page content
            response = await client.get(target_url)
            if response.status_code == 200:
                content = response.text
                content_hash = hash(content)
                content_length = len(content)

                self.spa_detection['base_content_hash'] = content_hash
                self.spa_detection['base_content_length'] = content_length

                # Check for SPA indicators
                spa_indicators = [
                    'react', 'vue', 'angular', 'ember', 'svelte',
                    'single page application', 'spa',
                    'router', 'routing',
                    'div id="root"', 'div id="app"',
                    'webpack', 'vite', 'parcel',
                    'bundle.js', 'app.js', 'main.js'
                ]

                content_lower = content.lower()
                found_indicators = [indicator for indicator in spa_indicators if indicator in content_lower]

                if found_indicators:
                    self.spa_detection['spa_indicators'] = set(found_indicators)

                    # Test a few different paths to see if they return the same content
                    test_paths = ['/admin', '/api', '/dashboard', '/nonexistent']
                    same_content_count = 0

                    for path in test_paths:
                        try:
                            test_url = urljoin(target_url, path)
                            test_response = await client.get(test_url)

                            if (test_response.status_code == 200 and
                                hash(test_response.text) == content_hash):
                                same_content_count += 1
                        except Exception:
                            pass

                    # If most paths return the same content, it's likely an SPA
                    if same_content_count >= 2:
                        self.spa_detection['is_spa'] = True
                        self.logger.info(
                            "SPA detected",
                            indicators=found_indicators,
                            same_content_paths=same_content_count
                        )

        except Exception as e:
            self.logger.debug("SPA detection failed", error=str(e))

    async def _discover_endpoints(self, client: httpx.AsyncClient, target_url: str):
        """Discover API endpoints through various methods."""
        base_domain = urlparse(target_url).netloc
        
        # Method 1: Common API paths
        await self._test_common_paths(client, target_url)
        
        # Method 2: robots.txt
        await self._parse_robots_txt(client, target_url)
        
        # Method 3: sitemap.xml
        await self._parse_sitemap(client, target_url)
        
        # Method 4: OpenAPI/Swagger discovery
        await self._discover_openapi_specs(client, target_url)
        
        # Method 5: HTML link crawling
        await self._crawl_html_links(client, target_url, max_depth=2)

        # Method 6: JavaScript file analysis
        await self._analyze_javascript_files(client, target_url)

        # Method 7: Directory bruteforcing
        await self._bruteforce_directories(client, target_url)

        # Method 8: Technology-specific endpoint discovery
        await self._discover_tech_specific_endpoints(client, target_url)

    async def _test_common_paths(self, client: httpx.AsyncClient, target_url: str):
        """Test common API paths."""
        for path in self.common_api_paths:
            endpoint = urljoin(target_url, path)
            self.discovered_endpoints.add(endpoint)
            
            # Also test with common extensions
            for ext in ["", "/", "/swagger", "/docs", "/health", "/status"]:
                full_endpoint = endpoint + ext
                self.discovered_endpoints.add(full_endpoint)

    async def _parse_robots_txt(self, client: httpx.AsyncClient, target_url: str):
        """Parse robots.txt for endpoint discovery."""
        try:
            robots_url = urljoin(target_url, "/robots.txt")
            response = await client.get(robots_url)
            
            if response.status_code == 200:
                for line in response.text.split('\n'):
                    if line.startswith('Disallow:') or line.startswith('Allow:'):
                        path = line.split(':', 1)[1].strip()
                        if path and path != '/':
                            endpoint = urljoin(target_url, path)
                            self.discovered_endpoints.add(endpoint)
        except Exception as e:
            self.logger.debug("Failed to parse robots.txt", error=str(e))

    async def _parse_sitemap(self, client: httpx.AsyncClient, target_url: str):
        """Parse sitemap.xml for endpoint discovery."""
        try:
            sitemap_url = urljoin(target_url, "/sitemap.xml")
            response = await client.get(sitemap_url)
            
            if response.status_code == 200:
                root = ET.fromstring(response.text)
                for url_elem in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}url'):
                    loc_elem = url_elem.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                    if loc_elem is not None and loc_elem.text:
                        self.discovered_endpoints.add(loc_elem.text)
        except Exception as e:
            self.logger.debug("Failed to parse sitemap.xml", error=str(e))

    async def _discover_openapi_specs(self, client: httpx.AsyncClient, target_url: str):
        """Discover OpenAPI/Swagger specifications."""
        swagger_paths = [
            "/swagger.json", "/swagger.yaml", "/swagger-ui.html",
            "/api-docs", "/api/swagger.json", "/docs", "/redoc",
            "/v1/swagger.json", "/v2/swagger.json", "/openapi.json"
        ]
        
        for path in swagger_paths:
            try:
                spec_url = urljoin(target_url, path)
                response = await client.get(spec_url)
                
                if response.status_code == 200:
                    await self._parse_openapi_spec(response.text, target_url)
            except Exception as e:
                self.logger.debug("Failed to fetch OpenAPI spec", path=path, error=str(e))

    async def _parse_openapi_spec(self, spec_content: str, base_url: str):
        """Parse OpenAPI specification to extract endpoints."""
        try:
            if spec_content.strip().startswith('{'):
                # JSON format
                spec = json.loads(spec_content)
            else:
                # Assume YAML format (simplified parsing)
                return
            
            # Extract paths from OpenAPI spec
            paths = spec.get('paths', {})
            for path, path_item in paths.items():
                endpoint = urljoin(base_url, path)
                self.discovered_endpoints.add(endpoint)
                
                # Also add endpoints with parameters replaced
                if '{' in path:
                    # Replace path parameters with test values
                    test_path = re.sub(r'\{[^}]+\}', '1', path)
                    test_endpoint = urljoin(base_url, test_path)
                    self.discovered_endpoints.add(test_endpoint)
                    
        except Exception as e:
            self.logger.debug("Failed to parse OpenAPI spec", error=str(e))

    async def _crawl_html_links(self, client: httpx.AsyncClient, target_url: str, max_depth: int = 2):
        """Crawl HTML pages to discover API endpoints."""
        if max_depth <= 0:
            return
            
        try:
            response = await client.get(target_url)
            if response.status_code == 200 and 'text/html' in response.headers.get('content-type', ''):
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Extract links from various sources
                for element in soup.find_all(['a', 'form', 'script']):
                    urls = []
                    
                    if element.name == 'a' and element.get('href'):
                        urls.append(element['href'])
                    elif element.name == 'form' and element.get('action'):
                        urls.append(element['action'])
                    elif element.name == 'script':
                        # Look for API endpoints in JavaScript
                        script_content = element.string or ''
                        api_urls = re.findall(r'["\']/(api|v\d+)/[^"\']*["\']', script_content)
                        urls.extend([url.strip('"\'') for url in api_urls])
                    
                    for url in urls:
                        if url and not url.startswith(('http', 'mailto:', 'tel:')):
                            full_url = urljoin(target_url, url)
                            if '/api' in url.lower() or '/v' in url.lower():
                                self.discovered_endpoints.add(full_url)
                                
        except Exception as e:
            self.logger.debug("Failed to crawl HTML links", error=str(e))

    async def _test_endpoint_with_semaphore(self, semaphore: asyncio.Semaphore, client: httpx.AsyncClient, endpoint: str, analysis) -> List[Dict[str, Any]]:
        """Test endpoint with semaphore for concurrency control."""
        async with semaphore:
            return await self._test_endpoint(client, endpoint, analysis)

    async def _test_endpoint(self, client: httpx.AsyncClient, endpoint: str, analysis) -> List[Dict[str, Any]]:
        """Test a single endpoint for vulnerabilities."""
        vulnerabilities = []
        self.tested_endpoints.add(endpoint)
        
        try:
            # Test 1: Authentication bypass
            auth_vulns = await self._test_authentication_bypass(client, endpoint, analysis)
            vulnerabilities.extend(auth_vulns)
            
            # Test 2: HTTP method testing
            method_vulns = await self._test_http_methods(client, endpoint, analysis)
            vulnerabilities.extend(method_vulns)
            
            # Test 3: Parameter fuzzing
            param_vulns = await self._test_parameter_fuzzing(client, endpoint, analysis)
            vulnerabilities.extend(param_vulns)
            
            # Test 4: CORS misconfiguration
            cors_vulns = await self._test_cors_misconfiguration(client, endpoint, analysis)
            vulnerabilities.extend(cors_vulns)
            
        except Exception as e:
            self.logger.debug("Endpoint test failed", endpoint=endpoint, error=str(e))
            
        return vulnerabilities

    def _add_business_scoring(self, vulnerability: Dict[str, Any], analysis) -> None:
        """Add business context scoring to a vulnerability."""
        vulnerability.update({
            'business_priority': analysis.business_priority.value,
            'endpoint_score': analysis.endpoint_score,
            'is_likely_false_positive': analysis.is_likely_false_positive,
            'false_positive_confidence': analysis.false_positive_confidence,
            'business_reasoning': analysis.reasoning
        })

    async def _test_authentication_bypass(self, client: httpx.AsyncClient, endpoint: str, analysis) -> List[Dict[str, Any]]:
        """Test for authentication bypass vulnerabilities."""
        vulnerabilities = []

        try:
            # Test without authentication
            response = await client.get(endpoint)

            if response.status_code == 200:
                response_text = response.text

                # Skip if this is an SPA returning the same content as the base page
                if self.spa_detection['is_spa']:
                    response_hash = hash(response_text)
                    if response_hash == self.spa_detection['base_content_hash']:
                        # This is just the SPA shell, not a real API endpoint
                        return vulnerabilities

                # Check if this looks like actual API data (JSON, XML, etc.)
                content_type = response.headers.get('content-type', '').lower()
                is_api_response = any(ct in content_type for ct in [
                    'application/json', 'application/xml', 'text/xml',
                    'application/api', 'text/plain'
                ])

                # If it's HTML and looks like the same SPA content, skip
                if 'text/html' in content_type:
                    if (len(response_text) == self.spa_detection['base_content_length'] or
                        any(indicator in response_text.lower() for indicator in self.spa_detection['spa_indicators'])):
                        return vulnerabilities

                # Check if endpoint returns actual sensitive data (not just keywords in HTML)
                response_text_lower = response_text.lower()

                # More specific sensitive data patterns
                sensitive_patterns = [
                    # Actual API responses with sensitive data
                    r'"password"\s*:\s*"[^"]+',
                    r'"token"\s*:\s*"[^"]+',
                    r'"secret"\s*:\s*"[^"]+',
                    r'"api_key"\s*:\s*"[^"]+',
                    r'"database_url"\s*:\s*"[^"]+',
                    # Database dumps or configs
                    r'password\s*=\s*[^\s]+',
                    r'secret\s*=\s*[^\s]+',
                    # User data in JSON/XML
                    r'"users"\s*:\s*\[',
                    r'<users>',
                    # Admin interfaces (not SPA)
                    r'admin\s+panel',
                    r'administration\s+interface'
                ]

                # Check for actual sensitive content patterns
                has_sensitive_data = any(
                    re.search(pattern, response_text_lower) for pattern in sensitive_patterns
                )

                # Also check if it's a real API response with structured data
                if is_api_response or has_sensitive_data:
                    # Additional check: ensure it's not just a generic error page or SPA
                    if (len(response_text) > 100 and  # Not too short
                        not ('<!doctype html>' in response_text_lower and
                             'div id="root"' in response_text_lower)):  # Not SPA shell

                        vuln = self.create_vulnerability(
                            title="Unauthenticated API Endpoint",
                            description=f"API endpoint {endpoint} is accessible without authentication and exposes sensitive data",
                            severity="high",
                            category="Authentication",
                            url=endpoint,
                            method="GET",
                            evidence=response_text[:500],
                            cwe_id="CWE-287",
                            owasp_category="A01:2021 – Broken Access Control",
                            remediation="Implement proper authentication and authorization mechanisms"
                        )

                        # Add business scoring from analysis
                        self._add_business_scoring(vuln, analysis)

                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)

        except Exception as e:
            self.logger.debug("Authentication bypass test failed", endpoint=endpoint, error=str(e))

        return vulnerabilities

    async def _test_http_methods(self, client: httpx.AsyncClient, endpoint: str, analysis) -> List[Dict[str, Any]]:
        """Test dangerous HTTP methods."""
        vulnerabilities = []
        dangerous_methods = ['DELETE', 'PUT', 'PATCH', 'OPTIONS', 'TRACE']

        for method in dangerous_methods:
            try:
                response = await client.request(method, endpoint)

                if response.status_code not in [405, 501]:  # Method not allowed/implemented

                    # Smart filtering for OPTIONS method
                    if method == 'OPTIONS':
                        # Skip OPTIONS if it's just CORS preflight (very common and not dangerous)
                        cors_headers = [
                            'access-control-allow-origin',
                            'access-control-allow-methods',
                            'access-control-allow-headers'
                        ]

                        has_cors_headers = any(
                            header in response.headers for header in cors_headers
                        )

                        # Skip if it's just CORS and the endpoint is likely SPA-related
                        if (has_cors_headers and
                            (self.spa_detection['is_spa'] or analysis.endpoint_score < 70)):
                            continue

                    # Skip if this is an SPA returning the same content
                    if (self.spa_detection['is_spa'] and
                        response.status_code == 200 and
                        hash(response.text) == self.spa_detection['base_content_hash']):
                        continue

                    # Only report if it's actually dangerous
                    is_dangerous = False

                    if method in ['DELETE', 'PUT', 'PATCH']:
                        # These are always concerning if they work
                        is_dangerous = True
                    elif method == 'TRACE':
                        # TRACE can be used for XST attacks
                        is_dangerous = True
                    elif method == 'OPTIONS':
                        # OPTIONS is only dangerous if it reveals sensitive methods or has no CORS protection
                        allow_header = response.headers.get('allow', '')
                        if any(dangerous in allow_header.upper() for dangerous in ['DELETE', 'PUT', 'PATCH']):
                            is_dangerous = True

                    if is_dangerous:
                        vuln = self.create_vulnerability(
                            title=f"Dangerous HTTP Method Allowed: {method}",
                            description=f"Endpoint {endpoint} accepts {method} method which could be dangerous",
                            severity="medium" if method in ['OPTIONS', 'TRACE'] else "high",
                            category="Configuration",
                            url=endpoint,
                            method=method,
                            evidence=f"HTTP {method} returned status {response.status_code}",
                            cwe_id="CWE-16",
                            remediation=f"Disable {method} method if not required"
                        )

                        self._add_business_scoring(vuln, analysis)
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)

            except Exception as e:
                self.logger.debug("HTTP method test failed", method=method, endpoint=endpoint, error=str(e))

        return vulnerabilities

    async def _test_parameter_fuzzing(self, client: httpx.AsyncClient, endpoint: str, analysis) -> List[Dict[str, Any]]:
        """Test parameters for injection vulnerabilities."""
        vulnerabilities = []
        
        # Test URL parameters
        parsed_url = urlparse(endpoint)
        if parsed_url.query:
            params = parse_qs(parsed_url.query)
            
            for param_name, param_values in params.items():
                # Test SQL injection
                sqli_vulns = await self._test_sql_injection(client, endpoint, param_name, analysis)
                vulnerabilities.extend(sqli_vulns)
                
                # Test XSS
                xss_vulns = await self._test_xss(client, endpoint, param_name, analysis)
                vulnerabilities.extend(xss_vulns)
                
                # Test command injection
                cmd_vulns = await self._test_command_injection(client, endpoint, param_name, analysis)
                vulnerabilities.extend(cmd_vulns)
        
        return vulnerabilities

    async def _test_sql_injection(self, client: httpx.AsyncClient, endpoint: str, param_name: str, analysis) -> List[Dict[str, Any]]:
        """Test for SQL injection vulnerabilities."""
        vulnerabilities = []
        
        for payload in self.sqli_payloads:
            try:
                params = {param_name: payload}
                response = await client.get(endpoint, params=params)
                
                # Check for SQL error indicators
                error_indicators = [
                    'sql syntax', 'mysql_fetch', 'ora-', 'postgresql',
                    'sqlite_', 'odbc', 'jdbc', 'warning: mysql',
                    'error in your sql syntax', 'invalid query'
                ]
                
                response_text = response.text.lower()
                if any(indicator in response_text for indicator in error_indicators):
                    vuln = self.create_vulnerability(
                        title="SQL Injection Vulnerability",
                        description=f"Parameter '{param_name}' appears to be vulnerable to SQL injection",
                        severity="critical",
                        category="Injection",
                        url=endpoint,
                        method="GET",
                        parameter=param_name,
                        payload=payload,
                        evidence=response_text[:500],
                        cwe_id="CWE-89",
                        owasp_category="A03:2021 – Injection",
                        remediation="Use parameterized queries and input validation"
                    )
                    self._add_business_scoring(vuln, analysis)
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
                    break  # Found vulnerability, no need to test more payloads
                    
            except Exception as e:
                self.logger.debug("SQL injection test failed", error=str(e))
                
        return vulnerabilities

    async def _test_xss(self, client: httpx.AsyncClient, endpoint: str, param_name: str, analysis) -> List[Dict[str, Any]]:
        """Test for XSS vulnerabilities."""
        vulnerabilities = []
        
        for payload in self.xss_payloads:
            try:
                params = {param_name: payload}
                response = await client.get(endpoint, params=params)
                
                # Check if payload is reflected in response
                if payload in response.text:
                    vuln = self.create_vulnerability(
                        title="Cross-Site Scripting (XSS) Vulnerability",
                        description=f"Parameter '{param_name}' is vulnerable to XSS attacks",
                        severity="high",
                        category="Injection",
                        url=endpoint,
                        method="GET",
                        parameter=param_name,
                        payload=payload,
                        evidence=f"Payload reflected in response: {payload}",
                        cwe_id="CWE-79",
                        owasp_category="A03:2021 – Injection",
                        remediation="Implement proper output encoding and input validation"
                    )
                    self._add_business_scoring(vuln, analysis)
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
                    break
                    
            except Exception as e:
                self.logger.debug("XSS test failed", error=str(e))
                
        return vulnerabilities

    async def _test_command_injection(self, client: httpx.AsyncClient, endpoint: str, param_name: str, analysis) -> List[Dict[str, Any]]:
        """Test for command injection vulnerabilities."""
        vulnerabilities = []
        
        for payload in self.command_payloads:
            try:
                params = {param_name: payload}
                response = await client.get(endpoint, params=params)
                
                # Check for command execution indicators
                cmd_indicators = [
                    'root:', '/bin/', '/usr/', 'uid=', 'gid=',
                    'volume serial number', 'directory of'
                ]
                
                response_text = response.text.lower()
                if any(indicator in response_text for indicator in cmd_indicators):
                    vuln = self.create_vulnerability(
                        title="Command Injection Vulnerability",
                        description=f"Parameter '{param_name}' appears to be vulnerable to command injection",
                        severity="critical",
                        category="Injection",
                        url=endpoint,
                        method="GET",
                        parameter=param_name,
                        payload=payload,
                        evidence=response_text[:500],
                        cwe_id="CWE-78",
                        owasp_category="A03:2021 – Injection",
                        remediation="Avoid executing system commands with user input"
                    )
                    self._add_business_scoring(vuln, analysis)
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
                    break
                    
            except Exception as e:
                self.logger.debug("Command injection test failed", error=str(e))
                
        return vulnerabilities

    async def _test_cors_misconfiguration(self, client: httpx.AsyncClient, endpoint: str, analysis) -> List[Dict[str, Any]]:
        """Test for CORS misconfiguration."""
        vulnerabilities = []
        
        try:
            headers = {'Origin': 'https://evil.com'}
            response = await client.options(endpoint, headers=headers)
            
            cors_headers = {
                'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
                'access-control-allow-credentials': response.headers.get('access-control-allow-credentials'),
                'access-control-allow-methods': response.headers.get('access-control-allow-methods')
            }
            
            # Check for dangerous CORS configurations
            if cors_headers['access-control-allow-origin'] == '*' and cors_headers['access-control-allow-credentials'] == 'true':
                vuln = self.create_vulnerability(
                    title="CORS Misconfiguration",
                    description="CORS policy allows all origins with credentials",
                    severity="high",
                    category="Configuration",
                    url=endpoint,
                    method="OPTIONS",
                    evidence=f"CORS headers: {cors_headers}",
                    cwe_id="CWE-942",
                    remediation="Configure CORS to allow only trusted origins"
                )
                self._add_business_scoring(vuln, analysis)
                vulnerabilities.append(vuln)
                self.log_vulnerability(vuln)
                
        except Exception as e:
            self.logger.debug("CORS test failed", error=str(e))

        return vulnerabilities

    async def _analyze_javascript_files(self, client: httpx.AsyncClient, target_url: str):
        """Analyze JavaScript files to discover API endpoints."""
        try:
            # First get the main page to find JS files
            response = await client.get(target_url)

            if response.status_code == 200:
                # Extract JavaScript file URLs
                js_files = re.findall(r'<script[^>]*src=[\'"]([^\'"]*\.js)[\'"]', response.text)

                # Also look for inline script tags with API calls
                inline_scripts = re.findall(r'<script[^>]*>(.*?)</script>', response.text, re.DOTALL)

                # Analyze external JS files
                for js_file in js_files[:10]:  # Limit to first 10 files
                    if not js_file.startswith('http'):
                        js_file = urljoin(target_url, js_file)

                    await self._extract_endpoints_from_js(client, js_file)

                # Analyze inline scripts
                for script_content in inline_scripts:
                    self._extract_endpoints_from_js_content(script_content, target_url)

        except Exception as e:
            self.logger.debug("JavaScript analysis failed", error=str(e))

    async def _extract_endpoints_from_js(self, client: httpx.AsyncClient, js_url: str):
        """Extract API endpoints from JavaScript file."""
        try:
            response = await client.get(js_url)

            if response.status_code == 200:
                self._extract_endpoints_from_js_content(response.text, js_url)

        except Exception as e:
            self.logger.debug("Failed to analyze JS file", js_url=js_url, error=str(e))

    def _extract_endpoints_from_js_content(self, js_content: str, base_url: str):
        """Extract endpoints from JavaScript content."""
        # Common patterns for API endpoints in JavaScript
        patterns = [
            # fetch() calls
            r'fetch\s*\(\s*[\'"`]([^\'"`]+)[\'"`]',
            # axios calls
            r'axios\.\w+\s*\(\s*[\'"`]([^\'"`]+)[\'"`]',
            # jQuery AJAX
            r'\$\.(?:get|post|ajax)\s*\(\s*[\'"`]([^\'"`]+)[\'"`]',
            # XMLHttpRequest
            r'\.open\s*\(\s*[\'"`]\w+[\'"`]\s*,\s*[\'"`]([^\'"`]+)[\'"`]',
            # API base URLs
            r'(?:api|API)_?(?:URL|url|Base|base)\s*[:=]\s*[\'"`]([^\'"`]+)[\'"`]',
            # Endpoint definitions
            r'[\'"`](/api/[^\'"`]*)[\'"`]',
            r'[\'"`](/v\d+/[^\'"`]*)[\'"`]',
            # REST endpoints
            r'[\'"`]([^\'"`]*(?:users|posts|comments|auth|login|register|admin)[^\'"`]*)[\'"`]',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)

            for match in matches:
                if match.startswith('/'):
                    # Relative URL
                    endpoint = urljoin(base_url, match)
                elif match.startswith('http'):
                    # Absolute URL
                    endpoint = match
                else:
                    # Assume relative
                    endpoint = urljoin(base_url, '/' + match.lstrip('/'))

                # Filter out obvious non-API endpoints
                if any(ext in endpoint.lower() for ext in ['.css', '.js', '.png', '.jpg', '.gif', '.ico']):
                    continue

                self.discovered_endpoints.add(endpoint)

    async def _bruteforce_directories(self, client: httpx.AsyncClient, target_url: str):
        """Bruteforce common directories and files."""
        common_paths = [
            # API directories
            '/api', '/api/v1', '/api/v2', '/api/v3',
            '/rest', '/rest/v1', '/rest/v2',
            '/graphql', '/gql',
            '/webhook', '/webhooks',
            '/callback', '/callbacks',

            # Admin and management
            '/admin', '/admin/api', '/management',
            '/console', '/dashboard',
            '/status', '/health', '/metrics',
            '/info', '/version', '/ping',

            # Authentication
            '/auth', '/login', '/logout', '/register',
            '/oauth', '/oauth2', '/token', '/refresh',
            '/sso', '/saml', '/ldap',

            # Common resources
            '/users', '/user', '/accounts', '/account',
            '/posts', '/comments', '/messages',
            '/files', '/uploads', '/downloads',
            '/search', '/query',

            # Configuration and docs
            '/config', '/configuration', '/settings',
            '/docs', '/documentation', '/help',
            '/swagger', '/openapi', '/redoc',

            # Development and testing
            '/test', '/testing', '/dev', '/development',
            '/staging', '/debug', '/trace',

            # Common files
            '/robots.txt', '/sitemap.xml', '/.well-known',
            '/favicon.ico', '/manifest.json',

            # Backup and temp files
            '/backup', '/backups', '/tmp', '/temp',
            '/old', '/archive', '/legacy'
        ]

        # Test paths with different HTTP methods
        semaphore = asyncio.Semaphore(10)  # Limit concurrent requests
        tasks = []

        for path in common_paths:
            endpoint = urljoin(target_url, path)
            task = self._test_path_with_semaphore(semaphore, client, endpoint)
            tasks.append(task)

        await asyncio.gather(*tasks, return_exceptions=True)

    async def _test_path_with_semaphore(self, semaphore: asyncio.Semaphore, client: httpx.AsyncClient, endpoint: str):
        """Test path with semaphore control."""
        async with semaphore:
            try:
                response = await client.get(endpoint)

                # Add endpoint if it returns interesting status codes
                if response.status_code in [200, 201, 202, 301, 302, 401, 403]:
                    self.discovered_endpoints.add(endpoint)

                    # If it's a directory, try to find more endpoints
                    if response.status_code == 200 and endpoint.endswith('/'):
                        await self._discover_directory_contents(client, endpoint)

            except Exception:
                pass  # Endpoint not accessible

    async def _discover_directory_contents(self, client: httpx.AsyncClient, directory_url: str):
        """Try to discover contents of a directory."""
        try:
            response = await client.get(directory_url)

            if response.status_code == 200:
                content = response.text.lower()

                # Look for directory listing
                if 'index of' in content or '<pre>' in content:
                    # Extract file/directory names from listing
                    links = re.findall(r'href=[\'"]([^\'">]+)[\'"]', response.text)

                    for link in links:
                        if not link.startswith(('http', 'mailto:', '..', '/')):
                            endpoint = urljoin(directory_url, link)
                            self.discovered_endpoints.add(endpoint)

        except Exception:
            pass

    async def _discover_tech_specific_endpoints(self, client: httpx.AsyncClient, target_url: str):
        """Discover endpoints specific to detected technologies."""
        try:
            # First, try to detect the technology stack
            response = await client.get(target_url)

            if response.status_code == 200:
                headers = response.headers
                content = response.text.lower()

                # Detect technologies and add specific endpoints
                tech_endpoints = []

                # WordPress
                if 'wp-content' in content or 'wordpress' in content:
                    tech_endpoints.extend([
                        '/wp-json/wp/v2/', '/wp-admin/', '/wp-login.php',
                        '/wp-content/', '/wp-includes/', '/xmlrpc.php'
                    ])

                # Django
                if 'django' in headers.get('server', '').lower() or 'csrftoken' in content:
                    tech_endpoints.extend([
                        '/admin/', '/api/', '/accounts/', '/static/',
                        '/__debug__/', '/media/'
                    ])

                # Laravel
                if 'laravel' in content or 'laravel_session' in content:
                    tech_endpoints.extend([
                        '/api/', '/admin/', '/storage/', '/vendor/',
                        '/artisan', '/.env'
                    ])

                # Express.js/Node.js
                if 'express' in headers.get('x-powered-by', '').lower():
                    tech_endpoints.extend([
                        '/api/', '/admin/', '/auth/', '/users/',
                        '/node_modules/', '/package.json'
                    ])

                # Spring Boot
                if 'spring' in content or 'java' in headers.get('server', '').lower():
                    tech_endpoints.extend([
                        '/actuator/', '/api/', '/admin/', '/management/',
                        '/health', '/info', '/metrics'
                    ])

                # React/Next.js
                if 'react' in content or '_next' in content:
                    tech_endpoints.extend([
                        '/api/', '/_next/', '/static/', '/public/',
                        '/manifest.json', '/service-worker.js'
                    ])

                # Add discovered tech-specific endpoints
                for endpoint_path in tech_endpoints:
                    endpoint = urljoin(target_url, endpoint_path)
                    self.discovered_endpoints.add(endpoint)

        except Exception as e:
            self.logger.debug("Technology-specific discovery failed", error=str(e))