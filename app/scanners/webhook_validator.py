import asyncio
import ssl
import hashlib
import hmac
import time
from typing import List, Dict, Any, Optional
from datetime import datetime
import httpx
from urllib.parse import urlparse
from .base_scanner import BaseScanner, ScannerResult


class WebhookValidator(BaseScanner):
    """Scanner for webhook security vulnerabilities."""
    
    def __init__(self, timeout: int = 300, concurrent_requests: int = 10):
        super().__init__(timeout, concurrent_requests)
        
        # Test payloads for various attack types
        self.test_payloads = {
            'xss': '<script>alert("xss")</script>',
            'sql_injection': "'; DROP TABLE users; --",
            'command_injection': '; cat /etc/passwd',
            'xxe': '<?xml version="1.0"?><!DOCTYPE foo [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><foo>&xxe;</foo>',
            'large_payload': 'A' * 1000000,  # 1MB payload
            'json_bomb': '{"a":' * 10000 + '1' + '}' * 10000
        }
        
        # Common webhook paths to test
        self.webhook_paths = [
            '/webhook', '/webhooks', '/hook', '/hooks',
            '/callback', '/notify', '/notification',
            '/api/webhook', '/api/webhooks', '/api/hook',
            '/github-webhook', '/slack-webhook', '/stripe-webhook',
            '/payload', '/events', '/ping'
        ]

    async def scan(self, target_url: str, **kwargs) -> ScannerResult:
        """Main webhook security validation method."""
        start_time = time.time()
        vulnerabilities = []
        
        try:
            async with httpx.AsyncClient(
                timeout=self.timeout,
                limits=httpx.Limits(max_connections=self.concurrent_requests),
                verify=False  # We'll manually verify SSL
            ) as client:
                
                # Step 1: Discover webhook endpoints
                webhook_urls = await self._discover_webhook_endpoints(client, target_url)
                
                # Step 2: Test each discovered webhook
                for webhook_url in webhook_urls:
                    # SSL/TLS Security Testing
                    ssl_vulns = await self._test_ssl_security(webhook_url)
                    vulnerabilities.extend(ssl_vulns)
                    
                    # Authentication Testing
                    auth_vulns = await self._test_webhook_authentication(client, webhook_url)
                    vulnerabilities.extend(auth_vulns)
                    
                    # Input Validation Testing
                    input_vulns = await self._test_input_validation(client, webhook_url)
                    vulnerabilities.extend(input_vulns)
                    
                    # Rate Limiting Testing
                    rate_vulns = await self._test_rate_limiting(client, webhook_url)
                    vulnerabilities.extend(rate_vulns)
                    
                    # Replay Attack Testing
                    replay_vulns = await self._test_replay_protection(client, webhook_url)
                    vulnerabilities.extend(replay_vulns)
                    
                    # Response Information Leakage
                    info_vulns = await self._test_information_leakage(client, webhook_url)
                    vulnerabilities.extend(info_vulns)
            
            scan_duration = time.time() - start_time
            
            return ScannerResult(
                vulnerabilities=vulnerabilities,
                metadata={
                    "webhook_endpoints_found": len(webhook_urls),
                    "webhook_urls": webhook_urls,
                    "tests_performed": [
                        "ssl_security", "authentication", "input_validation",
                        "rate_limiting", "replay_protection", "information_leakage"
                    ]
                },
                scan_duration=scan_duration,
                timestamp=datetime.utcnow(),
                scanner_name="webhook_validator",
                target_url=target_url,
                success=True
            )
            
        except Exception as e:
            scan_duration = time.time() - start_time
            self.logger.error("Webhook validation failed", error=str(e))
            
            return ScannerResult(
                vulnerabilities=[],
                metadata={},
                scan_duration=scan_duration,
                timestamp=datetime.utcnow(),
                scanner_name="webhook_validator",
                target_url=target_url,
                success=False,
                error_message=str(e)
            )

    async def _discover_webhook_endpoints(self, client: httpx.AsyncClient, target_url: str) -> List[str]:
        """Discover webhook endpoints."""
        webhook_urls = []
        
        # Test common webhook paths
        for path in self.webhook_paths:
            test_url = f"{target_url.rstrip('/')}{path}"
            
            try:
                # Use HEAD request to check if endpoint exists
                response = await client.head(test_url)
                
                # Consider endpoint as potential webhook if it doesn't return 404
                if response.status_code != 404:
                    webhook_urls.append(test_url)
                    
            except Exception:
                pass  # Endpoint not accessible
        
        # Also test the base URL as it might be a webhook
        webhook_urls.append(target_url)
        
        return list(set(webhook_urls))  # Remove duplicates

    async def _test_ssl_security(self, webhook_url: str) -> List[Dict[str, Any]]:
        """Test SSL/TLS security of webhook endpoint."""
        vulnerabilities = []
        
        parsed_url = urlparse(webhook_url)
        if parsed_url.scheme != 'https':
            # Webhook should use HTTPS
            vuln = self.create_vulnerability(
                title="Webhook Not Using HTTPS",
                description="Webhook endpoint is not using HTTPS encryption",
                severity="high",
                category="Transport Security",
                url=webhook_url,
                evidence=f"URL scheme: {parsed_url.scheme}",
                cwe_id="CWE-319",
                owasp_category="A02:2021 – Cryptographic Failures",
                remediation="Configure webhook to use HTTPS with valid SSL certificate"
            )
            vulnerabilities.append(vuln)
            self.log_vulnerability(vuln)
            return vulnerabilities
        
        try:
            # Test SSL certificate
            hostname = parsed_url.hostname
            port = parsed_url.port or 443
            
            # Create SSL context
            context = ssl.create_default_context()
            
            # Test with different SSL configurations
            ssl_tests = [
                {"check_hostname": True, "verify_mode": ssl.CERT_REQUIRED, "name": "strict"},
                {"check_hostname": False, "verify_mode": ssl.CERT_NONE, "name": "permissive"}
            ]
            
            for test_config in ssl_tests:
                try:
                    test_context = ssl.create_default_context()
                    test_context.check_hostname = test_config["check_hostname"]
                    test_context.verify_mode = test_config["verify_mode"]
                    
                    # Connect and get certificate info
                    import socket
                    with socket.create_connection((hostname, port), timeout=10) as sock:
                        with test_context.wrap_socket(sock, server_hostname=hostname) as ssock:
                            cert = ssock.getpeercert()
                            cipher = ssock.cipher()
                            
                            # Check certificate expiration
                            if cert:
                                import datetime
                                not_after = datetime.datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                                days_until_expiry = (not_after - datetime.datetime.now()).days
                                
                                if days_until_expiry < 30:
                                    vuln = self.create_vulnerability(
                                        title="SSL Certificate Expiring Soon",
                                        description=f"SSL certificate expires in {days_until_expiry} days",
                                        severity="medium",
                                        category="Transport Security",
                                        url=webhook_url,
                                        evidence=f"Certificate expires: {cert['notAfter']}",
                                        cwe_id="CWE-295",
                                        remediation="Renew SSL certificate before expiration"
                                    )
                                    vulnerabilities.append(vuln)
                                    self.log_vulnerability(vuln)
                            
                            # Check cipher strength
                            if cipher:
                                cipher_name = cipher[0]
                                # Check for weak ciphers
                                weak_ciphers = ['RC4', 'DES', '3DES', 'MD5']
                                if any(weak in cipher_name.upper() for weak in weak_ciphers):
                                    vuln = self.create_vulnerability(
                                        title="Weak SSL Cipher",
                                        description=f"Webhook uses weak SSL cipher: {cipher_name}",
                                        severity="medium", 
                                        category="Transport Security",
                                        url=webhook_url,
                                        evidence=f"Cipher: {cipher_name}",
                                        cwe_id="CWE-327",
                                        remediation="Configure stronger SSL ciphers"
                                    )
                                    vulnerabilities.append(vuln)
                                    self.log_vulnerability(vuln)
                            
                            break  # Successful connection
                            
                except ssl.SSLError as ssl_error:
                    if test_config["name"] == "strict":
                        # SSL verification failed with strict settings
                        vuln = self.create_vulnerability(
                            title="SSL Certificate Verification Failed",
                            description="SSL certificate verification failed",
                            severity="high",
                            category="Transport Security",
                            url=webhook_url,
                            evidence=f"SSL Error: {str(ssl_error)}",
                            cwe_id="CWE-295",
                            remediation="Fix SSL certificate configuration"
                        )
                        vulnerabilities.append(vuln)
                        self.log_vulnerability(vuln)
                        
        except Exception as e:
            self.logger.debug("SSL test failed", error=str(e))
        
        return vulnerabilities

    async def _test_webhook_authentication(self, client: httpx.AsyncClient, webhook_url: str) -> List[Dict[str, Any]]:
        """Test webhook authentication mechanisms."""
        vulnerabilities = []
        
        try:
            # Test 1: No authentication
            response = await client.post(webhook_url, json={"test": "payload"})
            
            if response.status_code in [200, 201, 202]:
                vuln = self.create_vulnerability(
                    title="Webhook Accepts Unauthenticated Requests",
                    description="Webhook endpoint accepts requests without any authentication",
                    severity="high",
                    category="Authentication",
                    url=webhook_url,
                    method="POST",
                    evidence=f"HTTP {response.status_code}: Request accepted without authentication",
                    cwe_id="CWE-287",
                    owasp_category="A07:2021 – Identification and Authentication Failures",
                    remediation="Implement webhook signature verification or API key authentication"
                )
                vulnerabilities.append(vuln)
                self.log_vulnerability(vuln)
            
            # Test 2: Weak signature verification
            weak_secrets = ['secret', 'webhook', 'password', '123456', 'key']
            
            for secret in weak_secrets:
                # Generate HMAC signature
                payload = '{"test": "payload"}'
                signature = hmac.new(
                    secret.encode(),
                    payload.encode(),
                    hashlib.sha256
                ).hexdigest()
                
                headers = {
                    'X-Hub-Signature-256': f'sha256={signature}',
                    'X-Signature': signature,
                    'Content-Type': 'application/json'
                }
                
                auth_response = await client.post(webhook_url, data=payload, headers=headers)
                
                if auth_response.status_code in [200, 201, 202]:
                    vuln = self.create_vulnerability(
                        title="Webhook Uses Weak Secret",
                        description=f"Webhook accepts requests signed with weak secret: '{secret}'",
                        severity="critical",
                        category="Authentication",
                        url=webhook_url,
                        evidence=f"Weak secret '{secret}' successfully authenticated request",
                        cwe_id="CWE-521",
                        remediation="Use a strong, random secret for webhook signature verification"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
                    break  # Found weak secret, no need to test others
        
        except Exception as e:
            self.logger.debug("Authentication test failed", error=str(e))
        
        return vulnerabilities

    async def _test_input_validation(self, client: httpx.AsyncClient, webhook_url: str) -> List[Dict[str, Any]]:
        """Test webhook input validation."""
        vulnerabilities = []
        
        for payload_type, payload in self.test_payloads.items():
            try:
                if payload_type == 'large_payload':
                    # Test large payload
                    response = await client.post(
                        webhook_url, 
                        data=payload,
                        headers={'Content-Type': 'text/plain'}
                    )
                elif payload_type == 'json_bomb':
                    # Test JSON bomb
                    response = await client.post(
                        webhook_url,
                        data=payload,
                        headers={'Content-Type': 'application/json'}
                    )
                else:
                    # Test other payloads
                    response = await client.post(
                        webhook_url,
                        json={"data": payload, "test": payload_type}
                    )
                
                # Check for signs of successful injection
                response_text = response.text.lower()
                
                if payload_type == 'xss' and any(indicator in response_text for indicator in ['<script>', 'alert', 'xss']):
                    vuln = self.create_vulnerability(
                        title="Webhook Vulnerable to XSS",
                        description="Webhook reflects unescaped user input",
                        severity="medium",
                        category="Injection",
                        url=webhook_url,
                        method="POST",
                        payload=payload,
                        evidence=f"XSS payload reflected in response: {response_text[:200]}",
                        cwe_id="CWE-79",
                        owasp_category="A03:2021 – Injection",
                        remediation="Implement proper input validation and output encoding"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
                
                elif payload_type == 'sql_injection' and any(indicator in response_text for indicator in ['sql', 'database', 'mysql', 'postgres']):
                    vuln = self.create_vulnerability(
                        title="Webhook Potentially Vulnerable to SQL Injection",
                        description="Webhook may be processing SQL injection payload",
                        severity="high",
                        category="Injection",
                        url=webhook_url,
                        method="POST",
                        payload=payload,
                        evidence=f"Potential SQL error in response: {response_text[:200]}",
                        cwe_id="CWE-89",
                        owasp_category="A03:2021 – Injection",
                        remediation="Use parameterized queries and input validation"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
                
                elif payload_type == 'large_payload' and response.status_code in [200, 201, 202]:
                    vuln = self.create_vulnerability(
                        title="Webhook Accepts Large Payloads",
                        description="Webhook accepts very large payloads without size limits",
                        severity="medium",
                        category="Resource Management",
                        url=webhook_url,
                        evidence=f"Accepted {len(payload)} byte payload",
                        cwe_id="CWE-400",
                        remediation="Implement payload size limits"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
                
            except httpx.TimeoutException:
                if payload_type in ['large_payload', 'json_bomb']:
                    vuln = self.create_vulnerability(
                        title="Webhook Vulnerable to DoS",
                        description=f"Webhook times out when processing {payload_type}",
                        severity="medium",
                        category="Resource Management",
                        url=webhook_url,
                        evidence=f"Request timed out with {payload_type}",
                        cwe_id="CWE-400",
                        remediation="Implement request size limits and timeouts"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
            except Exception as e:
                self.logger.debug("Input validation test failed", payload_type=payload_type, error=str(e))
        
        return vulnerabilities

    async def _test_rate_limiting(self, client: httpx.AsyncClient, webhook_url: str) -> List[Dict[str, Any]]:
        """Test webhook rate limiting."""
        vulnerabilities = []
        
        try:
            # Send multiple requests quickly
            start_time = time.time()
            responses = []
            
            tasks = []
            for i in range(20):  # Send 20 requests
                task = client.post(webhook_url, json={"test": f"request_{i}"})
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # Count successful responses
            successful_responses = [
                r for r in responses 
                if not isinstance(r, Exception) and r.status_code in [200, 201, 202]
            ]
            
            rate_limited_responses = [
                r for r in responses
                if not isinstance(r, Exception) and r.status_code == 429
            ]
            
            if len(successful_responses) > 15:  # Most requests succeeded
                vuln = self.create_vulnerability(
                    title="Webhook Lacks Rate Limiting",
                    description="Webhook does not implement rate limiting",
                    severity="medium",
                    category="Resource Management",
                    url=webhook_url,
                    evidence=f"Processed {len(successful_responses)}/20 requests in {end_time - start_time:.2f} seconds",
                    cwe_id="CWE-770",
                    remediation="Implement rate limiting to prevent abuse"
                )
                vulnerabilities.append(vuln)
                self.log_vulnerability(vuln)
            
        except Exception as e:
            self.logger.debug("Rate limiting test failed", error=str(e))
        
        return vulnerabilities

    async def _test_replay_protection(self, client: httpx.AsyncClient, webhook_url: str) -> List[Dict[str, Any]]:
        """Test webhook replay attack protection."""
        vulnerabilities = []
        
        try:
            # Send the same request twice
            payload = {"timestamp": int(time.time()), "data": "test_replay"}
            
            # First request
            response1 = await client.post(webhook_url, json=payload)
            
            # Wait a bit and send the same request again
            await asyncio.sleep(1)
            response2 = await client.post(webhook_url, json=payload)
            
            # If both requests succeed, webhook might be vulnerable to replay attacks
            if (response1.status_code in [200, 201, 202] and 
                response2.status_code in [200, 201, 202]):
                
                # Check if responses are identical (indicating no replay protection)
                if response1.text == response2.text:
                    vuln = self.create_vulnerability(
                        title="Webhook Vulnerable to Replay Attacks",
                        description="Webhook processes identical requests without replay protection",
                        severity="medium",
                        category="Authentication",
                        url=webhook_url,
                        evidence="Identical requests processed successfully twice",
                        cwe_id="CWE-294",
                        remediation="Implement nonce or timestamp validation to prevent replay attacks"
                    )
                    vulnerabilities.append(vuln)
                    self.log_vulnerability(vuln)
        
        except Exception as e:
            self.logger.debug("Replay protection test failed", error=str(e))
        
        return vulnerabilities

    async def _test_information_leakage(self, client: httpx.AsyncClient, webhook_url: str) -> List[Dict[str, Any]]:
        """Test for information leakage in webhook responses."""
        vulnerabilities = []
        
        try:
            # Test with invalid JSON
            invalid_json = '{"invalid": json}'
            response = await client.post(
                webhook_url,
                data=invalid_json,
                headers={'Content-Type': 'application/json'}
            )
            
            # Check for detailed error messages
            response_text = response.text.lower()
            error_indicators = [
                'traceback', 'stack trace', 'exception', 'error',
                'file not found', 'path', 'directory', 'server',
                'database', 'sql', 'connection', 'internal'
            ]
            
            if any(indicator in response_text for indicator in error_indicators):
                vuln = self.create_vulnerability(
                    title="Webhook Leaks Internal Information",
                    description="Webhook returns detailed error messages that may reveal internal information",
                    severity="low",
                    category="Information Disclosure",
                    url=webhook_url,
                    evidence=f"Error response: {response.text[:200]}",
                    cwe_id="CWE-209",
                    remediation="Return generic error messages to avoid information leakage"
                )
                vulnerabilities.append(vuln)
                self.log_vulnerability(vuln)
            
            # Test for server version disclosure
            server_header = response.headers.get('server', '').lower()
            if server_header and any(server in server_header for server in ['nginx', 'apache', 'iis']):
                vuln = self.create_vulnerability(
                    title="Server Version Disclosure",
                    description="Webhook response reveals server software version",
                    severity="low",
                    category="Information Disclosure",
                    url=webhook_url,
                    evidence=f"Server header: {response.headers.get('server')}",
                    cwe_id="CWE-200",
                    remediation="Remove or obfuscate server version headers"
                )
                vulnerabilities.append(vuln)
                self.log_vulnerability(vuln)
        
        except Exception as e:
            self.logger.debug("Information leakage test failed", error=str(e))
        
        return vulnerabilities