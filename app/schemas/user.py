from typing import Optional
from pydantic import BaseModel, EmailStr, Field
from datetime import datetime


class UserBase(BaseModel):
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=50)
    full_name: Optional[str] = None
    is_active: bool = True


class UserCreate(UserBase):
    password: str = Field(..., min_length=8, max_length=100)


class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    full_name: Optional[str] = None
    phone: Optional[str] = None
    location: Optional[str] = None
    is_active: Optional[bool] = None
    rate_limit_per_minute: Optional[int] = Field(None, ge=1, le=1000)
    max_concurrent_scans: Optional[int] = Field(None, ge=1, le=20)


class ChangePassword(BaseModel):
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=100)


class NotificationSettings(BaseModel):
    email_notifications: Optional[bool] = None
    browser_notifications: Optional[bool] = None
    scan_completion: Optional[bool] = None
    vulnerability_alerts: Optional[bool] = None
    weekly_reports: Optional[bool] = None


class User(UserBase):
    id: int
    phone: Optional[str] = None
    location: Optional[str] = None
    api_key: Optional[str] = None
    rate_limit_per_minute: int
    max_concurrent_scans: int
    email_notifications: Optional[bool] = True
    browser_notifications: Optional[bool] = True
    scan_completion: Optional[bool] = True
    vulnerability_alerts: Optional[bool] = True
    weekly_reports: Optional[bool] = False
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None

    class Config:
        from_attributes = True


class UserInDB(User):
    hashed_password: str