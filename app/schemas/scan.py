from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime
from enum import Enum


class ScanStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ScanType(str, Enum):
    API_ENDPOINTS = "api_endpoints"
    DATABASE_SECURITY = "database_security"
    SECRETS = "secrets"
    FIREBASE = "firebase"
    WEBHOOKS = "webhooks"
    COMPREHENSIVE = "comprehensive"


class VulnerabilitySeverity(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class BusinessPriority(str, Enum):
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


# Scan Schemas
class ScanBase(BaseModel):
    target_url: HttpUrl
    scan_type: ScanType
    scan_name: Optional[str] = None
    scan_description: Optional[str] = None
    max_depth: int = Field(default=3, ge=1, le=10)
    timeout_seconds: int = Field(default=300, ge=30, le=3600)
    concurrent_requests: int = Field(default=10, ge=1, le=50)
    custom_headers: Optional[Dict[str, str]] = None


class ScanCreate(ScanBase):
    pass


class ScanUpdate(BaseModel):
    scan_name: Optional[str] = None
    scan_description: Optional[str] = None
    status: Optional[ScanStatus] = None


class Scan(ScanBase):
    id: int
    user_id: int
    status: ScanStatus
    progress_percentage: int = 0
    error_message: Optional[str] = None
    total_vulnerabilities: int = 0
    critical_count: int = 0
    high_count: int = 0
    medium_count: int = 0
    low_count: int = 0
    security_grade: Optional[str] = Field(None, description="Security grade (A+, A, B+, B, C+, C, D, F)")
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ScanInDB(Scan):
    pass


# ScanResult Schemas
class ScanResultBase(BaseModel):
    scanner_name: str
    raw_data: Dict[str, Any]
    processed_data: Dict[str, Any]
    metadata: Optional[Dict[str, Any]] = None


class ScanResultCreate(ScanResultBase):
    scan_id: int


class ScanResult(ScanResultBase):
    id: int
    scan_id: int
    created_at: datetime

    class Config:
        from_attributes = True


class ScanResultInDB(ScanResult):
    pass


# Vulnerability Schemas
class VulnerabilityBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=1)
    severity: VulnerabilitySeverity
    category: str = Field(..., min_length=1, max_length=100)
    url: Optional[str] = None
    method: Optional[str] = None
    parameter: Optional[str] = None
    payload: Optional[str] = None
    evidence: Optional[str] = None
    cvss_score: Optional[str] = None
    cwe_id: Optional[str] = None
    owasp_category: Optional[str] = None
    remediation: Optional[str] = None
    references: Optional[List[str]] = None
    business_priority: BusinessPriority = BusinessPriority.MEDIUM
    endpoint_score: int = Field(default=50, ge=0, le=100)
    is_likely_false_positive: bool = False
    false_positive_confidence: int = Field(default=0, ge=0, le=100)


class VulnerabilityCreate(VulnerabilityBase):
    scan_id: int


class VulnerabilityUpdate(BaseModel):
    is_false_positive: Optional[bool] = None
    is_resolved: Optional[bool] = None
    resolution_notes: Optional[str] = None
    business_priority: Optional[BusinessPriority] = None
    endpoint_score: Optional[int] = Field(default=None, ge=0, le=100)


class Vulnerability(VulnerabilityBase):
    id: int
    scan_id: int
    is_false_positive: bool = False
    is_resolved: bool = False
    resolution_notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class VulnerabilityInDB(Vulnerability):
    pass


# Response Models
class ScanSummary(BaseModel):
    total_scans: int
    pending_scans: int
    running_scans: int
    completed_scans: int
    failed_scans: int


class VulnerabilitySummary(BaseModel):
    total_vulnerabilities: int
    critical_count: int
    high_count: int
    medium_count: int
    low_count: int
    by_category: Dict[str, int]