from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from pydantic import ValidationError
import structlog

from app.core.config import settings
from app.core.logging import configure_logging
from app.core.middleware import (
    request_logging_middleware,
    security_headers_middleware,
    rate_limit_middleware,
    error_handling_middleware
)
from app.core.exceptions import (
    MostlySecureException,
    mostlysecure_exception_handler,
    http_exception_handler,
    validation_exception_handler,
    general_exception_handler
)
from app.api.v1.router import api_router

configure_logging()
logger = structlog.get_logger()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("MostlySecure Security Scanner starting up...", version="1.0.0")
    yield
    # Shutdown
    logger.info("MostlySecure Security Scanner shutting down...")

def create_application() -> FastAPI:
    application = FastAPI(
        title="MostlySecure Security Scanner",
        description="""
        # MostlySecure Security Scanner API

        **Mostly Secure, Completely Confident** - Advanced Security Scanning Platform

        ## Features

        - 🔐 **JWT Authentication** - Secure user registration and login
        - 🔍 **5 Advanced Scanners**:
          - API Endpoint Scanner (37 test patterns)
          - Secrets Scanner (37 secret patterns)
          - Database Security Checker (RLS, SQLi, privilege escalation)
          - Firebase Security Auditor (7 Firebase patterns)
          - Webhook Security Validator (SSL, auth, input validation)
        - 📊 **Comprehensive Reporting** - CVSS scoring and remediation advice
        - 🚀 **High Performance** - Async processing with Celery workers
        - 🛡️ **Production Ready** - Rate limiting, security headers, monitoring

        ## Getting Started

        1. Register a new user account
        2. Login to get your JWT token
        3. Use the token to authenticate API requests
        4. Start scanning your applications for vulnerabilities

        ## Authentication

        Most endpoints require JWT authentication. Include your token in the Authorization header:
        ```
        Authorization: Bearer <your-jwt-token>
        ```
        """,
        version="1.0.0",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        lifespan=lifespan,
        contact={
            "name": "MostlySecure Security Team",
            "url": "https://github.com/mostlysecure/security-scanner",
            "email": "<EMAIL>",
        },
        license_info={
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT",
        },
        tags_metadata=[
            {
                "name": "Authentication",
                "description": "User registration, login, and token management",
            },
            {
                "name": "Users",
                "description": "User profile and account management",
            },
            {
                "name": "Scans",
                "description": "Security scanning operations and results",
            },
            {
                "name": "Health",
                "description": "System health and monitoring endpoints",
            },
        ],
    )

    # Add middleware (order matters - first added is executed last)
    application.middleware("http")(error_handling_middleware)
    application.middleware("http")(request_logging_middleware)
    application.middleware("http")(security_headers_middleware)
    application.middleware("http")(rate_limit_middleware)

    application.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_HOSTS,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )
    
    application.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS
    )

    # Add exception handlers
    application.add_exception_handler(MostlySecureException, mostlysecure_exception_handler)
    application.add_exception_handler(HTTPException, http_exception_handler)
    application.add_exception_handler(ValidationError, validation_exception_handler)
    application.add_exception_handler(Exception, general_exception_handler)

    # Include routers
    application.include_router(api_router, prefix="/api/v1")

    return application

app = create_application()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_config=None
    )