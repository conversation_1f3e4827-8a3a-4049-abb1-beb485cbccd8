# MostlySecure Migration to Supabase & Vercel

## Overview
This document outlines the migration from the current architecture (PostgreSQL, Redis, Celery, Docker) to a modern serverless architecture using Supabase and Vercel.

## Current Architecture Analysis

### Backend Stack
- **API**: FastAPI with asyncpg for PostgreSQL
- **Database**: PostgreSQL 15
- **Cache/Queue**: Redis for caching and Celery message broker
- **Background Jobs**: Celery workers with Celery Beat for scheduled tasks
- **Authentication**: JWT tokens with custom implementation
- **Deployment**: Docker Compose with Nginx reverse proxy

### Frontend Stack
- **Framework**: Next.js 15.3.3 with App Router
- **UI**: shadcn/ui components with Tailwind CSS
- **State**: Zustand for client state, React Query for server state
- **Auth**: Custom JWT implementation

## Target Architecture

### Backend on Vercel & Supabase
- **Database**: Supabase PostgreSQL with Row Level Security (RLS)
- **Auth**: <PERSON><PERSON><PERSON> Auth (replaces custom JWT)
- **API**: Next.js API Routes + Vercel Serverless Functions
- **Background Jobs**: Supabase Edge Functions + Vercel Cron
- **Realtime**: Supabase Realtime (replaces WebSocket)
- **Storage**: Supabase Storage for scan reports

### Frontend on Vercel
- **Deployment**: Vercel with automatic deployments
- **Auth**: Supabase Auth SDK
- **API**: Next.js API routes as BFF (Backend for Frontend)

## Migration Plan

### Phase 1: Environment Setup & Database Migration
1. Set up Supabase project
2. Migrate database schema
3. Set up Row Level Security (RLS)
4. Migrate existing data

### Phase 2: Authentication Migration
1. Implement Supabase Auth
2. Migrate user accounts
3. Update frontend auth flow
4. Implement auth middleware

### Phase 3: API Migration
1. Convert FastAPI endpoints to Next.js API routes
2. Implement Vercel Serverless Functions for heavy operations
3. Set up API rate limiting with Vercel Edge Config

### Phase 4: Background Jobs Migration
1. Convert Celery tasks to Supabase Edge Functions
2. Implement scan orchestration with Edge Functions
3. Set up Vercel Cron for scheduled tasks

### Phase 5: Frontend Integration
1. Update API client to use Next.js routes
2. Integrate Supabase client SDK
3. Implement realtime features with Supabase

### Phase 6: Testing & Deployment
1. Comprehensive testing
2. Performance optimization
3. Production deployment

## Detailed Migration Steps

### 1. Database Schema Migration

```sql
-- Users table (managed by Supabase Auth)
-- Custom user metadata stored in auth.users raw_user_meta_data

-- Scans table with RLS
CREATE TABLE scans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  scan_name TEXT NOT NULL,
  scan_description TEXT,
  target_url TEXT NOT NULL,
  scan_type TEXT NOT NULL,
  status TEXT DEFAULT 'pending',
  progress INTEGER DEFAULT 0,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE scans ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own scans" ON scans
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own scans" ON scans
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own scans" ON scans
  FOR UPDATE USING (auth.uid() = user_id);

-- Vulnerabilities table
CREATE TABLE vulnerabilities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scan_id UUID REFERENCES scans(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  severity TEXT NOT NULL,
  cvss_score DECIMAL(3,1),
  owasp_category TEXT,
  cwe_id TEXT,
  evidence JSONB,
  recommendation TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Notifications table
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT NOT NULL,
  read BOOLEAN DEFAULT FALSE,
  data JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 2. Authentication Implementation

```typescript
// lib/supabase/client.ts
import { createBrowserClient } from '@supabase/ssr'

export function createClient() {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

// lib/supabase/server.ts
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function createClient() {
  const cookieStore = await cookies()
  
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => 
            cookieStore.set(name, value, options)
          )
        },
      },
    }
  )
}
```

### 3. API Routes Structure

```
app/
├── api/
│   ├── auth/
│   │   ├── login/route.ts
│   │   ├── logout/route.ts
│   │   └── register/route.ts
│   ├── scans/
│   │   ├── route.ts              # GET (list), POST (create)
│   │   ├── [id]/
│   │   │   ├── route.ts          # GET, PUT, DELETE
│   │   │   ├── vulnerabilities/route.ts
│   │   │   └── status/route.ts
│   ├── scanners/
│   │   ├── api-endpoints/route.ts
│   │   ├── database/route.ts
│   │   ├── secrets/route.ts
│   │   ├── firebase/route.ts
│   │   └── webhooks/route.ts
│   └── notifications/route.ts
```

### 4. Scanner Migration to Edge Functions

```typescript
// supabase/functions/scan-orchestrator/index.ts
import { serve } from 'https://deno.land/std@0.208.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  const { scanId, scanType, targetUrl } = await req.json()
  
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
  )

  // Update scan status
  await supabase
    .from('scans')
    .update({ status: 'running', started_at: new Date() })
    .eq('id', scanId)

  // Execute scanner based on type
  const scannerMap = {
    'api_endpoints': 'scan-api-endpoints',
    'database_security': 'scan-database',
    'secrets': 'scan-secrets',
    'firebase': 'scan-firebase',
    'webhooks': 'scan-webhooks'
  }

  const functionName = scannerMap[scanType]
  
  // Invoke specific scanner function
  const { data, error } = await supabase.functions.invoke(functionName, {
    body: { scanId, targetUrl }
  })

  // Update scan completion
  await supabase
    .from('scans')
    .update({ 
      status: error ? 'failed' : 'completed',
      completed_at: new Date(),
      progress: 100
    })
    .eq('id', scanId)

  return new Response(JSON.stringify({ success: !error }))
})
```

### 5. Vercel Cron Configuration

```typescript
// app/api/cron/cleanup/route.ts
import { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  // Verify cron secret
  const authHeader = request.headers.get('authorization')
  if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
    return new Response('Unauthorized', { status: 401 })
  }

  const supabase = await createClient()
  
  // Clean up old scans
  const thirtyDaysAgo = new Date()
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
  
  await supabase
    .from('scans')
    .delete()
    .lt('created_at', thirtyDaysAgo.toISOString())
    .eq('status', 'completed')

  return Response.json({ success: true })
}

// vercel.json
{
  "crons": [{
    "path": "/api/cron/cleanup",
    "schedule": "0 2 * * *"
  }]
}
```

### 6. Environment Variables

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Vercel
VERCEL_URL=
CRON_SECRET=your-cron-secret

# External Services (optional)
VIRUSTOTAL_API_KEY=
SHODAN_API_KEY=
```

## Data Migration Script

```typescript
// scripts/migrate-data.ts
import { createClient as createSupabaseClient } from '@supabase/supabase-js'
import pg from 'pg'

async function migrateData() {
  // Connect to old PostgreSQL
  const oldDb = new pg.Client({
    connectionString: process.env.OLD_DATABASE_URL
  })
  await oldDb.connect()

  // Connect to Supabase
  const supabase = createSupabaseClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  )

  // Migrate users
  const users = await oldDb.query('SELECT * FROM users')
  for (const user of users.rows) {
    const { data: authUser, error } = await supabase.auth.admin.createUser({
      email: user.email,
      password: generateTempPassword(),
      email_confirm: true,
      user_metadata: {
        username: user.username,
        full_name: user.full_name,
        phone: user.phone,
        location: user.location
      }
    })

    if (!error && authUser) {
      // Map old user_id to new auth.uid
      userIdMap[user.id] = authUser.user.id
    }
  }

  // Migrate scans
  const scans = await oldDb.query('SELECT * FROM scans')
  const scanRecords = scans.rows.map(scan => ({
    user_id: userIdMap[scan.user_id],
    scan_name: scan.scan_name,
    scan_description: scan.scan_description,
    target_url: scan.target_url,
    scan_type: scan.scan_type,
    status: scan.status,
    created_at: scan.created_at
  }))

  await supabase.from('scans').insert(scanRecords)

  await oldDb.end()
}
```

## Testing Strategy

### Unit Tests
- Test all API routes
- Test Edge Functions locally
- Test authentication flows

### Integration Tests
- Test scanner integrations
- Test real-time updates
- Test cron jobs

### Performance Tests
- Load test API routes
- Test Edge Function cold starts
- Monitor database query performance

## Rollback Plan

1. Keep old infrastructure running in parallel
2. Use feature flags for gradual rollout
3. Maintain data sync between old and new systems
4. Have database backups ready
5. Document all changes for quick reversion

## Timeline

- **Week 1-2**: Environment setup, database migration
- **Week 3-4**: Auth migration, API conversion
- **Week 5-6**: Background jobs, scanner migration
- **Week 7**: Frontend integration
- **Week 8**: Testing and optimization
- **Week 9-10**: Gradual rollout and monitoring

## Cost Comparison

### Current (Monthly)
- VPS/Cloud hosting: ~$200
- Database: ~$50
- Redis: ~$30
- **Total**: ~$280/month

### New (Monthly)
- Supabase Free tier: $0 (or Pro at $25)
- Vercel Pro: $20
- **Total**: ~$20-45/month

## Benefits of Migration

1. **Reduced Complexity**: No need to manage Docker, Redis, Celery
2. **Better Scalability**: Automatic scaling with serverless
3. **Lower Costs**: 85% cost reduction
4. **Improved DX**: Better local development experience
5. **Built-in Features**: Auth, realtime, storage out of the box
6. **Global Performance**: Edge deployment with Vercel
7. **Zero DevOps**: No server management needed
